#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建和验证数据库表
"""

import logging
from db.database import engine, Base, get_database_info
from db.models import BuildRecord, BuildAnalysis
from sqlalchemy import inspect, text

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_database_connection():
    """检查数据库连接"""
    try:
        logger.info("🔗 检查数据库连接...")
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1")).fetchone()
            if result:
                logger.info("✅ 数据库连接正常")
                return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def create_tables():
    """创建数据库表"""
    try:
        logger.info("📋 开始创建数据库表...")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ 数据库表创建完成")
        return True
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        return False

def verify_tables():
    """验证表是否存在"""
    try:
        logger.info("🔍 验证数据库表...")
        
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        logger.info(f"📊 现有表: {existing_tables}")
        
        # 检查必需的表
        required_tables = ['build_records', 'build_analysis']
        missing_tables = []
        
        for table in required_tables:
            if table in existing_tables:
                logger.info(f"✅ 表 '{table}' 存在")
                
                # 检查表结构
                columns = inspector.get_columns(table)
                column_names = [col['name'] for col in columns]
                logger.info(f"   列: {column_names}")
            else:
                logger.error(f"❌ 表 '{table}' 不存在")
                missing_tables.append(table)
        
        if missing_tables:
            logger.error(f"❌ 缺少表: {missing_tables}")
            return False
        else:
            logger.info("✅ 所有必需的表都存在")
            return True
            
    except Exception as e:
        logger.error(f"❌ 验证表失败: {e}")
        return False

def drop_tables():
    """删除所有表（谨慎使用）"""
    try:
        logger.warning("⚠️ 开始删除数据库表...")
        Base.metadata.drop_all(bind=engine)
        logger.warning("⚠️ 数据库表删除完成")
        return True
    except Exception as e:
        logger.error(f"❌ 删除数据库表失败: {e}")
        return False

def reset_database():
    """重置数据库（删除并重新创建表）"""
    logger.warning("🔄 开始重置数据库...")
    
    if drop_tables():
        logger.info("等待2秒...")
        import time
        time.sleep(2)
        
        if create_tables():
            if verify_tables():
                logger.info("✅ 数据库重置成功")
                return True
    
    logger.error("❌ 数据库重置失败")
    return False

def show_database_info():
    """显示数据库信息"""
    try:
        info = get_database_info()
        logger.info("📊 数据库信息:")
        logger.info(f"   URL: {info['database_url']}")
        logger.info(f"   引擎: {info['engine']}")
        logger.info(f"   方言: {info['dialect']}")
    except Exception as e:
        logger.error(f"❌ 获取数据库信息失败: {e}")

def main():
    """主函数"""
    print("🚀 数据库初始化脚本")
    print("=" * 50)
    
    # 显示数据库信息
    show_database_info()
    print()
    
    # 检查数据库连接
    if not check_database_connection():
        logger.error("❌ 数据库连接失败，无法继续")
        return
    
    print()
    
    # 创建表
    if create_tables():
        print()
        # 验证表
        if verify_tables():
            logger.info("🎉 数据库初始化成功!")
        else:
            logger.error("❌ 表验证失败")
    else:
        logger.error("❌ 表创建失败")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "reset":
            print("⚠️ 警告: 这将删除所有数据!")
            confirm = input("确认重置数据库? (输入 'yes' 确认): ")
            if confirm.lower() == 'yes':
                reset_database()
            else:
                print("❌ 操作已取消")
        elif command == "verify":
            show_database_info()
            check_database_connection()
            verify_tables()
        elif command == "info":
            show_database_info()
        else:
            print("可用命令:")
            print("  python init_db.py        - 初始化数据库")
            print("  python init_db.py reset  - 重置数据库")
            print("  python init_db.py verify - 验证数据库")
            print("  python init_db.py info   - 显示数据库信息")
    else:
        main() 