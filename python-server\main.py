from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import time
import logging

from db.database import engine, Base
from route import build_routes, analysis_routes, webhook_routes

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 创建 FastAPI 应用
app = FastAPI(
    title="Build Analysis API",
    description="构建记录和AI分析结果管理API",
    version="1.0.0"
)

# CORS 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # 前端地址
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(build_routes.router)
app.include_router(analysis_routes.router)
app.include_router(webhook_routes.router)

@app.get("/")
async def root():
    return {"message": "Build Analysis API is running", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": int(time.time() * 1000),
        "service": "build-analysis-api"
    }

@app.get("/db/status")
async def db_status():
    """数据库状态检查接口"""
    try:
        from sqlalchemy import inspect, text
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        # 测试数据库连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1")).fetchone()
        
        return {
            "status": "connected",
            "tables": tables,
            "connection_test": "success" if result else "failed"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    import signal
    import sys
    
    def signal_handler(sig, frame):
        """信号处理器"""
        logger.info("📡 接收到停止信号，正在关闭服务器...")
        
        # 关闭数据库连接
        try:
            engine.dispose()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.error(f"❌ 关闭数据库连接时出错: {e}")
        
        logger.info("👋 服务器已停止")
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("🚀 启动服务器...")
    logger.info("💡 使用 Ctrl+C 停止服务器")
    
    try:
        uvicorn.run(
            "main:app", 
            host="127.0.0.1", 
            port=3001, 
            reload=True,
            log_level="info",
            access_log=False,  # 减少日志输出
            use_colors=True
        )
    except KeyboardInterrupt:
        logger.info("📡 接收到键盘中断信号")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        sys.exit(1) 