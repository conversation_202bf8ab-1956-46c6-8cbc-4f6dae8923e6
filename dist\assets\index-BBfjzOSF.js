(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))l(a);new MutationObserver(a=>{for(const c of a)if(c.type==="childList")for(const f of c.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function i(a){const c={};return a.integrity&&(c.integrity=a.integrity),a.referrerPolicy&&(c.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?c.credentials="include":a.crossOrigin==="anonymous"?c.credentials="omit":c.credentials="same-origin",c}function l(a){if(a.ep)return;a.ep=!0;const c=i(a);fetch(a.href,c)}})();function ph(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Ua={exports:{}},di={},Ha={exports:{}},Fe={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wf;function rm(){if(wf)return Fe;wf=1;var t=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),f=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),S=Symbol.iterator;function y(_){return _===null||typeof _!="object"?null:(_=S&&_[S]||_["@@iterator"],typeof _=="function"?_:null)}var x={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,P={};function T(_,$,he){this.props=_,this.context=$,this.refs=P,this.updater=he||x}T.prototype.isReactComponent={},T.prototype.setState=function(_,$){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,$,"setState")},T.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function F(){}F.prototype=T.prototype;function B(_,$,he){this.props=_,this.context=$,this.refs=P,this.updater=he||x}var A=B.prototype=new F;A.constructor=B,C(A,T.prototype),A.isPureReactComponent=!0;var K=Array.isArray,Z=Object.prototype.hasOwnProperty,j={current:null},ie={key:!0,ref:!0,__self:!0,__source:!0};function se(_,$,he){var we,Le={},Me=null,Ve=null;if($!=null)for(we in $.ref!==void 0&&(Ve=$.ref),$.key!==void 0&&(Me=""+$.key),$)Z.call($,we)&&!ie.hasOwnProperty(we)&&(Le[we]=$[we]);var $e=arguments.length-2;if($e===1)Le.children=he;else if(1<$e){for(var Be=Array($e),Ae=0;Ae<$e;Ae++)Be[Ae]=arguments[Ae+2];Le.children=Be}if(_&&_.defaultProps)for(we in $e=_.defaultProps,$e)Le[we]===void 0&&(Le[we]=$e[we]);return{$$typeof:t,type:_,key:Me,ref:Ve,props:Le,_owner:j.current}}function ue(_,$){return{$$typeof:t,type:_.type,key:$,ref:_.ref,props:_.props,_owner:_._owner}}function Ce(_){return typeof _=="object"&&_!==null&&_.$$typeof===t}function Ie(_){var $={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(he){return $[he]})}var xe=/\/+/g;function Re(_,$){return typeof _=="object"&&_!==null&&_.key!=null?Ie(""+_.key):$.toString(36)}function Pe(_,$,he,we,Le){var Me=typeof _;(Me==="undefined"||Me==="boolean")&&(_=null);var Ve=!1;if(_===null)Ve=!0;else switch(Me){case"string":case"number":Ve=!0;break;case"object":switch(_.$$typeof){case t:case r:Ve=!0}}if(Ve)return Ve=_,Le=Le(Ve),_=we===""?"."+Re(Ve,0):we,K(Le)?(he="",_!=null&&(he=_.replace(xe,"$&/")+"/"),Pe(Le,$,he,"",function(Ae){return Ae})):Le!=null&&(Ce(Le)&&(Le=ue(Le,he+(!Le.key||Ve&&Ve.key===Le.key?"":(""+Le.key).replace(xe,"$&/")+"/")+_)),$.push(Le)),1;if(Ve=0,we=we===""?".":we+":",K(_))for(var $e=0;$e<_.length;$e++){Me=_[$e];var Be=we+Re(Me,$e);Ve+=Pe(Me,$,he,Be,Le)}else if(Be=y(_),typeof Be=="function")for(_=Be.call(_),$e=0;!(Me=_.next()).done;)Me=Me.value,Be=we+Re(Me,$e++),Ve+=Pe(Me,$,he,Be,Le);else if(Me==="object")throw $=String(_),Error("Objects are not valid as a React child (found: "+($==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":$)+"). If you meant to render a collection of children, use an array instead.");return Ve}function He(_,$,he){if(_==null)return _;var we=[],Le=0;return Pe(_,we,"","",function(Me){return $.call(he,Me,Le++)}),we}function pe(_){if(_._status===-1){var $=_._result;$=$(),$.then(function(he){(_._status===0||_._status===-1)&&(_._status=1,_._result=he)},function(he){(_._status===0||_._status===-1)&&(_._status=2,_._result=he)}),_._status===-1&&(_._status=0,_._result=$)}if(_._status===1)return _._result.default;throw _._result}var _e={current:null},G={transition:null},J={ReactCurrentDispatcher:_e,ReactCurrentBatchConfig:G,ReactCurrentOwner:j};function W(){throw Error("act(...) is not supported in production builds of React.")}return Fe.Children={map:He,forEach:function(_,$,he){He(_,function(){$.apply(this,arguments)},he)},count:function(_){var $=0;return He(_,function(){$++}),$},toArray:function(_){return He(_,function($){return $})||[]},only:function(_){if(!Ce(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},Fe.Component=T,Fe.Fragment=i,Fe.Profiler=a,Fe.PureComponent=B,Fe.StrictMode=l,Fe.Suspense=p,Fe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=J,Fe.act=W,Fe.cloneElement=function(_,$,he){if(_==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+_+".");var we=C({},_.props),Le=_.key,Me=_.ref,Ve=_._owner;if($!=null){if($.ref!==void 0&&(Me=$.ref,Ve=j.current),$.key!==void 0&&(Le=""+$.key),_.type&&_.type.defaultProps)var $e=_.type.defaultProps;for(Be in $)Z.call($,Be)&&!ie.hasOwnProperty(Be)&&(we[Be]=$[Be]===void 0&&$e!==void 0?$e[Be]:$[Be])}var Be=arguments.length-2;if(Be===1)we.children=he;else if(1<Be){$e=Array(Be);for(var Ae=0;Ae<Be;Ae++)$e[Ae]=arguments[Ae+2];we.children=$e}return{$$typeof:t,type:_.type,key:Le,ref:Me,props:we,_owner:Ve}},Fe.createContext=function(_){return _={$$typeof:f,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},_.Provider={$$typeof:c,_context:_},_.Consumer=_},Fe.createElement=se,Fe.createFactory=function(_){var $=se.bind(null,_);return $.type=_,$},Fe.createRef=function(){return{current:null}},Fe.forwardRef=function(_){return{$$typeof:h,render:_}},Fe.isValidElement=Ce,Fe.lazy=function(_){return{$$typeof:v,_payload:{_status:-1,_result:_},_init:pe}},Fe.memo=function(_,$){return{$$typeof:m,type:_,compare:$===void 0?null:$}},Fe.startTransition=function(_){var $=G.transition;G.transition={};try{_()}finally{G.transition=$}},Fe.unstable_act=W,Fe.useCallback=function(_,$){return _e.current.useCallback(_,$)},Fe.useContext=function(_){return _e.current.useContext(_)},Fe.useDebugValue=function(){},Fe.useDeferredValue=function(_){return _e.current.useDeferredValue(_)},Fe.useEffect=function(_,$){return _e.current.useEffect(_,$)},Fe.useId=function(){return _e.current.useId()},Fe.useImperativeHandle=function(_,$,he){return _e.current.useImperativeHandle(_,$,he)},Fe.useInsertionEffect=function(_,$){return _e.current.useInsertionEffect(_,$)},Fe.useLayoutEffect=function(_,$){return _e.current.useLayoutEffect(_,$)},Fe.useMemo=function(_,$){return _e.current.useMemo(_,$)},Fe.useReducer=function(_,$,he){return _e.current.useReducer(_,$,he)},Fe.useRef=function(_){return _e.current.useRef(_)},Fe.useState=function(_){return _e.current.useState(_)},Fe.useSyncExternalStore=function(_,$,he){return _e.current.useSyncExternalStore(_,$,he)},Fe.useTransition=function(){return _e.current.useTransition()},Fe.version="18.3.1",Fe}var Sf;function Pu(){return Sf||(Sf=1,Ha.exports=rm()),Ha.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xf;function om(){if(xf)return di;xf=1;var t=Pu(),r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function f(h,p,m){var v,S={},y=null,x=null;m!==void 0&&(y=""+m),p.key!==void 0&&(y=""+p.key),p.ref!==void 0&&(x=p.ref);for(v in p)l.call(p,v)&&!c.hasOwnProperty(v)&&(S[v]=p[v]);if(h&&h.defaultProps)for(v in p=h.defaultProps,p)S[v]===void 0&&(S[v]=p[v]);return{$$typeof:r,type:h,key:y,ref:x,props:S,_owner:a.current}}return di.Fragment=i,di.jsx=f,di.jsxs=f,di}var Cf;function im(){return Cf||(Cf=1,Ua.exports=om()),Ua.exports}var M=im(),L=Pu();const lm=ph(L);var Vl={},Ba={exports:{}},Dt={},Ga={exports:{}},Qa={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rf;function sm(){return Rf||(Rf=1,function(t){function r(G,J){var W=G.length;G.push(J);e:for(;0<W;){var _=W-1>>>1,$=G[_];if(0<a($,J))G[_]=J,G[W]=$,W=_;else break e}}function i(G){return G.length===0?null:G[0]}function l(G){if(G.length===0)return null;var J=G[0],W=G.pop();if(W!==J){G[0]=W;e:for(var _=0,$=G.length,he=$>>>1;_<he;){var we=2*(_+1)-1,Le=G[we],Me=we+1,Ve=G[Me];if(0>a(Le,W))Me<$&&0>a(Ve,Le)?(G[_]=Ve,G[Me]=W,_=Me):(G[_]=Le,G[we]=W,_=we);else if(Me<$&&0>a(Ve,W))G[_]=Ve,G[Me]=W,_=Me;else break e}}return J}function a(G,J){var W=G.sortIndex-J.sortIndex;return W!==0?W:G.id-J.id}if(typeof performance=="object"&&typeof performance.now=="function"){var c=performance;t.unstable_now=function(){return c.now()}}else{var f=Date,h=f.now();t.unstable_now=function(){return f.now()-h}}var p=[],m=[],v=1,S=null,y=3,x=!1,C=!1,P=!1,T=typeof setTimeout=="function"?setTimeout:null,F=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function A(G){for(var J=i(m);J!==null;){if(J.callback===null)l(m);else if(J.startTime<=G)l(m),J.sortIndex=J.expirationTime,r(p,J);else break;J=i(m)}}function K(G){if(P=!1,A(G),!C)if(i(p)!==null)C=!0,pe(Z);else{var J=i(m);J!==null&&_e(K,J.startTime-G)}}function Z(G,J){C=!1,P&&(P=!1,F(se),se=-1),x=!0;var W=y;try{for(A(J),S=i(p);S!==null&&(!(S.expirationTime>J)||G&&!Ie());){var _=S.callback;if(typeof _=="function"){S.callback=null,y=S.priorityLevel;var $=_(S.expirationTime<=J);J=t.unstable_now(),typeof $=="function"?S.callback=$:S===i(p)&&l(p),A(J)}else l(p);S=i(p)}if(S!==null)var he=!0;else{var we=i(m);we!==null&&_e(K,we.startTime-J),he=!1}return he}finally{S=null,y=W,x=!1}}var j=!1,ie=null,se=-1,ue=5,Ce=-1;function Ie(){return!(t.unstable_now()-Ce<ue)}function xe(){if(ie!==null){var G=t.unstable_now();Ce=G;var J=!0;try{J=ie(!0,G)}finally{J?Re():(j=!1,ie=null)}}else j=!1}var Re;if(typeof B=="function")Re=function(){B(xe)};else if(typeof MessageChannel<"u"){var Pe=new MessageChannel,He=Pe.port2;Pe.port1.onmessage=xe,Re=function(){He.postMessage(null)}}else Re=function(){T(xe,0)};function pe(G){ie=G,j||(j=!0,Re())}function _e(G,J){se=T(function(){G(t.unstable_now())},J)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(G){G.callback=null},t.unstable_continueExecution=function(){C||x||(C=!0,pe(Z))},t.unstable_forceFrameRate=function(G){0>G||125<G?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ue=0<G?Math.floor(1e3/G):5},t.unstable_getCurrentPriorityLevel=function(){return y},t.unstable_getFirstCallbackNode=function(){return i(p)},t.unstable_next=function(G){switch(y){case 1:case 2:case 3:var J=3;break;default:J=y}var W=y;y=J;try{return G()}finally{y=W}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(G,J){switch(G){case 1:case 2:case 3:case 4:case 5:break;default:G=3}var W=y;y=G;try{return J()}finally{y=W}},t.unstable_scheduleCallback=function(G,J,W){var _=t.unstable_now();switch(typeof W=="object"&&W!==null?(W=W.delay,W=typeof W=="number"&&0<W?_+W:_):W=_,G){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=W+$,G={id:v++,callback:J,priorityLevel:G,startTime:W,expirationTime:$,sortIndex:-1},W>_?(G.sortIndex=W,r(m,G),i(p)===null&&G===i(m)&&(P?(F(se),se=-1):P=!0,_e(K,W-_))):(G.sortIndex=$,r(p,G),C||x||(C=!0,pe(Z))),G},t.unstable_shouldYield=Ie,t.unstable_wrapCallback=function(G){var J=y;return function(){var W=y;y=J;try{return G.apply(this,arguments)}finally{y=W}}}}(Qa)),Qa}var Ef;function am(){return Ef||(Ef=1,Ga.exports=sm()),Ga.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pf;function um(){if(Pf)return Dt;Pf=1;var t=Pu(),r=am();function i(e){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+e,o=1;o<arguments.length;o++)n+="&args[]="+encodeURIComponent(arguments[o]);return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,a={};function c(e,n){f(e,n),f(e+"Capture",n)}function f(e,n){for(a[e]=n,e=0;e<n.length;e++)l.add(n[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,m=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,v={},S={};function y(e){return p.call(S,e)?!0:p.call(v,e)?!1:m.test(e)?S[e]=!0:(v[e]=!0,!1)}function x(e,n,o,s){if(o!==null&&o.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return s?!1:o!==null?!o.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function C(e,n,o,s){if(n===null||typeof n>"u"||x(e,n,o,s))return!0;if(s)return!1;if(o!==null)switch(o.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function P(e,n,o,s,u,d,g){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=s,this.attributeNamespace=u,this.mustUseProperty=o,this.propertyName=e,this.type=n,this.sanitizeURL=d,this.removeEmptyString=g}var T={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){T[e]=new P(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var n=e[0];T[n]=new P(n,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){T[e]=new P(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){T[e]=new P(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){T[e]=new P(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){T[e]=new P(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){T[e]=new P(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){T[e]=new P(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){T[e]=new P(e,5,!1,e.toLowerCase(),null,!1,!1)});var F=/[\-:]([a-z])/g;function B(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var n=e.replace(F,B);T[n]=new P(n,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var n=e.replace(F,B);T[n]=new P(n,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var n=e.replace(F,B);T[n]=new P(n,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){T[e]=new P(e,1,!1,e.toLowerCase(),null,!1,!1)}),T.xlinkHref=new P("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){T[e]=new P(e,1,!1,e.toLowerCase(),null,!0,!0)});function A(e,n,o,s){var u=T.hasOwnProperty(n)?T[n]:null;(u!==null?u.type!==0:s||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(C(n,o,u,s)&&(o=null),s||u===null?y(n)&&(o===null?e.removeAttribute(n):e.setAttribute(n,""+o)):u.mustUseProperty?e[u.propertyName]=o===null?u.type===3?!1:"":o:(n=u.attributeName,s=u.attributeNamespace,o===null?e.removeAttribute(n):(u=u.type,o=u===3||u===4&&o===!0?"":""+o,s?e.setAttributeNS(s,n,o):e.setAttribute(n,o))))}var K=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Z=Symbol.for("react.element"),j=Symbol.for("react.portal"),ie=Symbol.for("react.fragment"),se=Symbol.for("react.strict_mode"),ue=Symbol.for("react.profiler"),Ce=Symbol.for("react.provider"),Ie=Symbol.for("react.context"),xe=Symbol.for("react.forward_ref"),Re=Symbol.for("react.suspense"),Pe=Symbol.for("react.suspense_list"),He=Symbol.for("react.memo"),pe=Symbol.for("react.lazy"),_e=Symbol.for("react.offscreen"),G=Symbol.iterator;function J(e){return e===null||typeof e!="object"?null:(e=G&&e[G]||e["@@iterator"],typeof e=="function"?e:null)}var W=Object.assign,_;function $(e){if(_===void 0)try{throw Error()}catch(o){var n=o.stack.trim().match(/\n( *(at )?)/);_=n&&n[1]||""}return`
`+_+e}var he=!1;function we(e,n){if(!e||he)return"";he=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(O){var s=O}Reflect.construct(e,[],n)}else{try{n.call()}catch(O){s=O}e.call(n.prototype)}else{try{throw Error()}catch(O){s=O}e()}}catch(O){if(O&&s&&typeof O.stack=="string"){for(var u=O.stack.split(`
`),d=s.stack.split(`
`),g=u.length-1,w=d.length-1;1<=g&&0<=w&&u[g]!==d[w];)w--;for(;1<=g&&0<=w;g--,w--)if(u[g]!==d[w]){if(g!==1||w!==1)do if(g--,w--,0>w||u[g]!==d[w]){var E=`
`+u[g].replace(" at new "," at ");return e.displayName&&E.includes("<anonymous>")&&(E=E.replace("<anonymous>",e.displayName)),E}while(1<=g&&0<=w);break}}}finally{he=!1,Error.prepareStackTrace=o}return(e=e?e.displayName||e.name:"")?$(e):""}function Le(e){switch(e.tag){case 5:return $(e.type);case 16:return $("Lazy");case 13:return $("Suspense");case 19:return $("SuspenseList");case 0:case 2:case 15:return e=we(e.type,!1),e;case 11:return e=we(e.type.render,!1),e;case 1:return e=we(e.type,!0),e;default:return""}}function Me(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ie:return"Fragment";case j:return"Portal";case ue:return"Profiler";case se:return"StrictMode";case Re:return"Suspense";case Pe:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ie:return(e.displayName||"Context")+".Consumer";case Ce:return(e._context.displayName||"Context")+".Provider";case xe:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case He:return n=e.displayName||null,n!==null?n:Me(e.type)||"Memo";case pe:n=e._payload,e=e._init;try{return Me(e(n))}catch{}}return null}function Ve(e){var n=e.type;switch(e.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=n.render,e=e.displayName||e.name||"",n.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Me(n);case 8:return n===se?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function $e(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Be(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Ae(e){var n=Be(e)?"checked":"value",o=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),s=""+e[n];if(!e.hasOwnProperty(n)&&typeof o<"u"&&typeof o.get=="function"&&typeof o.set=="function"){var u=o.get,d=o.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return u.call(this)},set:function(g){s=""+g,d.call(this,g)}}),Object.defineProperty(e,n,{enumerable:o.enumerable}),{getValue:function(){return s},setValue:function(g){s=""+g},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function Xt(e){e._valueTracker||(e._valueTracker=Ae(e))}function Eo(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var o=n.getValue(),s="";return e&&(s=Be(e)?e.checked?"true":"false":e.value),e=s,e!==o?(n.setValue(e),!0):!1}function Hr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Zt(e,n){var o=n.checked;return W({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:o??e._wrapperState.initialChecked})}function Ni(e,n){var o=n.defaultValue==null?"":n.defaultValue,s=n.checked!=null?n.checked:n.defaultChecked;o=$e(n.value!=null?n.value:o),e._wrapperState={initialChecked:s,initialValue:o,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function Di(e,n){n=n.checked,n!=null&&A(e,"checked",n,!1)}function Br(e,n){Di(e,n);var o=$e(n.value),s=n.type;if(o!=null)s==="number"?(o===0&&e.value===""||e.value!=o)&&(e.value=""+o):e.value!==""+o&&(e.value=""+o);else if(s==="submit"||s==="reset"){e.removeAttribute("value");return}n.hasOwnProperty("value")?Po(e,n.type,o):n.hasOwnProperty("defaultValue")&&Po(e,n.type,$e(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(e.defaultChecked=!!n.defaultChecked)}function Ti(e,n,o){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var s=n.type;if(!(s!=="submit"&&s!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+e._wrapperState.initialValue,o||n===e.value||(e.value=n),e.defaultValue=n}o=e.name,o!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,o!==""&&(e.name=o)}function Po(e,n,o){(n!=="number"||Hr(e.ownerDocument)!==e)&&(o==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+o&&(e.defaultValue=""+o))}var yr=Array.isArray;function zn(e,n,o,s){if(e=e.options,n){n={};for(var u=0;u<o.length;u++)n["$"+o[u]]=!0;for(o=0;o<e.length;o++)u=n.hasOwnProperty("$"+e[o].value),e[o].selected!==u&&(e[o].selected=u),u&&s&&(e[o].defaultSelected=!0)}else{for(o=""+$e(o),n=null,u=0;u<e.length;u++){if(e[u].value===o){e[u].selected=!0,s&&(e[u].defaultSelected=!0);return}n!==null||e[u].disabled||(n=e[u])}n!==null&&(n.selected=!0)}}function Jt(e,n){if(n.dangerouslySetInnerHTML!=null)throw Error(i(91));return W({},n,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function On(e,n){var o=n.value;if(o==null){if(o=n.children,n=n.defaultValue,o!=null){if(n!=null)throw Error(i(92));if(yr(o)){if(1<o.length)throw Error(i(93));o=o[0]}n=o}n==null&&(n=""),o=n}e._wrapperState={initialValue:$e(o)}}function ko(e,n){var o=$e(n.value),s=$e(n.defaultValue);o!=null&&(o=""+o,o!==e.value&&(e.value=o),n.defaultValue==null&&e.defaultValue!==o&&(e.defaultValue=o)),s!=null&&(e.defaultValue=""+s)}function Gr(e){var n=e.textContent;n===e._wrapperState.initialValue&&n!==""&&n!==null&&(e.value=n)}function en(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zt(e,n){return e==null||e==="http://www.w3.org/1999/xhtml"?en(n):e==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var wr,Qr=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,o,s,u){MSApp.execUnsafeLocalFunction(function(){return e(n,o,s,u)})}:e}(function(e,n){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=n;else{for(wr=wr||document.createElement("div"),wr.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=wr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild)}});function Sr(e,n){if(n){var o=e.firstChild;if(o&&o===e.lastChild&&o.nodeType===3){o.nodeValue=n;return}}e.textContent=n}var Rt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ji=["Webkit","ms","Moz","O"];Object.keys(Rt).forEach(function(e){ji.forEach(function(n){n=n+e.charAt(0).toUpperCase()+e.substring(1),Rt[n]=Rt[e]})});function _o(e,n,o){return n==null||typeof n=="boolean"||n===""?"":o||typeof n!="number"||n===0||Rt.hasOwnProperty(e)&&Rt[e]?(""+n).trim():n+"px"}function Mo(e,n){e=e.style;for(var o in n)if(n.hasOwnProperty(o)){var s=o.indexOf("--")===0,u=_o(o,n[o],s);o==="float"&&(o="cssFloat"),s?e.setProperty(o,u):e[o]=u}}var ss=W({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Wr(e,n){if(n){if(ss[e]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(i(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(i(61))}if(n.style!=null&&typeof n.style!="object")throw Error(i(62))}}function In(e,n){if(e.indexOf("-")===-1)return typeof n.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qr=null;function xr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Fo=null,Rn=null,$n=null;function Lo(e){if(e=Yo(e)){if(typeof Fo!="function")throw Error(i(280));var n=e.stateNode;n&&(n=nl(n),Fo(e.stateNode,e.type,n))}}function Cr(e){Rn?$n?$n.push(e):$n=[e]:Rn=e}function Rr(){if(Rn){var e=Rn,n=$n;if($n=Rn=null,Lo(e),n)for(e=0;e<n.length;e++)Lo(n[e])}}function zi(e,n){return e(n)}function Oi(){}var R=!1;function N(e,n,o){if(R)return e(n,o);R=!0;try{return zi(e,n,o)}finally{R=!1,(Rn!==null||$n!==null)&&(Oi(),Rr())}}function I(e,n){var o=e.stateNode;if(o===null)return null;var s=nl(o);if(s===null)return null;o=s[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(s=!s.disabled)||(e=e.type,s=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!s;break e;default:e=!1}if(e)return null;if(o&&typeof o!="function")throw Error(i(231,n,typeof o));return o}var H=!1;if(h)try{var q={};Object.defineProperty(q,"passive",{get:function(){H=!0}}),window.addEventListener("test",q,q),window.removeEventListener("test",q,q)}catch{H=!1}function ae(e,n,o,s,u,d,g,w,E){var O=Array.prototype.slice.call(arguments,3);try{n.apply(o,O)}catch(V){this.onError(V)}}var ce=!1,X=null,le=!1,ne=null,Se={onError:function(e){ce=!0,X=e}};function ye(e,n,o,s,u,d,g,w,E){ce=!1,X=null,ae.apply(Se,arguments)}function ke(e,n,o,s,u,d,g,w,E){if(ye.apply(this,arguments),ce){if(ce){var O=X;ce=!1,X=null}else throw Error(i(198));le||(le=!0,ne=O)}}function Te(e){var n=e,o=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,(n.flags&4098)!==0&&(o=n.return),e=n.return;while(e)}return n.tag===3?o:null}function Ze(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function ot(e){if(Te(e)!==e)throw Error(i(188))}function wt(e){var n=e.alternate;if(!n){if(n=Te(e),n===null)throw Error(i(188));return n!==e?null:e}for(var o=e,s=n;;){var u=o.return;if(u===null)break;var d=u.alternate;if(d===null){if(s=u.return,s!==null){o=s;continue}break}if(u.child===d.child){for(d=u.child;d;){if(d===o)return ot(u),e;if(d===s)return ot(u),n;d=d.sibling}throw Error(i(188))}if(o.return!==s.return)o=u,s=d;else{for(var g=!1,w=u.child;w;){if(w===o){g=!0,o=u,s=d;break}if(w===s){g=!0,s=u,o=d;break}w=w.sibling}if(!g){for(w=d.child;w;){if(w===o){g=!0,o=d,s=u;break}if(w===s){g=!0,s=d,o=u;break}w=w.sibling}if(!g)throw Error(i(189))}}if(o.alternate!==s)throw Error(i(190))}if(o.tag!==3)throw Error(i(188));return o.stateNode.current===o?e:n}function De(e){return e=wt(e),e!==null?An(e):null}function An(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var n=An(e);if(n!==null)return n;e=e.sibling}return null}var hn=r.unstable_scheduleCallback,bn=r.unstable_cancelCallback,Et=r.unstable_shouldYield,Er=r.unstable_requestPaint,Ue=r.unstable_now,No=r.unstable_getCurrentPriorityLevel,tn=r.unstable_ImmediatePriority,Pr=r.unstable_UserBlockingPriority,je=r.unstable_NormalPriority,Pt=r.unstable_LowPriority,nn=r.unstable_IdlePriority,En=null,kt=null;function Je(e){if(kt&&typeof kt.onCommitFiberRoot=="function")try{kt.onCommitFiberRoot(En,e,void 0,(e.current.flags&128)===128)}catch{}}var st=Math.clz32?Math.clz32:xp,Ii=Math.log,as=Math.LN2;function xp(e){return e>>>=0,e===0?32:31-(Ii(e)/as|0)|0}var $i=64,Ai=4194304;function Do(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function bi(e,n){var o=e.pendingLanes;if(o===0)return 0;var s=0,u=e.suspendedLanes,d=e.pingedLanes,g=o&268435455;if(g!==0){var w=g&~u;w!==0?s=Do(w):(d&=g,d!==0&&(s=Do(d)))}else g=o&~u,g!==0?s=Do(g):d!==0&&(s=Do(d));if(s===0)return 0;if(n!==0&&n!==s&&(n&u)===0&&(u=s&-s,d=n&-n,u>=d||u===16&&(d&4194240)!==0))return n;if((s&4)!==0&&(s|=o&16),n=e.entangledLanes,n!==0)for(e=e.entanglements,n&=s;0<n;)o=31-st(n),u=1<<o,s|=e[o],n&=~u;return s}function Cp(e,n){switch(e){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Rp(e,n){for(var o=e.suspendedLanes,s=e.pingedLanes,u=e.expirationTimes,d=e.pendingLanes;0<d;){var g=31-st(d),w=1<<g,E=u[g];E===-1?((w&o)===0||(w&s)!==0)&&(u[g]=Cp(w,n)):E<=n&&(e.expiredLanes|=w),d&=~w}}function us(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ku(){var e=$i;return $i<<=1,($i&4194240)===0&&($i=64),e}function cs(e){for(var n=[],o=0;31>o;o++)n.push(e);return n}function To(e,n,o){e.pendingLanes|=n,n!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,n=31-st(n),e[n]=o}function Ep(e,n){var o=e.pendingLanes&~n;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=n,e.mutableReadLanes&=n,e.entangledLanes&=n,n=e.entanglements;var s=e.eventTimes;for(e=e.expirationTimes;0<o;){var u=31-st(o),d=1<<u;n[u]=0,s[u]=-1,e[u]=-1,o&=~d}}function ds(e,n){var o=e.entangledLanes|=n;for(e=e.entanglements;o;){var s=31-st(o),u=1<<s;u&n|e[s]&n&&(e[s]|=n),o&=~u}}var be=0;function Yu(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var Xu,fs,Zu,Ju,ec,hs=!1,Vi=[],Vn=null,Un=null,Hn=null,jo=new Map,zo=new Map,Bn=[],Pp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function tc(e,n){switch(e){case"focusin":case"focusout":Vn=null;break;case"dragenter":case"dragleave":Un=null;break;case"mouseover":case"mouseout":Hn=null;break;case"pointerover":case"pointerout":jo.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":zo.delete(n.pointerId)}}function Oo(e,n,o,s,u,d){return e===null||e.nativeEvent!==d?(e={blockedOn:n,domEventName:o,eventSystemFlags:s,nativeEvent:d,targetContainers:[u]},n!==null&&(n=Yo(n),n!==null&&fs(n)),e):(e.eventSystemFlags|=s,n=e.targetContainers,u!==null&&n.indexOf(u)===-1&&n.push(u),e)}function kp(e,n,o,s,u){switch(n){case"focusin":return Vn=Oo(Vn,e,n,o,s,u),!0;case"dragenter":return Un=Oo(Un,e,n,o,s,u),!0;case"mouseover":return Hn=Oo(Hn,e,n,o,s,u),!0;case"pointerover":var d=u.pointerId;return jo.set(d,Oo(jo.get(d)||null,e,n,o,s,u)),!0;case"gotpointercapture":return d=u.pointerId,zo.set(d,Oo(zo.get(d)||null,e,n,o,s,u)),!0}return!1}function nc(e){var n=kr(e.target);if(n!==null){var o=Te(n);if(o!==null){if(n=o.tag,n===13){if(n=Ze(o),n!==null){e.blockedOn=n,ec(e.priority,function(){Zu(o)});return}}else if(n===3&&o.stateNode.current.memoizedState.isDehydrated){e.blockedOn=o.tag===3?o.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ui(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var o=gs(e.domEventName,e.eventSystemFlags,n[0],e.nativeEvent);if(o===null){o=e.nativeEvent;var s=new o.constructor(o.type,o);qr=s,o.target.dispatchEvent(s),qr=null}else return n=Yo(o),n!==null&&fs(n),e.blockedOn=o,!1;n.shift()}return!0}function rc(e,n,o){Ui(e)&&o.delete(n)}function _p(){hs=!1,Vn!==null&&Ui(Vn)&&(Vn=null),Un!==null&&Ui(Un)&&(Un=null),Hn!==null&&Ui(Hn)&&(Hn=null),jo.forEach(rc),zo.forEach(rc)}function Io(e,n){e.blockedOn===n&&(e.blockedOn=null,hs||(hs=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,_p)))}function $o(e){function n(u){return Io(u,e)}if(0<Vi.length){Io(Vi[0],e);for(var o=1;o<Vi.length;o++){var s=Vi[o];s.blockedOn===e&&(s.blockedOn=null)}}for(Vn!==null&&Io(Vn,e),Un!==null&&Io(Un,e),Hn!==null&&Io(Hn,e),jo.forEach(n),zo.forEach(n),o=0;o<Bn.length;o++)s=Bn[o],s.blockedOn===e&&(s.blockedOn=null);for(;0<Bn.length&&(o=Bn[0],o.blockedOn===null);)nc(o),o.blockedOn===null&&Bn.shift()}var Kr=K.ReactCurrentBatchConfig,Hi=!0;function Mp(e,n,o,s){var u=be,d=Kr.transition;Kr.transition=null;try{be=1,ps(e,n,o,s)}finally{be=u,Kr.transition=d}}function Fp(e,n,o,s){var u=be,d=Kr.transition;Kr.transition=null;try{be=4,ps(e,n,o,s)}finally{be=u,Kr.transition=d}}function ps(e,n,o,s){if(Hi){var u=gs(e,n,o,s);if(u===null)Ds(e,n,s,Bi,o),tc(e,s);else if(kp(u,e,n,o,s))s.stopPropagation();else if(tc(e,s),n&4&&-1<Pp.indexOf(e)){for(;u!==null;){var d=Yo(u);if(d!==null&&Xu(d),d=gs(e,n,o,s),d===null&&Ds(e,n,s,Bi,o),d===u)break;u=d}u!==null&&s.stopPropagation()}else Ds(e,n,s,null,o)}}var Bi=null;function gs(e,n,o,s){if(Bi=null,e=xr(s),e=kr(e),e!==null)if(n=Te(e),n===null)e=null;else if(o=n.tag,o===13){if(e=Ze(n),e!==null)return e;e=null}else if(o===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null);return Bi=e,null}function oc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(No()){case tn:return 1;case Pr:return 4;case je:case Pt:return 16;case nn:return 536870912;default:return 16}default:return 16}}var Gn=null,ms=null,Gi=null;function ic(){if(Gi)return Gi;var e,n=ms,o=n.length,s,u="value"in Gn?Gn.value:Gn.textContent,d=u.length;for(e=0;e<o&&n[e]===u[e];e++);var g=o-e;for(s=1;s<=g&&n[o-s]===u[d-s];s++);return Gi=u.slice(e,1<s?1-s:void 0)}function Qi(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function Wi(){return!0}function lc(){return!1}function Ot(e){function n(o,s,u,d,g){this._reactName=o,this._targetInst=u,this.type=s,this.nativeEvent=d,this.target=g,this.currentTarget=null;for(var w in e)e.hasOwnProperty(w)&&(o=e[w],this[w]=o?o(d):d[w]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Wi:lc,this.isPropagationStopped=lc,this}return W(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var o=this.nativeEvent;o&&(o.preventDefault?o.preventDefault():typeof o.returnValue!="unknown"&&(o.returnValue=!1),this.isDefaultPrevented=Wi)},stopPropagation:function(){var o=this.nativeEvent;o&&(o.stopPropagation?o.stopPropagation():typeof o.cancelBubble!="unknown"&&(o.cancelBubble=!0),this.isPropagationStopped=Wi)},persist:function(){},isPersistent:Wi}),n}var Yr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},vs=Ot(Yr),Ao=W({},Yr,{view:0,detail:0}),Lp=Ot(Ao),ys,ws,bo,qi=W({},Ao,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==bo&&(bo&&e.type==="mousemove"?(ys=e.screenX-bo.screenX,ws=e.screenY-bo.screenY):ws=ys=0,bo=e),ys)},movementY:function(e){return"movementY"in e?e.movementY:ws}}),sc=Ot(qi),Np=W({},qi,{dataTransfer:0}),Dp=Ot(Np),Tp=W({},Ao,{relatedTarget:0}),Ss=Ot(Tp),jp=W({},Yr,{animationName:0,elapsedTime:0,pseudoElement:0}),zp=Ot(jp),Op=W({},Yr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Ip=Ot(Op),$p=W({},Yr,{data:0}),ac=Ot($p),Ap={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},bp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Vp={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Up(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=Vp[e])?!!n[e]:!1}function xs(){return Up}var Hp=W({},Ao,{key:function(e){if(e.key){var n=Ap[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=Qi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?bp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xs,charCode:function(e){return e.type==="keypress"?Qi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Qi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Bp=Ot(Hp),Gp=W({},qi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),uc=Ot(Gp),Qp=W({},Ao,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xs}),Wp=Ot(Qp),qp=W({},Yr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Kp=Ot(qp),Yp=W({},qi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Xp=Ot(Yp),Zp=[9,13,27,32],Cs=h&&"CompositionEvent"in window,Vo=null;h&&"documentMode"in document&&(Vo=document.documentMode);var Jp=h&&"TextEvent"in window&&!Vo,cc=h&&(!Cs||Vo&&8<Vo&&11>=Vo),dc=" ",fc=!1;function hc(e,n){switch(e){case"keyup":return Zp.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function pc(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Xr=!1;function eg(e,n){switch(e){case"compositionend":return pc(n);case"keypress":return n.which!==32?null:(fc=!0,dc);case"textInput":return e=n.data,e===dc&&fc?null:e;default:return null}}function tg(e,n){if(Xr)return e==="compositionend"||!Cs&&hc(e,n)?(e=ic(),Gi=ms=Gn=null,Xr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return cc&&n.locale!=="ko"?null:n.data;default:return null}}var ng={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gc(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!ng[e.type]:n==="textarea"}function mc(e,n,o,s){Cr(s),n=Ji(n,"onChange"),0<n.length&&(o=new vs("onChange","change",null,o,s),e.push({event:o,listeners:n}))}var Uo=null,Ho=null;function rg(e){jc(e,0)}function Ki(e){var n=no(e);if(Eo(n))return e}function og(e,n){if(e==="change")return n}var vc=!1;if(h){var Rs;if(h){var Es="oninput"in document;if(!Es){var yc=document.createElement("div");yc.setAttribute("oninput","return;"),Es=typeof yc.oninput=="function"}Rs=Es}else Rs=!1;vc=Rs&&(!document.documentMode||9<document.documentMode)}function wc(){Uo&&(Uo.detachEvent("onpropertychange",Sc),Ho=Uo=null)}function Sc(e){if(e.propertyName==="value"&&Ki(Ho)){var n=[];mc(n,Ho,e,xr(e)),N(rg,n)}}function ig(e,n,o){e==="focusin"?(wc(),Uo=n,Ho=o,Uo.attachEvent("onpropertychange",Sc)):e==="focusout"&&wc()}function lg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ki(Ho)}function sg(e,n){if(e==="click")return Ki(n)}function ag(e,n){if(e==="input"||e==="change")return Ki(n)}function ug(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var rn=typeof Object.is=="function"?Object.is:ug;function Bo(e,n){if(rn(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var o=Object.keys(e),s=Object.keys(n);if(o.length!==s.length)return!1;for(s=0;s<o.length;s++){var u=o[s];if(!p.call(n,u)||!rn(e[u],n[u]))return!1}return!0}function xc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Cc(e,n){var o=xc(e);e=0;for(var s;o;){if(o.nodeType===3){if(s=e+o.textContent.length,e<=n&&s>=n)return{node:o,offset:n-e};e=s}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=xc(o)}}function Rc(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?Rc(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function Ec(){for(var e=window,n=Hr();n instanceof e.HTMLIFrameElement;){try{var o=typeof n.contentWindow.location.href=="string"}catch{o=!1}if(o)e=n.contentWindow;else break;n=Hr(e.document)}return n}function Ps(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function cg(e){var n=Ec(),o=e.focusedElem,s=e.selectionRange;if(n!==o&&o&&o.ownerDocument&&Rc(o.ownerDocument.documentElement,o)){if(s!==null&&Ps(o)){if(n=s.start,e=s.end,e===void 0&&(e=n),"selectionStart"in o)o.selectionStart=n,o.selectionEnd=Math.min(e,o.value.length);else if(e=(n=o.ownerDocument||document)&&n.defaultView||window,e.getSelection){e=e.getSelection();var u=o.textContent.length,d=Math.min(s.start,u);s=s.end===void 0?d:Math.min(s.end,u),!e.extend&&d>s&&(u=s,s=d,d=u),u=Cc(o,d);var g=Cc(o,s);u&&g&&(e.rangeCount!==1||e.anchorNode!==u.node||e.anchorOffset!==u.offset||e.focusNode!==g.node||e.focusOffset!==g.offset)&&(n=n.createRange(),n.setStart(u.node,u.offset),e.removeAllRanges(),d>s?(e.addRange(n),e.extend(g.node,g.offset)):(n.setEnd(g.node,g.offset),e.addRange(n)))}}for(n=[],e=o;e=e.parentNode;)e.nodeType===1&&n.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<n.length;o++)e=n[o],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var dg=h&&"documentMode"in document&&11>=document.documentMode,Zr=null,ks=null,Go=null,_s=!1;function Pc(e,n,o){var s=o.window===o?o.document:o.nodeType===9?o:o.ownerDocument;_s||Zr==null||Zr!==Hr(s)||(s=Zr,"selectionStart"in s&&Ps(s)?s={start:s.selectionStart,end:s.selectionEnd}:(s=(s.ownerDocument&&s.ownerDocument.defaultView||window).getSelection(),s={anchorNode:s.anchorNode,anchorOffset:s.anchorOffset,focusNode:s.focusNode,focusOffset:s.focusOffset}),Go&&Bo(Go,s)||(Go=s,s=Ji(ks,"onSelect"),0<s.length&&(n=new vs("onSelect","select",null,n,o),e.push({event:n,listeners:s}),n.target=Zr)))}function Yi(e,n){var o={};return o[e.toLowerCase()]=n.toLowerCase(),o["Webkit"+e]="webkit"+n,o["Moz"+e]="moz"+n,o}var Jr={animationend:Yi("Animation","AnimationEnd"),animationiteration:Yi("Animation","AnimationIteration"),animationstart:Yi("Animation","AnimationStart"),transitionend:Yi("Transition","TransitionEnd")},Ms={},kc={};h&&(kc=document.createElement("div").style,"AnimationEvent"in window||(delete Jr.animationend.animation,delete Jr.animationiteration.animation,delete Jr.animationstart.animation),"TransitionEvent"in window||delete Jr.transitionend.transition);function Xi(e){if(Ms[e])return Ms[e];if(!Jr[e])return e;var n=Jr[e],o;for(o in n)if(n.hasOwnProperty(o)&&o in kc)return Ms[e]=n[o];return e}var _c=Xi("animationend"),Mc=Xi("animationiteration"),Fc=Xi("animationstart"),Lc=Xi("transitionend"),Nc=new Map,Dc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Qn(e,n){Nc.set(e,n),c(n,[e])}for(var Fs=0;Fs<Dc.length;Fs++){var Ls=Dc[Fs],fg=Ls.toLowerCase(),hg=Ls[0].toUpperCase()+Ls.slice(1);Qn(fg,"on"+hg)}Qn(_c,"onAnimationEnd"),Qn(Mc,"onAnimationIteration"),Qn(Fc,"onAnimationStart"),Qn("dblclick","onDoubleClick"),Qn("focusin","onFocus"),Qn("focusout","onBlur"),Qn(Lc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),c("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),c("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),c("onBeforeInput",["compositionend","keypress","textInput","paste"]),c("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),c("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Qo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),pg=new Set("cancel close invalid load scroll toggle".split(" ").concat(Qo));function Tc(e,n,o){var s=e.type||"unknown-event";e.currentTarget=o,ke(s,n,void 0,e),e.currentTarget=null}function jc(e,n){n=(n&4)!==0;for(var o=0;o<e.length;o++){var s=e[o],u=s.event;s=s.listeners;e:{var d=void 0;if(n)for(var g=s.length-1;0<=g;g--){var w=s[g],E=w.instance,O=w.currentTarget;if(w=w.listener,E!==d&&u.isPropagationStopped())break e;Tc(u,w,O),d=E}else for(g=0;g<s.length;g++){if(w=s[g],E=w.instance,O=w.currentTarget,w=w.listener,E!==d&&u.isPropagationStopped())break e;Tc(u,w,O),d=E}}}if(le)throw e=ne,le=!1,ne=null,e}function Qe(e,n){var o=n[$s];o===void 0&&(o=n[$s]=new Set);var s=e+"__bubble";o.has(s)||(zc(n,e,2,!1),o.add(s))}function Ns(e,n,o){var s=0;n&&(s|=4),zc(o,e,s,n)}var Zi="_reactListening"+Math.random().toString(36).slice(2);function Wo(e){if(!e[Zi]){e[Zi]=!0,l.forEach(function(o){o!=="selectionchange"&&(pg.has(o)||Ns(o,!1,e),Ns(o,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[Zi]||(n[Zi]=!0,Ns("selectionchange",!1,n))}}function zc(e,n,o,s){switch(oc(n)){case 1:var u=Mp;break;case 4:u=Fp;break;default:u=ps}o=u.bind(null,n,o,e),u=void 0,!H||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(u=!0),s?u!==void 0?e.addEventListener(n,o,{capture:!0,passive:u}):e.addEventListener(n,o,!0):u!==void 0?e.addEventListener(n,o,{passive:u}):e.addEventListener(n,o,!1)}function Ds(e,n,o,s,u){var d=s;if((n&1)===0&&(n&2)===0&&s!==null)e:for(;;){if(s===null)return;var g=s.tag;if(g===3||g===4){var w=s.stateNode.containerInfo;if(w===u||w.nodeType===8&&w.parentNode===u)break;if(g===4)for(g=s.return;g!==null;){var E=g.tag;if((E===3||E===4)&&(E=g.stateNode.containerInfo,E===u||E.nodeType===8&&E.parentNode===u))return;g=g.return}for(;w!==null;){if(g=kr(w),g===null)return;if(E=g.tag,E===5||E===6){s=d=g;continue e}w=w.parentNode}}s=s.return}N(function(){var O=d,V=xr(o),U=[];e:{var b=Nc.get(e);if(b!==void 0){var Y=vs,te=e;switch(e){case"keypress":if(Qi(o)===0)break e;case"keydown":case"keyup":Y=Bp;break;case"focusin":te="focus",Y=Ss;break;case"focusout":te="blur",Y=Ss;break;case"beforeblur":case"afterblur":Y=Ss;break;case"click":if(o.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":Y=sc;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":Y=Dp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":Y=Wp;break;case _c:case Mc:case Fc:Y=zp;break;case Lc:Y=Kp;break;case"scroll":Y=Lp;break;case"wheel":Y=Xp;break;case"copy":case"cut":case"paste":Y=Ip;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":Y=uc}var re=(n&4)!==0,tt=!re&&e==="scroll",D=re?b!==null?b+"Capture":null:b;re=[];for(var k=O,z;k!==null;){z=k;var Q=z.stateNode;if(z.tag===5&&Q!==null&&(z=Q,D!==null&&(Q=I(k,D),Q!=null&&re.push(qo(k,Q,z)))),tt)break;k=k.return}0<re.length&&(b=new Y(b,te,null,o,V),U.push({event:b,listeners:re}))}}if((n&7)===0){e:{if(b=e==="mouseover"||e==="pointerover",Y=e==="mouseout"||e==="pointerout",b&&o!==qr&&(te=o.relatedTarget||o.fromElement)&&(kr(te)||te[Pn]))break e;if((Y||b)&&(b=V.window===V?V:(b=V.ownerDocument)?b.defaultView||b.parentWindow:window,Y?(te=o.relatedTarget||o.toElement,Y=O,te=te?kr(te):null,te!==null&&(tt=Te(te),te!==tt||te.tag!==5&&te.tag!==6)&&(te=null)):(Y=null,te=O),Y!==te)){if(re=sc,Q="onMouseLeave",D="onMouseEnter",k="mouse",(e==="pointerout"||e==="pointerover")&&(re=uc,Q="onPointerLeave",D="onPointerEnter",k="pointer"),tt=Y==null?b:no(Y),z=te==null?b:no(te),b=new re(Q,k+"leave",Y,o,V),b.target=tt,b.relatedTarget=z,Q=null,kr(V)===O&&(re=new re(D,k+"enter",te,o,V),re.target=z,re.relatedTarget=tt,Q=re),tt=Q,Y&&te)t:{for(re=Y,D=te,k=0,z=re;z;z=eo(z))k++;for(z=0,Q=D;Q;Q=eo(Q))z++;for(;0<k-z;)re=eo(re),k--;for(;0<z-k;)D=eo(D),z--;for(;k--;){if(re===D||D!==null&&re===D.alternate)break t;re=eo(re),D=eo(D)}re=null}else re=null;Y!==null&&Oc(U,b,Y,re,!1),te!==null&&tt!==null&&Oc(U,tt,te,re,!0)}}e:{if(b=O?no(O):window,Y=b.nodeName&&b.nodeName.toLowerCase(),Y==="select"||Y==="input"&&b.type==="file")var oe=og;else if(gc(b))if(vc)oe=ag;else{oe=lg;var de=ig}else(Y=b.nodeName)&&Y.toLowerCase()==="input"&&(b.type==="checkbox"||b.type==="radio")&&(oe=sg);if(oe&&(oe=oe(e,O))){mc(U,oe,o,V);break e}de&&de(e,b,O),e==="focusout"&&(de=b._wrapperState)&&de.controlled&&b.type==="number"&&Po(b,"number",b.value)}switch(de=O?no(O):window,e){case"focusin":(gc(de)||de.contentEditable==="true")&&(Zr=de,ks=O,Go=null);break;case"focusout":Go=ks=Zr=null;break;case"mousedown":_s=!0;break;case"contextmenu":case"mouseup":case"dragend":_s=!1,Pc(U,o,V);break;case"selectionchange":if(dg)break;case"keydown":case"keyup":Pc(U,o,V)}var fe;if(Cs)e:{switch(e){case"compositionstart":var ge="onCompositionStart";break e;case"compositionend":ge="onCompositionEnd";break e;case"compositionupdate":ge="onCompositionUpdate";break e}ge=void 0}else Xr?hc(e,o)&&(ge="onCompositionEnd"):e==="keydown"&&o.keyCode===229&&(ge="onCompositionStart");ge&&(cc&&o.locale!=="ko"&&(Xr||ge!=="onCompositionStart"?ge==="onCompositionEnd"&&Xr&&(fe=ic()):(Gn=V,ms="value"in Gn?Gn.value:Gn.textContent,Xr=!0)),de=Ji(O,ge),0<de.length&&(ge=new ac(ge,e,null,o,V),U.push({event:ge,listeners:de}),fe?ge.data=fe:(fe=pc(o),fe!==null&&(ge.data=fe)))),(fe=Jp?eg(e,o):tg(e,o))&&(O=Ji(O,"onBeforeInput"),0<O.length&&(V=new ac("onBeforeInput","beforeinput",null,o,V),U.push({event:V,listeners:O}),V.data=fe))}jc(U,n)})}function qo(e,n,o){return{instance:e,listener:n,currentTarget:o}}function Ji(e,n){for(var o=n+"Capture",s=[];e!==null;){var u=e,d=u.stateNode;u.tag===5&&d!==null&&(u=d,d=I(e,o),d!=null&&s.unshift(qo(e,d,u)),d=I(e,n),d!=null&&s.push(qo(e,d,u))),e=e.return}return s}function eo(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Oc(e,n,o,s,u){for(var d=n._reactName,g=[];o!==null&&o!==s;){var w=o,E=w.alternate,O=w.stateNode;if(E!==null&&E===s)break;w.tag===5&&O!==null&&(w=O,u?(E=I(o,d),E!=null&&g.unshift(qo(o,E,w))):u||(E=I(o,d),E!=null&&g.push(qo(o,E,w)))),o=o.return}g.length!==0&&e.push({event:n,listeners:g})}var gg=/\r\n?/g,mg=/\u0000|\uFFFD/g;function Ic(e){return(typeof e=="string"?e:""+e).replace(gg,`
`).replace(mg,"")}function el(e,n,o){if(n=Ic(n),Ic(e)!==n&&o)throw Error(i(425))}function tl(){}var Ts=null,js=null;function zs(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Os=typeof setTimeout=="function"?setTimeout:void 0,vg=typeof clearTimeout=="function"?clearTimeout:void 0,$c=typeof Promise=="function"?Promise:void 0,yg=typeof queueMicrotask=="function"?queueMicrotask:typeof $c<"u"?function(e){return $c.resolve(null).then(e).catch(wg)}:Os;function wg(e){setTimeout(function(){throw e})}function Is(e,n){var o=n,s=0;do{var u=o.nextSibling;if(e.removeChild(o),u&&u.nodeType===8)if(o=u.data,o==="/$"){if(s===0){e.removeChild(u),$o(n);return}s--}else o!=="$"&&o!=="$?"&&o!=="$!"||s++;o=u}while(o);$o(n)}function Wn(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return e}function Ac(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var o=e.data;if(o==="$"||o==="$!"||o==="$?"){if(n===0)return e;n--}else o==="/$"&&n++}e=e.previousSibling}return null}var to=Math.random().toString(36).slice(2),pn="__reactFiber$"+to,Ko="__reactProps$"+to,Pn="__reactContainer$"+to,$s="__reactEvents$"+to,Sg="__reactListeners$"+to,xg="__reactHandles$"+to;function kr(e){var n=e[pn];if(n)return n;for(var o=e.parentNode;o;){if(n=o[Pn]||o[pn]){if(o=n.alternate,n.child!==null||o!==null&&o.child!==null)for(e=Ac(e);e!==null;){if(o=e[pn])return o;e=Ac(e)}return n}e=o,o=e.parentNode}return null}function Yo(e){return e=e[pn]||e[Pn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function no(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function nl(e){return e[Ko]||null}var As=[],ro=-1;function qn(e){return{current:e}}function We(e){0>ro||(e.current=As[ro],As[ro]=null,ro--)}function Ge(e,n){ro++,As[ro]=e.current,e.current=n}var Kn={},pt=qn(Kn),_t=qn(!1),_r=Kn;function oo(e,n){var o=e.type.contextTypes;if(!o)return Kn;var s=e.stateNode;if(s&&s.__reactInternalMemoizedUnmaskedChildContext===n)return s.__reactInternalMemoizedMaskedChildContext;var u={},d;for(d in o)u[d]=n[d];return s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=u),u}function Mt(e){return e=e.childContextTypes,e!=null}function rl(){We(_t),We(pt)}function bc(e,n,o){if(pt.current!==Kn)throw Error(i(168));Ge(pt,n),Ge(_t,o)}function Vc(e,n,o){var s=e.stateNode;if(n=n.childContextTypes,typeof s.getChildContext!="function")return o;s=s.getChildContext();for(var u in s)if(!(u in n))throw Error(i(108,Ve(e)||"Unknown",u));return W({},o,s)}function ol(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Kn,_r=pt.current,Ge(pt,e),Ge(_t,_t.current),!0}function Uc(e,n,o){var s=e.stateNode;if(!s)throw Error(i(169));o?(e=Vc(e,n,_r),s.__reactInternalMemoizedMergedChildContext=e,We(_t),We(pt),Ge(pt,e)):We(_t),Ge(_t,o)}var kn=null,il=!1,bs=!1;function Hc(e){kn===null?kn=[e]:kn.push(e)}function Cg(e){il=!0,Hc(e)}function Yn(){if(!bs&&kn!==null){bs=!0;var e=0,n=be;try{var o=kn;for(be=1;e<o.length;e++){var s=o[e];do s=s(!0);while(s!==null)}kn=null,il=!1}catch(u){throw kn!==null&&(kn=kn.slice(e+1)),hn(tn,Yn),u}finally{be=n,bs=!1}}return null}var io=[],lo=0,ll=null,sl=0,Ht=[],Bt=0,Mr=null,_n=1,Mn="";function Fr(e,n){io[lo++]=sl,io[lo++]=ll,ll=e,sl=n}function Bc(e,n,o){Ht[Bt++]=_n,Ht[Bt++]=Mn,Ht[Bt++]=Mr,Mr=e;var s=_n;e=Mn;var u=32-st(s)-1;s&=~(1<<u),o+=1;var d=32-st(n)+u;if(30<d){var g=u-u%5;d=(s&(1<<g)-1).toString(32),s>>=g,u-=g,_n=1<<32-st(n)+u|o<<u|s,Mn=d+e}else _n=1<<d|o<<u|s,Mn=e}function Vs(e){e.return!==null&&(Fr(e,1),Bc(e,1,0))}function Us(e){for(;e===ll;)ll=io[--lo],io[lo]=null,sl=io[--lo],io[lo]=null;for(;e===Mr;)Mr=Ht[--Bt],Ht[Bt]=null,Mn=Ht[--Bt],Ht[Bt]=null,_n=Ht[--Bt],Ht[Bt]=null}var It=null,$t=null,Ke=!1,on=null;function Gc(e,n){var o=qt(5,null,null,0);o.elementType="DELETED",o.stateNode=n,o.return=e,n=e.deletions,n===null?(e.deletions=[o],e.flags|=16):n.push(o)}function Qc(e,n){switch(e.tag){case 5:var o=e.type;return n=n.nodeType!==1||o.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(e.stateNode=n,It=e,$t=Wn(n.firstChild),!0):!1;case 6:return n=e.pendingProps===""||n.nodeType!==3?null:n,n!==null?(e.stateNode=n,It=e,$t=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(o=Mr!==null?{id:_n,overflow:Mn}:null,e.memoizedState={dehydrated:n,treeContext:o,retryLane:1073741824},o=qt(18,null,null,0),o.stateNode=n,o.return=e,e.child=o,It=e,$t=null,!0):!1;default:return!1}}function Hs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bs(e){if(Ke){var n=$t;if(n){var o=n;if(!Qc(e,n)){if(Hs(e))throw Error(i(418));n=Wn(o.nextSibling);var s=It;n&&Qc(e,n)?Gc(s,o):(e.flags=e.flags&-4097|2,Ke=!1,It=e)}}else{if(Hs(e))throw Error(i(418));e.flags=e.flags&-4097|2,Ke=!1,It=e}}}function Wc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;It=e}function al(e){if(e!==It)return!1;if(!Ke)return Wc(e),Ke=!0,!1;var n;if((n=e.tag!==3)&&!(n=e.tag!==5)&&(n=e.type,n=n!=="head"&&n!=="body"&&!zs(e.type,e.memoizedProps)),n&&(n=$t)){if(Hs(e))throw qc(),Error(i(418));for(;n;)Gc(e,n),n=Wn(n.nextSibling)}if(Wc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8){var o=e.data;if(o==="/$"){if(n===0){$t=Wn(e.nextSibling);break e}n--}else o!=="$"&&o!=="$!"&&o!=="$?"||n++}e=e.nextSibling}$t=null}}else $t=It?Wn(e.stateNode.nextSibling):null;return!0}function qc(){for(var e=$t;e;)e=Wn(e.nextSibling)}function so(){$t=It=null,Ke=!1}function Gs(e){on===null?on=[e]:on.push(e)}var Rg=K.ReactCurrentBatchConfig;function Xo(e,n,o){if(e=o.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(o._owner){if(o=o._owner,o){if(o.tag!==1)throw Error(i(309));var s=o.stateNode}if(!s)throw Error(i(147,e));var u=s,d=""+e;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===d?n.ref:(n=function(g){var w=u.refs;g===null?delete w[d]:w[d]=g},n._stringRef=d,n)}if(typeof e!="string")throw Error(i(284));if(!o._owner)throw Error(i(290,e))}return e}function ul(e,n){throw e=Object.prototype.toString.call(n),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function Kc(e){var n=e._init;return n(e._payload)}function Yc(e){function n(D,k){if(e){var z=D.deletions;z===null?(D.deletions=[k],D.flags|=16):z.push(k)}}function o(D,k){if(!e)return null;for(;k!==null;)n(D,k),k=k.sibling;return null}function s(D,k){for(D=new Map;k!==null;)k.key!==null?D.set(k.key,k):D.set(k.index,k),k=k.sibling;return D}function u(D,k){return D=or(D,k),D.index=0,D.sibling=null,D}function d(D,k,z){return D.index=z,e?(z=D.alternate,z!==null?(z=z.index,z<k?(D.flags|=2,k):z):(D.flags|=2,k)):(D.flags|=1048576,k)}function g(D){return e&&D.alternate===null&&(D.flags|=2),D}function w(D,k,z,Q){return k===null||k.tag!==6?(k=Oa(z,D.mode,Q),k.return=D,k):(k=u(k,z),k.return=D,k)}function E(D,k,z,Q){var oe=z.type;return oe===ie?V(D,k,z.props.children,Q,z.key):k!==null&&(k.elementType===oe||typeof oe=="object"&&oe!==null&&oe.$$typeof===pe&&Kc(oe)===k.type)?(Q=u(k,z.props),Q.ref=Xo(D,k,z),Q.return=D,Q):(Q=Tl(z.type,z.key,z.props,null,D.mode,Q),Q.ref=Xo(D,k,z),Q.return=D,Q)}function O(D,k,z,Q){return k===null||k.tag!==4||k.stateNode.containerInfo!==z.containerInfo||k.stateNode.implementation!==z.implementation?(k=Ia(z,D.mode,Q),k.return=D,k):(k=u(k,z.children||[]),k.return=D,k)}function V(D,k,z,Q,oe){return k===null||k.tag!==7?(k=Ir(z,D.mode,Q,oe),k.return=D,k):(k=u(k,z),k.return=D,k)}function U(D,k,z){if(typeof k=="string"&&k!==""||typeof k=="number")return k=Oa(""+k,D.mode,z),k.return=D,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case Z:return z=Tl(k.type,k.key,k.props,null,D.mode,z),z.ref=Xo(D,null,k),z.return=D,z;case j:return k=Ia(k,D.mode,z),k.return=D,k;case pe:var Q=k._init;return U(D,Q(k._payload),z)}if(yr(k)||J(k))return k=Ir(k,D.mode,z,null),k.return=D,k;ul(D,k)}return null}function b(D,k,z,Q){var oe=k!==null?k.key:null;if(typeof z=="string"&&z!==""||typeof z=="number")return oe!==null?null:w(D,k,""+z,Q);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case Z:return z.key===oe?E(D,k,z,Q):null;case j:return z.key===oe?O(D,k,z,Q):null;case pe:return oe=z._init,b(D,k,oe(z._payload),Q)}if(yr(z)||J(z))return oe!==null?null:V(D,k,z,Q,null);ul(D,z)}return null}function Y(D,k,z,Q,oe){if(typeof Q=="string"&&Q!==""||typeof Q=="number")return D=D.get(z)||null,w(k,D,""+Q,oe);if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case Z:return D=D.get(Q.key===null?z:Q.key)||null,E(k,D,Q,oe);case j:return D=D.get(Q.key===null?z:Q.key)||null,O(k,D,Q,oe);case pe:var de=Q._init;return Y(D,k,z,de(Q._payload),oe)}if(yr(Q)||J(Q))return D=D.get(z)||null,V(k,D,Q,oe,null);ul(k,Q)}return null}function te(D,k,z,Q){for(var oe=null,de=null,fe=k,ge=k=0,ct=null;fe!==null&&ge<z.length;ge++){fe.index>ge?(ct=fe,fe=null):ct=fe.sibling;var Oe=b(D,fe,z[ge],Q);if(Oe===null){fe===null&&(fe=ct);break}e&&fe&&Oe.alternate===null&&n(D,fe),k=d(Oe,k,ge),de===null?oe=Oe:de.sibling=Oe,de=Oe,fe=ct}if(ge===z.length)return o(D,fe),Ke&&Fr(D,ge),oe;if(fe===null){for(;ge<z.length;ge++)fe=U(D,z[ge],Q),fe!==null&&(k=d(fe,k,ge),de===null?oe=fe:de.sibling=fe,de=fe);return Ke&&Fr(D,ge),oe}for(fe=s(D,fe);ge<z.length;ge++)ct=Y(fe,D,ge,z[ge],Q),ct!==null&&(e&&ct.alternate!==null&&fe.delete(ct.key===null?ge:ct.key),k=d(ct,k,ge),de===null?oe=ct:de.sibling=ct,de=ct);return e&&fe.forEach(function(ir){return n(D,ir)}),Ke&&Fr(D,ge),oe}function re(D,k,z,Q){var oe=J(z);if(typeof oe!="function")throw Error(i(150));if(z=oe.call(z),z==null)throw Error(i(151));for(var de=oe=null,fe=k,ge=k=0,ct=null,Oe=z.next();fe!==null&&!Oe.done;ge++,Oe=z.next()){fe.index>ge?(ct=fe,fe=null):ct=fe.sibling;var ir=b(D,fe,Oe.value,Q);if(ir===null){fe===null&&(fe=ct);break}e&&fe&&ir.alternate===null&&n(D,fe),k=d(ir,k,ge),de===null?oe=ir:de.sibling=ir,de=ir,fe=ct}if(Oe.done)return o(D,fe),Ke&&Fr(D,ge),oe;if(fe===null){for(;!Oe.done;ge++,Oe=z.next())Oe=U(D,Oe.value,Q),Oe!==null&&(k=d(Oe,k,ge),de===null?oe=Oe:de.sibling=Oe,de=Oe);return Ke&&Fr(D,ge),oe}for(fe=s(D,fe);!Oe.done;ge++,Oe=z.next())Oe=Y(fe,D,ge,Oe.value,Q),Oe!==null&&(e&&Oe.alternate!==null&&fe.delete(Oe.key===null?ge:Oe.key),k=d(Oe,k,ge),de===null?oe=Oe:de.sibling=Oe,de=Oe);return e&&fe.forEach(function(nm){return n(D,nm)}),Ke&&Fr(D,ge),oe}function tt(D,k,z,Q){if(typeof z=="object"&&z!==null&&z.type===ie&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case Z:e:{for(var oe=z.key,de=k;de!==null;){if(de.key===oe){if(oe=z.type,oe===ie){if(de.tag===7){o(D,de.sibling),k=u(de,z.props.children),k.return=D,D=k;break e}}else if(de.elementType===oe||typeof oe=="object"&&oe!==null&&oe.$$typeof===pe&&Kc(oe)===de.type){o(D,de.sibling),k=u(de,z.props),k.ref=Xo(D,de,z),k.return=D,D=k;break e}o(D,de);break}else n(D,de);de=de.sibling}z.type===ie?(k=Ir(z.props.children,D.mode,Q,z.key),k.return=D,D=k):(Q=Tl(z.type,z.key,z.props,null,D.mode,Q),Q.ref=Xo(D,k,z),Q.return=D,D=Q)}return g(D);case j:e:{for(de=z.key;k!==null;){if(k.key===de)if(k.tag===4&&k.stateNode.containerInfo===z.containerInfo&&k.stateNode.implementation===z.implementation){o(D,k.sibling),k=u(k,z.children||[]),k.return=D,D=k;break e}else{o(D,k);break}else n(D,k);k=k.sibling}k=Ia(z,D.mode,Q),k.return=D,D=k}return g(D);case pe:return de=z._init,tt(D,k,de(z._payload),Q)}if(yr(z))return te(D,k,z,Q);if(J(z))return re(D,k,z,Q);ul(D,z)}return typeof z=="string"&&z!==""||typeof z=="number"?(z=""+z,k!==null&&k.tag===6?(o(D,k.sibling),k=u(k,z),k.return=D,D=k):(o(D,k),k=Oa(z,D.mode,Q),k.return=D,D=k),g(D)):o(D,k)}return tt}var ao=Yc(!0),Xc=Yc(!1),cl=qn(null),dl=null,uo=null,Qs=null;function Ws(){Qs=uo=dl=null}function qs(e){var n=cl.current;We(cl),e._currentValue=n}function Ks(e,n,o){for(;e!==null;){var s=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,s!==null&&(s.childLanes|=n)):s!==null&&(s.childLanes&n)!==n&&(s.childLanes|=n),e===o)break;e=e.return}}function co(e,n){dl=e,Qs=uo=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&n)!==0&&(Ft=!0),e.firstContext=null)}function Gt(e){var n=e._currentValue;if(Qs!==e)if(e={context:e,memoizedValue:n,next:null},uo===null){if(dl===null)throw Error(i(308));uo=e,dl.dependencies={lanes:0,firstContext:e}}else uo=uo.next=e;return n}var Lr=null;function Ys(e){Lr===null?Lr=[e]:Lr.push(e)}function Zc(e,n,o,s){var u=n.interleaved;return u===null?(o.next=o,Ys(n)):(o.next=u.next,u.next=o),n.interleaved=o,Fn(e,s)}function Fn(e,n){e.lanes|=n;var o=e.alternate;for(o!==null&&(o.lanes|=n),o=e,e=e.return;e!==null;)e.childLanes|=n,o=e.alternate,o!==null&&(o.childLanes|=n),o=e,e=e.return;return o.tag===3?o.stateNode:null}var Xn=!1;function Xs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jc(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ln(e,n){return{eventTime:e,lane:n,tag:0,payload:null,callback:null,next:null}}function Zn(e,n,o){var s=e.updateQueue;if(s===null)return null;if(s=s.shared,(ze&2)!==0){var u=s.pending;return u===null?n.next=n:(n.next=u.next,u.next=n),s.pending=n,Fn(e,o)}return u=s.interleaved,u===null?(n.next=n,Ys(s)):(n.next=u.next,u.next=n),s.interleaved=n,Fn(e,o)}function fl(e,n,o){if(n=n.updateQueue,n!==null&&(n=n.shared,(o&4194240)!==0)){var s=n.lanes;s&=e.pendingLanes,o|=s,n.lanes=o,ds(e,o)}}function ed(e,n){var o=e.updateQueue,s=e.alternate;if(s!==null&&(s=s.updateQueue,o===s)){var u=null,d=null;if(o=o.firstBaseUpdate,o!==null){do{var g={eventTime:o.eventTime,lane:o.lane,tag:o.tag,payload:o.payload,callback:o.callback,next:null};d===null?u=d=g:d=d.next=g,o=o.next}while(o!==null);d===null?u=d=n:d=d.next=n}else u=d=n;o={baseState:s.baseState,firstBaseUpdate:u,lastBaseUpdate:d,shared:s.shared,effects:s.effects},e.updateQueue=o;return}e=o.lastBaseUpdate,e===null?o.firstBaseUpdate=n:e.next=n,o.lastBaseUpdate=n}function hl(e,n,o,s){var u=e.updateQueue;Xn=!1;var d=u.firstBaseUpdate,g=u.lastBaseUpdate,w=u.shared.pending;if(w!==null){u.shared.pending=null;var E=w,O=E.next;E.next=null,g===null?d=O:g.next=O,g=E;var V=e.alternate;V!==null&&(V=V.updateQueue,w=V.lastBaseUpdate,w!==g&&(w===null?V.firstBaseUpdate=O:w.next=O,V.lastBaseUpdate=E))}if(d!==null){var U=u.baseState;g=0,V=O=E=null,w=d;do{var b=w.lane,Y=w.eventTime;if((s&b)===b){V!==null&&(V=V.next={eventTime:Y,lane:0,tag:w.tag,payload:w.payload,callback:w.callback,next:null});e:{var te=e,re=w;switch(b=n,Y=o,re.tag){case 1:if(te=re.payload,typeof te=="function"){U=te.call(Y,U,b);break e}U=te;break e;case 3:te.flags=te.flags&-65537|128;case 0:if(te=re.payload,b=typeof te=="function"?te.call(Y,U,b):te,b==null)break e;U=W({},U,b);break e;case 2:Xn=!0}}w.callback!==null&&w.lane!==0&&(e.flags|=64,b=u.effects,b===null?u.effects=[w]:b.push(w))}else Y={eventTime:Y,lane:b,tag:w.tag,payload:w.payload,callback:w.callback,next:null},V===null?(O=V=Y,E=U):V=V.next=Y,g|=b;if(w=w.next,w===null){if(w=u.shared.pending,w===null)break;b=w,w=b.next,b.next=null,u.lastBaseUpdate=b,u.shared.pending=null}}while(!0);if(V===null&&(E=U),u.baseState=E,u.firstBaseUpdate=O,u.lastBaseUpdate=V,n=u.shared.interleaved,n!==null){u=n;do g|=u.lane,u=u.next;while(u!==n)}else d===null&&(u.shared.lanes=0);Tr|=g,e.lanes=g,e.memoizedState=U}}function td(e,n,o){if(e=n.effects,n.effects=null,e!==null)for(n=0;n<e.length;n++){var s=e[n],u=s.callback;if(u!==null){if(s.callback=null,s=o,typeof u!="function")throw Error(i(191,u));u.call(s)}}}var Zo={},gn=qn(Zo),Jo=qn(Zo),ei=qn(Zo);function Nr(e){if(e===Zo)throw Error(i(174));return e}function Zs(e,n){switch(Ge(ei,n),Ge(Jo,e),Ge(gn,Zo),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:zt(null,"");break;default:e=e===8?n.parentNode:n,n=e.namespaceURI||null,e=e.tagName,n=zt(n,e)}We(gn),Ge(gn,n)}function fo(){We(gn),We(Jo),We(ei)}function nd(e){Nr(ei.current);var n=Nr(gn.current),o=zt(n,e.type);n!==o&&(Ge(Jo,e),Ge(gn,o))}function Js(e){Jo.current===e&&(We(gn),We(Jo))}var Ye=qn(0);function pl(e){for(var n=e;n!==null;){if(n.tag===13){var o=n.memoizedState;if(o!==null&&(o=o.dehydrated,o===null||o.data==="$?"||o.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var ea=[];function ta(){for(var e=0;e<ea.length;e++)ea[e]._workInProgressVersionPrimary=null;ea.length=0}var gl=K.ReactCurrentDispatcher,na=K.ReactCurrentBatchConfig,Dr=0,Xe=null,it=null,at=null,ml=!1,ti=!1,ni=0,Eg=0;function gt(){throw Error(i(321))}function ra(e,n){if(n===null)return!1;for(var o=0;o<n.length&&o<e.length;o++)if(!rn(e[o],n[o]))return!1;return!0}function oa(e,n,o,s,u,d){if(Dr=d,Xe=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,gl.current=e===null||e.memoizedState===null?Mg:Fg,e=o(s,u),ti){d=0;do{if(ti=!1,ni=0,25<=d)throw Error(i(301));d+=1,at=it=null,n.updateQueue=null,gl.current=Lg,e=o(s,u)}while(ti)}if(gl.current=wl,n=it!==null&&it.next!==null,Dr=0,at=it=Xe=null,ml=!1,n)throw Error(i(300));return e}function ia(){var e=ni!==0;return ni=0,e}function mn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return at===null?Xe.memoizedState=at=e:at=at.next=e,at}function Qt(){if(it===null){var e=Xe.alternate;e=e!==null?e.memoizedState:null}else e=it.next;var n=at===null?Xe.memoizedState:at.next;if(n!==null)at=n,it=e;else{if(e===null)throw Error(i(310));it=e,e={memoizedState:it.memoizedState,baseState:it.baseState,baseQueue:it.baseQueue,queue:it.queue,next:null},at===null?Xe.memoizedState=at=e:at=at.next=e}return at}function ri(e,n){return typeof n=="function"?n(e):n}function la(e){var n=Qt(),o=n.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=e;var s=it,u=s.baseQueue,d=o.pending;if(d!==null){if(u!==null){var g=u.next;u.next=d.next,d.next=g}s.baseQueue=u=d,o.pending=null}if(u!==null){d=u.next,s=s.baseState;var w=g=null,E=null,O=d;do{var V=O.lane;if((Dr&V)===V)E!==null&&(E=E.next={lane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),s=O.hasEagerState?O.eagerState:e(s,O.action);else{var U={lane:V,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null};E===null?(w=E=U,g=s):E=E.next=U,Xe.lanes|=V,Tr|=V}O=O.next}while(O!==null&&O!==d);E===null?g=s:E.next=w,rn(s,n.memoizedState)||(Ft=!0),n.memoizedState=s,n.baseState=g,n.baseQueue=E,o.lastRenderedState=s}if(e=o.interleaved,e!==null){u=e;do d=u.lane,Xe.lanes|=d,Tr|=d,u=u.next;while(u!==e)}else u===null&&(o.lanes=0);return[n.memoizedState,o.dispatch]}function sa(e){var n=Qt(),o=n.queue;if(o===null)throw Error(i(311));o.lastRenderedReducer=e;var s=o.dispatch,u=o.pending,d=n.memoizedState;if(u!==null){o.pending=null;var g=u=u.next;do d=e(d,g.action),g=g.next;while(g!==u);rn(d,n.memoizedState)||(Ft=!0),n.memoizedState=d,n.baseQueue===null&&(n.baseState=d),o.lastRenderedState=d}return[d,s]}function rd(){}function od(e,n){var o=Xe,s=Qt(),u=n(),d=!rn(s.memoizedState,u);if(d&&(s.memoizedState=u,Ft=!0),s=s.queue,aa(sd.bind(null,o,s,e),[e]),s.getSnapshot!==n||d||at!==null&&at.memoizedState.tag&1){if(o.flags|=2048,oi(9,ld.bind(null,o,s,u,n),void 0,null),ut===null)throw Error(i(349));(Dr&30)!==0||id(o,n,u)}return u}function id(e,n,o){e.flags|=16384,e={getSnapshot:n,value:o},n=Xe.updateQueue,n===null?(n={lastEffect:null,stores:null},Xe.updateQueue=n,n.stores=[e]):(o=n.stores,o===null?n.stores=[e]:o.push(e))}function ld(e,n,o,s){n.value=o,n.getSnapshot=s,ad(n)&&ud(e)}function sd(e,n,o){return o(function(){ad(n)&&ud(e)})}function ad(e){var n=e.getSnapshot;e=e.value;try{var o=n();return!rn(e,o)}catch{return!0}}function ud(e){var n=Fn(e,1);n!==null&&un(n,e,1,-1)}function cd(e){var n=mn();return typeof e=="function"&&(e=e()),n.memoizedState=n.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ri,lastRenderedState:e},n.queue=e,e=e.dispatch=_g.bind(null,Xe,e),[n.memoizedState,e]}function oi(e,n,o,s){return e={tag:e,create:n,destroy:o,deps:s,next:null},n=Xe.updateQueue,n===null?(n={lastEffect:null,stores:null},Xe.updateQueue=n,n.lastEffect=e.next=e):(o=n.lastEffect,o===null?n.lastEffect=e.next=e:(s=o.next,o.next=e,e.next=s,n.lastEffect=e)),e}function dd(){return Qt().memoizedState}function vl(e,n,o,s){var u=mn();Xe.flags|=e,u.memoizedState=oi(1|n,o,void 0,s===void 0?null:s)}function yl(e,n,o,s){var u=Qt();s=s===void 0?null:s;var d=void 0;if(it!==null){var g=it.memoizedState;if(d=g.destroy,s!==null&&ra(s,g.deps)){u.memoizedState=oi(n,o,d,s);return}}Xe.flags|=e,u.memoizedState=oi(1|n,o,d,s)}function fd(e,n){return vl(8390656,8,e,n)}function aa(e,n){return yl(2048,8,e,n)}function hd(e,n){return yl(4,2,e,n)}function pd(e,n){return yl(4,4,e,n)}function gd(e,n){if(typeof n=="function")return e=e(),n(e),function(){n(null)};if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function md(e,n,o){return o=o!=null?o.concat([e]):null,yl(4,4,gd.bind(null,n,e),o)}function ua(){}function vd(e,n){var o=Qt();n=n===void 0?null:n;var s=o.memoizedState;return s!==null&&n!==null&&ra(n,s[1])?s[0]:(o.memoizedState=[e,n],e)}function yd(e,n){var o=Qt();n=n===void 0?null:n;var s=o.memoizedState;return s!==null&&n!==null&&ra(n,s[1])?s[0]:(e=e(),o.memoizedState=[e,n],e)}function wd(e,n,o){return(Dr&21)===0?(e.baseState&&(e.baseState=!1,Ft=!0),e.memoizedState=o):(rn(o,n)||(o=Ku(),Xe.lanes|=o,Tr|=o,e.baseState=!0),n)}function Pg(e,n){var o=be;be=o!==0&&4>o?o:4,e(!0);var s=na.transition;na.transition={};try{e(!1),n()}finally{be=o,na.transition=s}}function Sd(){return Qt().memoizedState}function kg(e,n,o){var s=nr(e);if(o={lane:s,action:o,hasEagerState:!1,eagerState:null,next:null},xd(e))Cd(n,o);else if(o=Zc(e,n,o,s),o!==null){var u=xt();un(o,e,s,u),Rd(o,n,s)}}function _g(e,n,o){var s=nr(e),u={lane:s,action:o,hasEagerState:!1,eagerState:null,next:null};if(xd(e))Cd(n,u);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=n.lastRenderedReducer,d!==null))try{var g=n.lastRenderedState,w=d(g,o);if(u.hasEagerState=!0,u.eagerState=w,rn(w,g)){var E=n.interleaved;E===null?(u.next=u,Ys(n)):(u.next=E.next,E.next=u),n.interleaved=u;return}}catch{}finally{}o=Zc(e,n,u,s),o!==null&&(u=xt(),un(o,e,s,u),Rd(o,n,s))}}function xd(e){var n=e.alternate;return e===Xe||n!==null&&n===Xe}function Cd(e,n){ti=ml=!0;var o=e.pending;o===null?n.next=n:(n.next=o.next,o.next=n),e.pending=n}function Rd(e,n,o){if((o&4194240)!==0){var s=n.lanes;s&=e.pendingLanes,o|=s,n.lanes=o,ds(e,o)}}var wl={readContext:Gt,useCallback:gt,useContext:gt,useEffect:gt,useImperativeHandle:gt,useInsertionEffect:gt,useLayoutEffect:gt,useMemo:gt,useReducer:gt,useRef:gt,useState:gt,useDebugValue:gt,useDeferredValue:gt,useTransition:gt,useMutableSource:gt,useSyncExternalStore:gt,useId:gt,unstable_isNewReconciler:!1},Mg={readContext:Gt,useCallback:function(e,n){return mn().memoizedState=[e,n===void 0?null:n],e},useContext:Gt,useEffect:fd,useImperativeHandle:function(e,n,o){return o=o!=null?o.concat([e]):null,vl(4194308,4,gd.bind(null,n,e),o)},useLayoutEffect:function(e,n){return vl(4194308,4,e,n)},useInsertionEffect:function(e,n){return vl(4,2,e,n)},useMemo:function(e,n){var o=mn();return n=n===void 0?null:n,e=e(),o.memoizedState=[e,n],e},useReducer:function(e,n,o){var s=mn();return n=o!==void 0?o(n):n,s.memoizedState=s.baseState=n,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},s.queue=e,e=e.dispatch=kg.bind(null,Xe,e),[s.memoizedState,e]},useRef:function(e){var n=mn();return e={current:e},n.memoizedState=e},useState:cd,useDebugValue:ua,useDeferredValue:function(e){return mn().memoizedState=e},useTransition:function(){var e=cd(!1),n=e[0];return e=Pg.bind(null,e[1]),mn().memoizedState=e,[n,e]},useMutableSource:function(){},useSyncExternalStore:function(e,n,o){var s=Xe,u=mn();if(Ke){if(o===void 0)throw Error(i(407));o=o()}else{if(o=n(),ut===null)throw Error(i(349));(Dr&30)!==0||id(s,n,o)}u.memoizedState=o;var d={value:o,getSnapshot:n};return u.queue=d,fd(sd.bind(null,s,d,e),[e]),s.flags|=2048,oi(9,ld.bind(null,s,d,o,n),void 0,null),o},useId:function(){var e=mn(),n=ut.identifierPrefix;if(Ke){var o=Mn,s=_n;o=(s&~(1<<32-st(s)-1)).toString(32)+o,n=":"+n+"R"+o,o=ni++,0<o&&(n+="H"+o.toString(32)),n+=":"}else o=Eg++,n=":"+n+"r"+o.toString(32)+":";return e.memoizedState=n},unstable_isNewReconciler:!1},Fg={readContext:Gt,useCallback:vd,useContext:Gt,useEffect:aa,useImperativeHandle:md,useInsertionEffect:hd,useLayoutEffect:pd,useMemo:yd,useReducer:la,useRef:dd,useState:function(){return la(ri)},useDebugValue:ua,useDeferredValue:function(e){var n=Qt();return wd(n,it.memoizedState,e)},useTransition:function(){var e=la(ri)[0],n=Qt().memoizedState;return[e,n]},useMutableSource:rd,useSyncExternalStore:od,useId:Sd,unstable_isNewReconciler:!1},Lg={readContext:Gt,useCallback:vd,useContext:Gt,useEffect:aa,useImperativeHandle:md,useInsertionEffect:hd,useLayoutEffect:pd,useMemo:yd,useReducer:sa,useRef:dd,useState:function(){return sa(ri)},useDebugValue:ua,useDeferredValue:function(e){var n=Qt();return it===null?n.memoizedState=e:wd(n,it.memoizedState,e)},useTransition:function(){var e=sa(ri)[0],n=Qt().memoizedState;return[e,n]},useMutableSource:rd,useSyncExternalStore:od,useId:Sd,unstable_isNewReconciler:!1};function ln(e,n){if(e&&e.defaultProps){n=W({},n),e=e.defaultProps;for(var o in e)n[o]===void 0&&(n[o]=e[o]);return n}return n}function ca(e,n,o,s){n=e.memoizedState,o=o(s,n),o=o==null?n:W({},n,o),e.memoizedState=o,e.lanes===0&&(e.updateQueue.baseState=o)}var Sl={isMounted:function(e){return(e=e._reactInternals)?Te(e)===e:!1},enqueueSetState:function(e,n,o){e=e._reactInternals;var s=xt(),u=nr(e),d=Ln(s,u);d.payload=n,o!=null&&(d.callback=o),n=Zn(e,d,u),n!==null&&(un(n,e,u,s),fl(n,e,u))},enqueueReplaceState:function(e,n,o){e=e._reactInternals;var s=xt(),u=nr(e),d=Ln(s,u);d.tag=1,d.payload=n,o!=null&&(d.callback=o),n=Zn(e,d,u),n!==null&&(un(n,e,u,s),fl(n,e,u))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var o=xt(),s=nr(e),u=Ln(o,s);u.tag=2,n!=null&&(u.callback=n),n=Zn(e,u,s),n!==null&&(un(n,e,s,o),fl(n,e,s))}};function Ed(e,n,o,s,u,d,g){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(s,d,g):n.prototype&&n.prototype.isPureReactComponent?!Bo(o,s)||!Bo(u,d):!0}function Pd(e,n,o){var s=!1,u=Kn,d=n.contextType;return typeof d=="object"&&d!==null?d=Gt(d):(u=Mt(n)?_r:pt.current,s=n.contextTypes,d=(s=s!=null)?oo(e,u):Kn),n=new n(o,d),e.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=Sl,e.stateNode=n,n._reactInternals=e,s&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=u,e.__reactInternalMemoizedMaskedChildContext=d),n}function kd(e,n,o,s){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(o,s),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(o,s),n.state!==e&&Sl.enqueueReplaceState(n,n.state,null)}function da(e,n,o,s){var u=e.stateNode;u.props=o,u.state=e.memoizedState,u.refs={},Xs(e);var d=n.contextType;typeof d=="object"&&d!==null?u.context=Gt(d):(d=Mt(n)?_r:pt.current,u.context=oo(e,d)),u.state=e.memoizedState,d=n.getDerivedStateFromProps,typeof d=="function"&&(ca(e,n,d,o),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(n=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),n!==u.state&&Sl.enqueueReplaceState(u,u.state,null),hl(e,o,u,s),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308)}function ho(e,n){try{var o="",s=n;do o+=Le(s),s=s.return;while(s);var u=o}catch(d){u=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:n,stack:u,digest:null}}function fa(e,n,o){return{value:e,source:null,stack:o??null,digest:n??null}}function ha(e,n){try{console.error(n.value)}catch(o){setTimeout(function(){throw o})}}var Ng=typeof WeakMap=="function"?WeakMap:Map;function _d(e,n,o){o=Ln(-1,o),o.tag=3,o.payload={element:null};var s=n.value;return o.callback=function(){_l||(_l=!0,Ma=s),ha(e,n)},o}function Md(e,n,o){o=Ln(-1,o),o.tag=3;var s=e.type.getDerivedStateFromError;if(typeof s=="function"){var u=n.value;o.payload=function(){return s(u)},o.callback=function(){ha(e,n)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(o.callback=function(){ha(e,n),typeof s!="function"&&(er===null?er=new Set([this]):er.add(this));var g=n.stack;this.componentDidCatch(n.value,{componentStack:g!==null?g:""})}),o}function Fd(e,n,o){var s=e.pingCache;if(s===null){s=e.pingCache=new Ng;var u=new Set;s.set(n,u)}else u=s.get(n),u===void 0&&(u=new Set,s.set(n,u));u.has(o)||(u.add(o),e=Gg.bind(null,e,n,o),n.then(e,e))}function Ld(e){do{var n;if((n=e.tag===13)&&(n=e.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return e;e=e.return}while(e!==null);return null}function Nd(e,n,o,s,u){return(e.mode&1)===0?(e===n?e.flags|=65536:(e.flags|=128,o.flags|=131072,o.flags&=-52805,o.tag===1&&(o.alternate===null?o.tag=17:(n=Ln(-1,1),n.tag=2,Zn(o,n,1))),o.lanes|=1),e):(e.flags|=65536,e.lanes=u,e)}var Dg=K.ReactCurrentOwner,Ft=!1;function St(e,n,o,s){n.child=e===null?Xc(n,null,o,s):ao(n,e.child,o,s)}function Dd(e,n,o,s,u){o=o.render;var d=n.ref;return co(n,u),s=oa(e,n,o,s,d,u),o=ia(),e!==null&&!Ft?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~u,Nn(e,n,u)):(Ke&&o&&Vs(n),n.flags|=1,St(e,n,s,u),n.child)}function Td(e,n,o,s,u){if(e===null){var d=o.type;return typeof d=="function"&&!za(d)&&d.defaultProps===void 0&&o.compare===null&&o.defaultProps===void 0?(n.tag=15,n.type=d,jd(e,n,d,s,u)):(e=Tl(o.type,null,s,n,n.mode,u),e.ref=n.ref,e.return=n,n.child=e)}if(d=e.child,(e.lanes&u)===0){var g=d.memoizedProps;if(o=o.compare,o=o!==null?o:Bo,o(g,s)&&e.ref===n.ref)return Nn(e,n,u)}return n.flags|=1,e=or(d,s),e.ref=n.ref,e.return=n,n.child=e}function jd(e,n,o,s,u){if(e!==null){var d=e.memoizedProps;if(Bo(d,s)&&e.ref===n.ref)if(Ft=!1,n.pendingProps=s=d,(e.lanes&u)!==0)(e.flags&131072)!==0&&(Ft=!0);else return n.lanes=e.lanes,Nn(e,n,u)}return pa(e,n,o,s,u)}function zd(e,n,o){var s=n.pendingProps,u=s.children,d=e!==null?e.memoizedState:null;if(s.mode==="hidden")if((n.mode&1)===0)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ge(go,At),At|=o;else{if((o&1073741824)===0)return e=d!==null?d.baseLanes|o:o,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:e,cachePool:null,transitions:null},n.updateQueue=null,Ge(go,At),At|=e,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},s=d!==null?d.baseLanes:o,Ge(go,At),At|=s}else d!==null?(s=d.baseLanes|o,n.memoizedState=null):s=o,Ge(go,At),At|=s;return St(e,n,u,o),n.child}function Od(e,n){var o=n.ref;(e===null&&o!==null||e!==null&&e.ref!==o)&&(n.flags|=512,n.flags|=2097152)}function pa(e,n,o,s,u){var d=Mt(o)?_r:pt.current;return d=oo(n,d),co(n,u),o=oa(e,n,o,s,d,u),s=ia(),e!==null&&!Ft?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~u,Nn(e,n,u)):(Ke&&s&&Vs(n),n.flags|=1,St(e,n,o,u),n.child)}function Id(e,n,o,s,u){if(Mt(o)){var d=!0;ol(n)}else d=!1;if(co(n,u),n.stateNode===null)Cl(e,n),Pd(n,o,s),da(n,o,s,u),s=!0;else if(e===null){var g=n.stateNode,w=n.memoizedProps;g.props=w;var E=g.context,O=o.contextType;typeof O=="object"&&O!==null?O=Gt(O):(O=Mt(o)?_r:pt.current,O=oo(n,O));var V=o.getDerivedStateFromProps,U=typeof V=="function"||typeof g.getSnapshotBeforeUpdate=="function";U||typeof g.UNSAFE_componentWillReceiveProps!="function"&&typeof g.componentWillReceiveProps!="function"||(w!==s||E!==O)&&kd(n,g,s,O),Xn=!1;var b=n.memoizedState;g.state=b,hl(n,s,g,u),E=n.memoizedState,w!==s||b!==E||_t.current||Xn?(typeof V=="function"&&(ca(n,o,V,s),E=n.memoizedState),(w=Xn||Ed(n,o,w,s,b,E,O))?(U||typeof g.UNSAFE_componentWillMount!="function"&&typeof g.componentWillMount!="function"||(typeof g.componentWillMount=="function"&&g.componentWillMount(),typeof g.UNSAFE_componentWillMount=="function"&&g.UNSAFE_componentWillMount()),typeof g.componentDidMount=="function"&&(n.flags|=4194308)):(typeof g.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=s,n.memoizedState=E),g.props=s,g.state=E,g.context=O,s=w):(typeof g.componentDidMount=="function"&&(n.flags|=4194308),s=!1)}else{g=n.stateNode,Jc(e,n),w=n.memoizedProps,O=n.type===n.elementType?w:ln(n.type,w),g.props=O,U=n.pendingProps,b=g.context,E=o.contextType,typeof E=="object"&&E!==null?E=Gt(E):(E=Mt(o)?_r:pt.current,E=oo(n,E));var Y=o.getDerivedStateFromProps;(V=typeof Y=="function"||typeof g.getSnapshotBeforeUpdate=="function")||typeof g.UNSAFE_componentWillReceiveProps!="function"&&typeof g.componentWillReceiveProps!="function"||(w!==U||b!==E)&&kd(n,g,s,E),Xn=!1,b=n.memoizedState,g.state=b,hl(n,s,g,u);var te=n.memoizedState;w!==U||b!==te||_t.current||Xn?(typeof Y=="function"&&(ca(n,o,Y,s),te=n.memoizedState),(O=Xn||Ed(n,o,O,s,b,te,E)||!1)?(V||typeof g.UNSAFE_componentWillUpdate!="function"&&typeof g.componentWillUpdate!="function"||(typeof g.componentWillUpdate=="function"&&g.componentWillUpdate(s,te,E),typeof g.UNSAFE_componentWillUpdate=="function"&&g.UNSAFE_componentWillUpdate(s,te,E)),typeof g.componentDidUpdate=="function"&&(n.flags|=4),typeof g.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof g.componentDidUpdate!="function"||w===e.memoizedProps&&b===e.memoizedState||(n.flags|=4),typeof g.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&b===e.memoizedState||(n.flags|=1024),n.memoizedProps=s,n.memoizedState=te),g.props=s,g.state=te,g.context=E,s=O):(typeof g.componentDidUpdate!="function"||w===e.memoizedProps&&b===e.memoizedState||(n.flags|=4),typeof g.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&b===e.memoizedState||(n.flags|=1024),s=!1)}return ga(e,n,o,s,d,u)}function ga(e,n,o,s,u,d){Od(e,n);var g=(n.flags&128)!==0;if(!s&&!g)return u&&Uc(n,o,!1),Nn(e,n,d);s=n.stateNode,Dg.current=n;var w=g&&typeof o.getDerivedStateFromError!="function"?null:s.render();return n.flags|=1,e!==null&&g?(n.child=ao(n,e.child,null,d),n.child=ao(n,null,w,d)):St(e,n,w,d),n.memoizedState=s.state,u&&Uc(n,o,!0),n.child}function $d(e){var n=e.stateNode;n.pendingContext?bc(e,n.pendingContext,n.pendingContext!==n.context):n.context&&bc(e,n.context,!1),Zs(e,n.containerInfo)}function Ad(e,n,o,s,u){return so(),Gs(u),n.flags|=256,St(e,n,o,s),n.child}var ma={dehydrated:null,treeContext:null,retryLane:0};function va(e){return{baseLanes:e,cachePool:null,transitions:null}}function bd(e,n,o){var s=n.pendingProps,u=Ye.current,d=!1,g=(n.flags&128)!==0,w;if((w=g)||(w=e!==null&&e.memoizedState===null?!1:(u&2)!==0),w?(d=!0,n.flags&=-129):(e===null||e.memoizedState!==null)&&(u|=1),Ge(Ye,u&1),e===null)return Bs(n),e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((n.mode&1)===0?n.lanes=1:e.data==="$!"?n.lanes=8:n.lanes=1073741824,null):(g=s.children,e=s.fallback,d?(s=n.mode,d=n.child,g={mode:"hidden",children:g},(s&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=g):d=jl(g,s,0,null),e=Ir(e,s,o,null),d.return=n,e.return=n,d.sibling=e,n.child=d,n.child.memoizedState=va(o),n.memoizedState=ma,e):ya(n,g));if(u=e.memoizedState,u!==null&&(w=u.dehydrated,w!==null))return Tg(e,n,g,s,w,u,o);if(d){d=s.fallback,g=n.mode,u=e.child,w=u.sibling;var E={mode:"hidden",children:s.children};return(g&1)===0&&n.child!==u?(s=n.child,s.childLanes=0,s.pendingProps=E,n.deletions=null):(s=or(u,E),s.subtreeFlags=u.subtreeFlags&14680064),w!==null?d=or(w,d):(d=Ir(d,g,o,null),d.flags|=2),d.return=n,s.return=n,s.sibling=d,n.child=s,s=d,d=n.child,g=e.child.memoizedState,g=g===null?va(o):{baseLanes:g.baseLanes|o,cachePool:null,transitions:g.transitions},d.memoizedState=g,d.childLanes=e.childLanes&~o,n.memoizedState=ma,s}return d=e.child,e=d.sibling,s=or(d,{mode:"visible",children:s.children}),(n.mode&1)===0&&(s.lanes=o),s.return=n,s.sibling=null,e!==null&&(o=n.deletions,o===null?(n.deletions=[e],n.flags|=16):o.push(e)),n.child=s,n.memoizedState=null,s}function ya(e,n){return n=jl({mode:"visible",children:n},e.mode,0,null),n.return=e,e.child=n}function xl(e,n,o,s){return s!==null&&Gs(s),ao(n,e.child,null,o),e=ya(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function Tg(e,n,o,s,u,d,g){if(o)return n.flags&256?(n.flags&=-257,s=fa(Error(i(422))),xl(e,n,g,s)):n.memoizedState!==null?(n.child=e.child,n.flags|=128,null):(d=s.fallback,u=n.mode,s=jl({mode:"visible",children:s.children},u,0,null),d=Ir(d,u,g,null),d.flags|=2,s.return=n,d.return=n,s.sibling=d,n.child=s,(n.mode&1)!==0&&ao(n,e.child,null,g),n.child.memoizedState=va(g),n.memoizedState=ma,d);if((n.mode&1)===0)return xl(e,n,g,null);if(u.data==="$!"){if(s=u.nextSibling&&u.nextSibling.dataset,s)var w=s.dgst;return s=w,d=Error(i(419)),s=fa(d,s,void 0),xl(e,n,g,s)}if(w=(g&e.childLanes)!==0,Ft||w){if(s=ut,s!==null){switch(g&-g){case 4:u=2;break;case 16:u=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:u=32;break;case 536870912:u=268435456;break;default:u=0}u=(u&(s.suspendedLanes|g))!==0?0:u,u!==0&&u!==d.retryLane&&(d.retryLane=u,Fn(e,u),un(s,e,u,-1))}return ja(),s=fa(Error(i(421))),xl(e,n,g,s)}return u.data==="$?"?(n.flags|=128,n.child=e.child,n=Qg.bind(null,e),u._reactRetry=n,null):(e=d.treeContext,$t=Wn(u.nextSibling),It=n,Ke=!0,on=null,e!==null&&(Ht[Bt++]=_n,Ht[Bt++]=Mn,Ht[Bt++]=Mr,_n=e.id,Mn=e.overflow,Mr=n),n=ya(n,s.children),n.flags|=4096,n)}function Vd(e,n,o){e.lanes|=n;var s=e.alternate;s!==null&&(s.lanes|=n),Ks(e.return,n,o)}function wa(e,n,o,s,u){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:s,tail:o,tailMode:u}:(d.isBackwards=n,d.rendering=null,d.renderingStartTime=0,d.last=s,d.tail=o,d.tailMode=u)}function Ud(e,n,o){var s=n.pendingProps,u=s.revealOrder,d=s.tail;if(St(e,n,s.children,o),s=Ye.current,(s&2)!==0)s=s&1|2,n.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vd(e,o,n);else if(e.tag===19)Vd(e,o,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}s&=1}if(Ge(Ye,s),(n.mode&1)===0)n.memoizedState=null;else switch(u){case"forwards":for(o=n.child,u=null;o!==null;)e=o.alternate,e!==null&&pl(e)===null&&(u=o),o=o.sibling;o=u,o===null?(u=n.child,n.child=null):(u=o.sibling,o.sibling=null),wa(n,!1,u,o,d);break;case"backwards":for(o=null,u=n.child,n.child=null;u!==null;){if(e=u.alternate,e!==null&&pl(e)===null){n.child=u;break}e=u.sibling,u.sibling=o,o=u,u=e}wa(n,!0,o,null,d);break;case"together":wa(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Cl(e,n){(n.mode&1)===0&&e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2)}function Nn(e,n,o){if(e!==null&&(n.dependencies=e.dependencies),Tr|=n.lanes,(o&n.childLanes)===0)return null;if(e!==null&&n.child!==e.child)throw Error(i(153));if(n.child!==null){for(e=n.child,o=or(e,e.pendingProps),n.child=o,o.return=n;e.sibling!==null;)e=e.sibling,o=o.sibling=or(e,e.pendingProps),o.return=n;o.sibling=null}return n.child}function jg(e,n,o){switch(n.tag){case 3:$d(n),so();break;case 5:nd(n);break;case 1:Mt(n.type)&&ol(n);break;case 4:Zs(n,n.stateNode.containerInfo);break;case 10:var s=n.type._context,u=n.memoizedProps.value;Ge(cl,s._currentValue),s._currentValue=u;break;case 13:if(s=n.memoizedState,s!==null)return s.dehydrated!==null?(Ge(Ye,Ye.current&1),n.flags|=128,null):(o&n.child.childLanes)!==0?bd(e,n,o):(Ge(Ye,Ye.current&1),e=Nn(e,n,o),e!==null?e.sibling:null);Ge(Ye,Ye.current&1);break;case 19:if(s=(o&n.childLanes)!==0,(e.flags&128)!==0){if(s)return Ud(e,n,o);n.flags|=128}if(u=n.memoizedState,u!==null&&(u.rendering=null,u.tail=null,u.lastEffect=null),Ge(Ye,Ye.current),s)break;return null;case 22:case 23:return n.lanes=0,zd(e,n,o)}return Nn(e,n,o)}var Hd,Sa,Bd,Gd;Hd=function(e,n){for(var o=n.child;o!==null;){if(o.tag===5||o.tag===6)e.appendChild(o.stateNode);else if(o.tag!==4&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===n)break;for(;o.sibling===null;){if(o.return===null||o.return===n)return;o=o.return}o.sibling.return=o.return,o=o.sibling}},Sa=function(){},Bd=function(e,n,o,s){var u=e.memoizedProps;if(u!==s){e=n.stateNode,Nr(gn.current);var d=null;switch(o){case"input":u=Zt(e,u),s=Zt(e,s),d=[];break;case"select":u=W({},u,{value:void 0}),s=W({},s,{value:void 0}),d=[];break;case"textarea":u=Jt(e,u),s=Jt(e,s),d=[];break;default:typeof u.onClick!="function"&&typeof s.onClick=="function"&&(e.onclick=tl)}Wr(o,s);var g;o=null;for(O in u)if(!s.hasOwnProperty(O)&&u.hasOwnProperty(O)&&u[O]!=null)if(O==="style"){var w=u[O];for(g in w)w.hasOwnProperty(g)&&(o||(o={}),o[g]="")}else O!=="dangerouslySetInnerHTML"&&O!=="children"&&O!=="suppressContentEditableWarning"&&O!=="suppressHydrationWarning"&&O!=="autoFocus"&&(a.hasOwnProperty(O)?d||(d=[]):(d=d||[]).push(O,null));for(O in s){var E=s[O];if(w=u?.[O],s.hasOwnProperty(O)&&E!==w&&(E!=null||w!=null))if(O==="style")if(w){for(g in w)!w.hasOwnProperty(g)||E&&E.hasOwnProperty(g)||(o||(o={}),o[g]="");for(g in E)E.hasOwnProperty(g)&&w[g]!==E[g]&&(o||(o={}),o[g]=E[g])}else o||(d||(d=[]),d.push(O,o)),o=E;else O==="dangerouslySetInnerHTML"?(E=E?E.__html:void 0,w=w?w.__html:void 0,E!=null&&w!==E&&(d=d||[]).push(O,E)):O==="children"?typeof E!="string"&&typeof E!="number"||(d=d||[]).push(O,""+E):O!=="suppressContentEditableWarning"&&O!=="suppressHydrationWarning"&&(a.hasOwnProperty(O)?(E!=null&&O==="onScroll"&&Qe("scroll",e),d||w===E||(d=[])):(d=d||[]).push(O,E))}o&&(d=d||[]).push("style",o);var O=d;(n.updateQueue=O)&&(n.flags|=4)}},Gd=function(e,n,o,s){o!==s&&(n.flags|=4)};function ii(e,n){if(!Ke)switch(e.tailMode){case"hidden":n=e.tail;for(var o=null;n!==null;)n.alternate!==null&&(o=n),n=n.sibling;o===null?e.tail=null:o.sibling=null;break;case"collapsed":o=e.tail;for(var s=null;o!==null;)o.alternate!==null&&(s=o),o=o.sibling;s===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:s.sibling=null}}function mt(e){var n=e.alternate!==null&&e.alternate.child===e.child,o=0,s=0;if(n)for(var u=e.child;u!==null;)o|=u.lanes|u.childLanes,s|=u.subtreeFlags&14680064,s|=u.flags&14680064,u.return=e,u=u.sibling;else for(u=e.child;u!==null;)o|=u.lanes|u.childLanes,s|=u.subtreeFlags,s|=u.flags,u.return=e,u=u.sibling;return e.subtreeFlags|=s,e.childLanes=o,n}function zg(e,n,o){var s=n.pendingProps;switch(Us(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return mt(n),null;case 1:return Mt(n.type)&&rl(),mt(n),null;case 3:return s=n.stateNode,fo(),We(_t),We(pt),ta(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(al(n)?n.flags|=4:e===null||e.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,on!==null&&(Na(on),on=null))),Sa(e,n),mt(n),null;case 5:Js(n);var u=Nr(ei.current);if(o=n.type,e!==null&&n.stateNode!=null)Bd(e,n,o,s,u),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!s){if(n.stateNode===null)throw Error(i(166));return mt(n),null}if(e=Nr(gn.current),al(n)){s=n.stateNode,o=n.type;var d=n.memoizedProps;switch(s[pn]=n,s[Ko]=d,e=(n.mode&1)!==0,o){case"dialog":Qe("cancel",s),Qe("close",s);break;case"iframe":case"object":case"embed":Qe("load",s);break;case"video":case"audio":for(u=0;u<Qo.length;u++)Qe(Qo[u],s);break;case"source":Qe("error",s);break;case"img":case"image":case"link":Qe("error",s),Qe("load",s);break;case"details":Qe("toggle",s);break;case"input":Ni(s,d),Qe("invalid",s);break;case"select":s._wrapperState={wasMultiple:!!d.multiple},Qe("invalid",s);break;case"textarea":On(s,d),Qe("invalid",s)}Wr(o,d),u=null;for(var g in d)if(d.hasOwnProperty(g)){var w=d[g];g==="children"?typeof w=="string"?s.textContent!==w&&(d.suppressHydrationWarning!==!0&&el(s.textContent,w,e),u=["children",w]):typeof w=="number"&&s.textContent!==""+w&&(d.suppressHydrationWarning!==!0&&el(s.textContent,w,e),u=["children",""+w]):a.hasOwnProperty(g)&&w!=null&&g==="onScroll"&&Qe("scroll",s)}switch(o){case"input":Xt(s),Ti(s,d,!0);break;case"textarea":Xt(s),Gr(s);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(s.onclick=tl)}s=u,n.updateQueue=s,s!==null&&(n.flags|=4)}else{g=u.nodeType===9?u:u.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=en(o)),e==="http://www.w3.org/1999/xhtml"?o==="script"?(e=g.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof s.is=="string"?e=g.createElement(o,{is:s.is}):(e=g.createElement(o),o==="select"&&(g=e,s.multiple?g.multiple=!0:s.size&&(g.size=s.size))):e=g.createElementNS(e,o),e[pn]=n,e[Ko]=s,Hd(e,n,!1,!1),n.stateNode=e;e:{switch(g=In(o,s),o){case"dialog":Qe("cancel",e),Qe("close",e),u=s;break;case"iframe":case"object":case"embed":Qe("load",e),u=s;break;case"video":case"audio":for(u=0;u<Qo.length;u++)Qe(Qo[u],e);u=s;break;case"source":Qe("error",e),u=s;break;case"img":case"image":case"link":Qe("error",e),Qe("load",e),u=s;break;case"details":Qe("toggle",e),u=s;break;case"input":Ni(e,s),u=Zt(e,s),Qe("invalid",e);break;case"option":u=s;break;case"select":e._wrapperState={wasMultiple:!!s.multiple},u=W({},s,{value:void 0}),Qe("invalid",e);break;case"textarea":On(e,s),u=Jt(e,s),Qe("invalid",e);break;default:u=s}Wr(o,u),w=u;for(d in w)if(w.hasOwnProperty(d)){var E=w[d];d==="style"?Mo(e,E):d==="dangerouslySetInnerHTML"?(E=E?E.__html:void 0,E!=null&&Qr(e,E)):d==="children"?typeof E=="string"?(o!=="textarea"||E!=="")&&Sr(e,E):typeof E=="number"&&Sr(e,""+E):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(a.hasOwnProperty(d)?E!=null&&d==="onScroll"&&Qe("scroll",e):E!=null&&A(e,d,E,g))}switch(o){case"input":Xt(e),Ti(e,s,!1);break;case"textarea":Xt(e),Gr(e);break;case"option":s.value!=null&&e.setAttribute("value",""+$e(s.value));break;case"select":e.multiple=!!s.multiple,d=s.value,d!=null?zn(e,!!s.multiple,d,!1):s.defaultValue!=null&&zn(e,!!s.multiple,s.defaultValue,!0);break;default:typeof u.onClick=="function"&&(e.onclick=tl)}switch(o){case"button":case"input":case"select":case"textarea":s=!!s.autoFocus;break e;case"img":s=!0;break e;default:s=!1}}s&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return mt(n),null;case 6:if(e&&n.stateNode!=null)Gd(e,n,e.memoizedProps,s);else{if(typeof s!="string"&&n.stateNode===null)throw Error(i(166));if(o=Nr(ei.current),Nr(gn.current),al(n)){if(s=n.stateNode,o=n.memoizedProps,s[pn]=n,(d=s.nodeValue!==o)&&(e=It,e!==null))switch(e.tag){case 3:el(s.nodeValue,o,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&el(s.nodeValue,o,(e.mode&1)!==0)}d&&(n.flags|=4)}else s=(o.nodeType===9?o:o.ownerDocument).createTextNode(s),s[pn]=n,n.stateNode=s}return mt(n),null;case 13:if(We(Ye),s=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ke&&$t!==null&&(n.mode&1)!==0&&(n.flags&128)===0)qc(),so(),n.flags|=98560,d=!1;else if(d=al(n),s!==null&&s.dehydrated!==null){if(e===null){if(!d)throw Error(i(318));if(d=n.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(i(317));d[pn]=n}else so(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;mt(n),d=!1}else on!==null&&(Na(on),on=null),d=!0;if(!d)return n.flags&65536?n:null}return(n.flags&128)!==0?(n.lanes=o,n):(s=s!==null,s!==(e!==null&&e.memoizedState!==null)&&s&&(n.child.flags|=8192,(n.mode&1)!==0&&(e===null||(Ye.current&1)!==0?lt===0&&(lt=3):ja())),n.updateQueue!==null&&(n.flags|=4),mt(n),null);case 4:return fo(),Sa(e,n),e===null&&Wo(n.stateNode.containerInfo),mt(n),null;case 10:return qs(n.type._context),mt(n),null;case 17:return Mt(n.type)&&rl(),mt(n),null;case 19:if(We(Ye),d=n.memoizedState,d===null)return mt(n),null;if(s=(n.flags&128)!==0,g=d.rendering,g===null)if(s)ii(d,!1);else{if(lt!==0||e!==null&&(e.flags&128)!==0)for(e=n.child;e!==null;){if(g=pl(e),g!==null){for(n.flags|=128,ii(d,!1),s=g.updateQueue,s!==null&&(n.updateQueue=s,n.flags|=4),n.subtreeFlags=0,s=o,o=n.child;o!==null;)d=o,e=s,d.flags&=14680066,g=d.alternate,g===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=g.childLanes,d.lanes=g.lanes,d.child=g.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=g.memoizedProps,d.memoizedState=g.memoizedState,d.updateQueue=g.updateQueue,d.type=g.type,e=g.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),o=o.sibling;return Ge(Ye,Ye.current&1|2),n.child}e=e.sibling}d.tail!==null&&Ue()>mo&&(n.flags|=128,s=!0,ii(d,!1),n.lanes=4194304)}else{if(!s)if(e=pl(g),e!==null){if(n.flags|=128,s=!0,o=e.updateQueue,o!==null&&(n.updateQueue=o,n.flags|=4),ii(d,!0),d.tail===null&&d.tailMode==="hidden"&&!g.alternate&&!Ke)return mt(n),null}else 2*Ue()-d.renderingStartTime>mo&&o!==1073741824&&(n.flags|=128,s=!0,ii(d,!1),n.lanes=4194304);d.isBackwards?(g.sibling=n.child,n.child=g):(o=d.last,o!==null?o.sibling=g:n.child=g,d.last=g)}return d.tail!==null?(n=d.tail,d.rendering=n,d.tail=n.sibling,d.renderingStartTime=Ue(),n.sibling=null,o=Ye.current,Ge(Ye,s?o&1|2:o&1),n):(mt(n),null);case 22:case 23:return Ta(),s=n.memoizedState!==null,e!==null&&e.memoizedState!==null!==s&&(n.flags|=8192),s&&(n.mode&1)!==0?(At&1073741824)!==0&&(mt(n),n.subtreeFlags&6&&(n.flags|=8192)):mt(n),null;case 24:return null;case 25:return null}throw Error(i(156,n.tag))}function Og(e,n){switch(Us(n),n.tag){case 1:return Mt(n.type)&&rl(),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return fo(),We(_t),We(pt),ta(),e=n.flags,(e&65536)!==0&&(e&128)===0?(n.flags=e&-65537|128,n):null;case 5:return Js(n),null;case 13:if(We(Ye),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(i(340));so()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return We(Ye),null;case 4:return fo(),null;case 10:return qs(n.type._context),null;case 22:case 23:return Ta(),null;case 24:return null;default:return null}}var Rl=!1,vt=!1,Ig=typeof WeakSet=="function"?WeakSet:Set,ee=null;function po(e,n){var o=e.ref;if(o!==null)if(typeof o=="function")try{o(null)}catch(s){et(e,n,s)}else o.current=null}function xa(e,n,o){try{o()}catch(s){et(e,n,s)}}var Qd=!1;function $g(e,n){if(Ts=Hi,e=Ec(),Ps(e)){if("selectionStart"in e)var o={start:e.selectionStart,end:e.selectionEnd};else e:{o=(o=e.ownerDocument)&&o.defaultView||window;var s=o.getSelection&&o.getSelection();if(s&&s.rangeCount!==0){o=s.anchorNode;var u=s.anchorOffset,d=s.focusNode;s=s.focusOffset;try{o.nodeType,d.nodeType}catch{o=null;break e}var g=0,w=-1,E=-1,O=0,V=0,U=e,b=null;t:for(;;){for(var Y;U!==o||u!==0&&U.nodeType!==3||(w=g+u),U!==d||s!==0&&U.nodeType!==3||(E=g+s),U.nodeType===3&&(g+=U.nodeValue.length),(Y=U.firstChild)!==null;)b=U,U=Y;for(;;){if(U===e)break t;if(b===o&&++O===u&&(w=g),b===d&&++V===s&&(E=g),(Y=U.nextSibling)!==null)break;U=b,b=U.parentNode}U=Y}o=w===-1||E===-1?null:{start:w,end:E}}else o=null}o=o||{start:0,end:0}}else o=null;for(js={focusedElem:e,selectionRange:o},Hi=!1,ee=n;ee!==null;)if(n=ee,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,ee=e;else for(;ee!==null;){n=ee;try{var te=n.alternate;if((n.flags&1024)!==0)switch(n.tag){case 0:case 11:case 15:break;case 1:if(te!==null){var re=te.memoizedProps,tt=te.memoizedState,D=n.stateNode,k=D.getSnapshotBeforeUpdate(n.elementType===n.type?re:ln(n.type,re),tt);D.__reactInternalSnapshotBeforeUpdate=k}break;case 3:var z=n.stateNode.containerInfo;z.nodeType===1?z.textContent="":z.nodeType===9&&z.documentElement&&z.removeChild(z.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(Q){et(n,n.return,Q)}if(e=n.sibling,e!==null){e.return=n.return,ee=e;break}ee=n.return}return te=Qd,Qd=!1,te}function li(e,n,o){var s=n.updateQueue;if(s=s!==null?s.lastEffect:null,s!==null){var u=s=s.next;do{if((u.tag&e)===e){var d=u.destroy;u.destroy=void 0,d!==void 0&&xa(n,o,d)}u=u.next}while(u!==s)}}function El(e,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var o=n=n.next;do{if((o.tag&e)===e){var s=o.create;o.destroy=s()}o=o.next}while(o!==n)}}function Ca(e){var n=e.ref;if(n!==null){var o=e.stateNode;switch(e.tag){case 5:e=o;break;default:e=o}typeof n=="function"?n(e):n.current=e}}function Wd(e){var n=e.alternate;n!==null&&(e.alternate=null,Wd(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&(delete n[pn],delete n[Ko],delete n[$s],delete n[Sg],delete n[xg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function qd(e){return e.tag===5||e.tag===3||e.tag===4}function Kd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||qd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ra(e,n,o){var s=e.tag;if(s===5||s===6)e=e.stateNode,n?o.nodeType===8?o.parentNode.insertBefore(e,n):o.insertBefore(e,n):(o.nodeType===8?(n=o.parentNode,n.insertBefore(e,o)):(n=o,n.appendChild(e)),o=o._reactRootContainer,o!=null||n.onclick!==null||(n.onclick=tl));else if(s!==4&&(e=e.child,e!==null))for(Ra(e,n,o),e=e.sibling;e!==null;)Ra(e,n,o),e=e.sibling}function Ea(e,n,o){var s=e.tag;if(s===5||s===6)e=e.stateNode,n?o.insertBefore(e,n):o.appendChild(e);else if(s!==4&&(e=e.child,e!==null))for(Ea(e,n,o),e=e.sibling;e!==null;)Ea(e,n,o),e=e.sibling}var dt=null,sn=!1;function Jn(e,n,o){for(o=o.child;o!==null;)Yd(e,n,o),o=o.sibling}function Yd(e,n,o){if(kt&&typeof kt.onCommitFiberUnmount=="function")try{kt.onCommitFiberUnmount(En,o)}catch{}switch(o.tag){case 5:vt||po(o,n);case 6:var s=dt,u=sn;dt=null,Jn(e,n,o),dt=s,sn=u,dt!==null&&(sn?(e=dt,o=o.stateNode,e.nodeType===8?e.parentNode.removeChild(o):e.removeChild(o)):dt.removeChild(o.stateNode));break;case 18:dt!==null&&(sn?(e=dt,o=o.stateNode,e.nodeType===8?Is(e.parentNode,o):e.nodeType===1&&Is(e,o),$o(e)):Is(dt,o.stateNode));break;case 4:s=dt,u=sn,dt=o.stateNode.containerInfo,sn=!0,Jn(e,n,o),dt=s,sn=u;break;case 0:case 11:case 14:case 15:if(!vt&&(s=o.updateQueue,s!==null&&(s=s.lastEffect,s!==null))){u=s=s.next;do{var d=u,g=d.destroy;d=d.tag,g!==void 0&&((d&2)!==0||(d&4)!==0)&&xa(o,n,g),u=u.next}while(u!==s)}Jn(e,n,o);break;case 1:if(!vt&&(po(o,n),s=o.stateNode,typeof s.componentWillUnmount=="function"))try{s.props=o.memoizedProps,s.state=o.memoizedState,s.componentWillUnmount()}catch(w){et(o,n,w)}Jn(e,n,o);break;case 21:Jn(e,n,o);break;case 22:o.mode&1?(vt=(s=vt)||o.memoizedState!==null,Jn(e,n,o),vt=s):Jn(e,n,o);break;default:Jn(e,n,o)}}function Xd(e){var n=e.updateQueue;if(n!==null){e.updateQueue=null;var o=e.stateNode;o===null&&(o=e.stateNode=new Ig),n.forEach(function(s){var u=Wg.bind(null,e,s);o.has(s)||(o.add(s),s.then(u,u))})}}function an(e,n){var o=n.deletions;if(o!==null)for(var s=0;s<o.length;s++){var u=o[s];try{var d=e,g=n,w=g;e:for(;w!==null;){switch(w.tag){case 5:dt=w.stateNode,sn=!1;break e;case 3:dt=w.stateNode.containerInfo,sn=!0;break e;case 4:dt=w.stateNode.containerInfo,sn=!0;break e}w=w.return}if(dt===null)throw Error(i(160));Yd(d,g,u),dt=null,sn=!1;var E=u.alternate;E!==null&&(E.return=null),u.return=null}catch(O){et(u,n,O)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)Zd(n,e),n=n.sibling}function Zd(e,n){var o=e.alternate,s=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(an(n,e),vn(e),s&4){try{li(3,e,e.return),El(3,e)}catch(re){et(e,e.return,re)}try{li(5,e,e.return)}catch(re){et(e,e.return,re)}}break;case 1:an(n,e),vn(e),s&512&&o!==null&&po(o,o.return);break;case 5:if(an(n,e),vn(e),s&512&&o!==null&&po(o,o.return),e.flags&32){var u=e.stateNode;try{Sr(u,"")}catch(re){et(e,e.return,re)}}if(s&4&&(u=e.stateNode,u!=null)){var d=e.memoizedProps,g=o!==null?o.memoizedProps:d,w=e.type,E=e.updateQueue;if(e.updateQueue=null,E!==null)try{w==="input"&&d.type==="radio"&&d.name!=null&&Di(u,d),In(w,g);var O=In(w,d);for(g=0;g<E.length;g+=2){var V=E[g],U=E[g+1];V==="style"?Mo(u,U):V==="dangerouslySetInnerHTML"?Qr(u,U):V==="children"?Sr(u,U):A(u,V,U,O)}switch(w){case"input":Br(u,d);break;case"textarea":ko(u,d);break;case"select":var b=u._wrapperState.wasMultiple;u._wrapperState.wasMultiple=!!d.multiple;var Y=d.value;Y!=null?zn(u,!!d.multiple,Y,!1):b!==!!d.multiple&&(d.defaultValue!=null?zn(u,!!d.multiple,d.defaultValue,!0):zn(u,!!d.multiple,d.multiple?[]:"",!1))}u[Ko]=d}catch(re){et(e,e.return,re)}}break;case 6:if(an(n,e),vn(e),s&4){if(e.stateNode===null)throw Error(i(162));u=e.stateNode,d=e.memoizedProps;try{u.nodeValue=d}catch(re){et(e,e.return,re)}}break;case 3:if(an(n,e),vn(e),s&4&&o!==null&&o.memoizedState.isDehydrated)try{$o(n.containerInfo)}catch(re){et(e,e.return,re)}break;case 4:an(n,e),vn(e);break;case 13:an(n,e),vn(e),u=e.child,u.flags&8192&&(d=u.memoizedState!==null,u.stateNode.isHidden=d,!d||u.alternate!==null&&u.alternate.memoizedState!==null||(_a=Ue())),s&4&&Xd(e);break;case 22:if(V=o!==null&&o.memoizedState!==null,e.mode&1?(vt=(O=vt)||V,an(n,e),vt=O):an(n,e),vn(e),s&8192){if(O=e.memoizedState!==null,(e.stateNode.isHidden=O)&&!V&&(e.mode&1)!==0)for(ee=e,V=e.child;V!==null;){for(U=ee=V;ee!==null;){switch(b=ee,Y=b.child,b.tag){case 0:case 11:case 14:case 15:li(4,b,b.return);break;case 1:po(b,b.return);var te=b.stateNode;if(typeof te.componentWillUnmount=="function"){s=b,o=b.return;try{n=s,te.props=n.memoizedProps,te.state=n.memoizedState,te.componentWillUnmount()}catch(re){et(s,o,re)}}break;case 5:po(b,b.return);break;case 22:if(b.memoizedState!==null){tf(U);continue}}Y!==null?(Y.return=b,ee=Y):tf(U)}V=V.sibling}e:for(V=null,U=e;;){if(U.tag===5){if(V===null){V=U;try{u=U.stateNode,O?(d=u.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(w=U.stateNode,E=U.memoizedProps.style,g=E!=null&&E.hasOwnProperty("display")?E.display:null,w.style.display=_o("display",g))}catch(re){et(e,e.return,re)}}}else if(U.tag===6){if(V===null)try{U.stateNode.nodeValue=O?"":U.memoizedProps}catch(re){et(e,e.return,re)}}else if((U.tag!==22&&U.tag!==23||U.memoizedState===null||U===e)&&U.child!==null){U.child.return=U,U=U.child;continue}if(U===e)break e;for(;U.sibling===null;){if(U.return===null||U.return===e)break e;V===U&&(V=null),U=U.return}V===U&&(V=null),U.sibling.return=U.return,U=U.sibling}}break;case 19:an(n,e),vn(e),s&4&&Xd(e);break;case 21:break;default:an(n,e),vn(e)}}function vn(e){var n=e.flags;if(n&2){try{e:{for(var o=e.return;o!==null;){if(qd(o)){var s=o;break e}o=o.return}throw Error(i(160))}switch(s.tag){case 5:var u=s.stateNode;s.flags&32&&(Sr(u,""),s.flags&=-33);var d=Kd(e);Ea(e,d,u);break;case 3:case 4:var g=s.stateNode.containerInfo,w=Kd(e);Ra(e,w,g);break;default:throw Error(i(161))}}catch(E){et(e,e.return,E)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function Ag(e,n,o){ee=e,Jd(e)}function Jd(e,n,o){for(var s=(e.mode&1)!==0;ee!==null;){var u=ee,d=u.child;if(u.tag===22&&s){var g=u.memoizedState!==null||Rl;if(!g){var w=u.alternate,E=w!==null&&w.memoizedState!==null||vt;w=Rl;var O=vt;if(Rl=g,(vt=E)&&!O)for(ee=u;ee!==null;)g=ee,E=g.child,g.tag===22&&g.memoizedState!==null?nf(u):E!==null?(E.return=g,ee=E):nf(u);for(;d!==null;)ee=d,Jd(d),d=d.sibling;ee=u,Rl=w,vt=O}ef(e)}else(u.subtreeFlags&8772)!==0&&d!==null?(d.return=u,ee=d):ef(e)}}function ef(e){for(;ee!==null;){var n=ee;if((n.flags&8772)!==0){var o=n.alternate;try{if((n.flags&8772)!==0)switch(n.tag){case 0:case 11:case 15:vt||El(5,n);break;case 1:var s=n.stateNode;if(n.flags&4&&!vt)if(o===null)s.componentDidMount();else{var u=n.elementType===n.type?o.memoizedProps:ln(n.type,o.memoizedProps);s.componentDidUpdate(u,o.memoizedState,s.__reactInternalSnapshotBeforeUpdate)}var d=n.updateQueue;d!==null&&td(n,d,s);break;case 3:var g=n.updateQueue;if(g!==null){if(o=null,n.child!==null)switch(n.child.tag){case 5:o=n.child.stateNode;break;case 1:o=n.child.stateNode}td(n,g,o)}break;case 5:var w=n.stateNode;if(o===null&&n.flags&4){o=w;var E=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":E.autoFocus&&o.focus();break;case"img":E.src&&(o.src=E.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var O=n.alternate;if(O!==null){var V=O.memoizedState;if(V!==null){var U=V.dehydrated;U!==null&&$o(U)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}vt||n.flags&512&&Ca(n)}catch(b){et(n,n.return,b)}}if(n===e){ee=null;break}if(o=n.sibling,o!==null){o.return=n.return,ee=o;break}ee=n.return}}function tf(e){for(;ee!==null;){var n=ee;if(n===e){ee=null;break}var o=n.sibling;if(o!==null){o.return=n.return,ee=o;break}ee=n.return}}function nf(e){for(;ee!==null;){var n=ee;try{switch(n.tag){case 0:case 11:case 15:var o=n.return;try{El(4,n)}catch(E){et(n,o,E)}break;case 1:var s=n.stateNode;if(typeof s.componentDidMount=="function"){var u=n.return;try{s.componentDidMount()}catch(E){et(n,u,E)}}var d=n.return;try{Ca(n)}catch(E){et(n,d,E)}break;case 5:var g=n.return;try{Ca(n)}catch(E){et(n,g,E)}}}catch(E){et(n,n.return,E)}if(n===e){ee=null;break}var w=n.sibling;if(w!==null){w.return=n.return,ee=w;break}ee=n.return}}var bg=Math.ceil,Pl=K.ReactCurrentDispatcher,Pa=K.ReactCurrentOwner,Wt=K.ReactCurrentBatchConfig,ze=0,ut=null,nt=null,ft=0,At=0,go=qn(0),lt=0,si=null,Tr=0,kl=0,ka=0,ai=null,Lt=null,_a=0,mo=1/0,Dn=null,_l=!1,Ma=null,er=null,Ml=!1,tr=null,Fl=0,ui=0,Fa=null,Ll=-1,Nl=0;function xt(){return(ze&6)!==0?Ue():Ll!==-1?Ll:Ll=Ue()}function nr(e){return(e.mode&1)===0?1:(ze&2)!==0&&ft!==0?ft&-ft:Rg.transition!==null?(Nl===0&&(Nl=Ku()),Nl):(e=be,e!==0||(e=window.event,e=e===void 0?16:oc(e.type)),e)}function un(e,n,o,s){if(50<ui)throw ui=0,Fa=null,Error(i(185));To(e,o,s),((ze&2)===0||e!==ut)&&(e===ut&&((ze&2)===0&&(kl|=o),lt===4&&rr(e,ft)),Nt(e,s),o===1&&ze===0&&(n.mode&1)===0&&(mo=Ue()+500,il&&Yn()))}function Nt(e,n){var o=e.callbackNode;Rp(e,n);var s=bi(e,e===ut?ft:0);if(s===0)o!==null&&bn(o),e.callbackNode=null,e.callbackPriority=0;else if(n=s&-s,e.callbackPriority!==n){if(o!=null&&bn(o),n===1)e.tag===0?Cg(of.bind(null,e)):Hc(of.bind(null,e)),yg(function(){(ze&6)===0&&Yn()}),o=null;else{switch(Yu(s)){case 1:o=tn;break;case 4:o=Pr;break;case 16:o=je;break;case 536870912:o=nn;break;default:o=je}o=hf(o,rf.bind(null,e))}e.callbackPriority=n,e.callbackNode=o}}function rf(e,n){if(Ll=-1,Nl=0,(ze&6)!==0)throw Error(i(327));var o=e.callbackNode;if(vo()&&e.callbackNode!==o)return null;var s=bi(e,e===ut?ft:0);if(s===0)return null;if((s&30)!==0||(s&e.expiredLanes)!==0||n)n=Dl(e,s);else{n=s;var u=ze;ze|=2;var d=sf();(ut!==e||ft!==n)&&(Dn=null,mo=Ue()+500,zr(e,n));do try{Hg();break}catch(w){lf(e,w)}while(!0);Ws(),Pl.current=d,ze=u,nt!==null?n=0:(ut=null,ft=0,n=lt)}if(n!==0){if(n===2&&(u=us(e),u!==0&&(s=u,n=La(e,u))),n===1)throw o=si,zr(e,0),rr(e,s),Nt(e,Ue()),o;if(n===6)rr(e,s);else{if(u=e.current.alternate,(s&30)===0&&!Vg(u)&&(n=Dl(e,s),n===2&&(d=us(e),d!==0&&(s=d,n=La(e,d))),n===1))throw o=si,zr(e,0),rr(e,s),Nt(e,Ue()),o;switch(e.finishedWork=u,e.finishedLanes=s,n){case 0:case 1:throw Error(i(345));case 2:Or(e,Lt,Dn);break;case 3:if(rr(e,s),(s&130023424)===s&&(n=_a+500-Ue(),10<n)){if(bi(e,0)!==0)break;if(u=e.suspendedLanes,(u&s)!==s){xt(),e.pingedLanes|=e.suspendedLanes&u;break}e.timeoutHandle=Os(Or.bind(null,e,Lt,Dn),n);break}Or(e,Lt,Dn);break;case 4:if(rr(e,s),(s&4194240)===s)break;for(n=e.eventTimes,u=-1;0<s;){var g=31-st(s);d=1<<g,g=n[g],g>u&&(u=g),s&=~d}if(s=u,s=Ue()-s,s=(120>s?120:480>s?480:1080>s?1080:1920>s?1920:3e3>s?3e3:4320>s?4320:1960*bg(s/1960))-s,10<s){e.timeoutHandle=Os(Or.bind(null,e,Lt,Dn),s);break}Or(e,Lt,Dn);break;case 5:Or(e,Lt,Dn);break;default:throw Error(i(329))}}}return Nt(e,Ue()),e.callbackNode===o?rf.bind(null,e):null}function La(e,n){var o=ai;return e.current.memoizedState.isDehydrated&&(zr(e,n).flags|=256),e=Dl(e,n),e!==2&&(n=Lt,Lt=o,n!==null&&Na(n)),e}function Na(e){Lt===null?Lt=e:Lt.push.apply(Lt,e)}function Vg(e){for(var n=e;;){if(n.flags&16384){var o=n.updateQueue;if(o!==null&&(o=o.stores,o!==null))for(var s=0;s<o.length;s++){var u=o[s],d=u.getSnapshot;u=u.value;try{if(!rn(d(),u))return!1}catch{return!1}}}if(o=n.child,n.subtreeFlags&16384&&o!==null)o.return=n,n=o;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function rr(e,n){for(n&=~ka,n&=~kl,e.suspendedLanes|=n,e.pingedLanes&=~n,e=e.expirationTimes;0<n;){var o=31-st(n),s=1<<o;e[o]=-1,n&=~s}}function of(e){if((ze&6)!==0)throw Error(i(327));vo();var n=bi(e,0);if((n&1)===0)return Nt(e,Ue()),null;var o=Dl(e,n);if(e.tag!==0&&o===2){var s=us(e);s!==0&&(n=s,o=La(e,s))}if(o===1)throw o=si,zr(e,0),rr(e,n),Nt(e,Ue()),o;if(o===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=n,Or(e,Lt,Dn),Nt(e,Ue()),null}function Da(e,n){var o=ze;ze|=1;try{return e(n)}finally{ze=o,ze===0&&(mo=Ue()+500,il&&Yn())}}function jr(e){tr!==null&&tr.tag===0&&(ze&6)===0&&vo();var n=ze;ze|=1;var o=Wt.transition,s=be;try{if(Wt.transition=null,be=1,e)return e()}finally{be=s,Wt.transition=o,ze=n,(ze&6)===0&&Yn()}}function Ta(){At=go.current,We(go)}function zr(e,n){e.finishedWork=null,e.finishedLanes=0;var o=e.timeoutHandle;if(o!==-1&&(e.timeoutHandle=-1,vg(o)),nt!==null)for(o=nt.return;o!==null;){var s=o;switch(Us(s),s.tag){case 1:s=s.type.childContextTypes,s!=null&&rl();break;case 3:fo(),We(_t),We(pt),ta();break;case 5:Js(s);break;case 4:fo();break;case 13:We(Ye);break;case 19:We(Ye);break;case 10:qs(s.type._context);break;case 22:case 23:Ta()}o=o.return}if(ut=e,nt=e=or(e.current,null),ft=At=n,lt=0,si=null,ka=kl=Tr=0,Lt=ai=null,Lr!==null){for(n=0;n<Lr.length;n++)if(o=Lr[n],s=o.interleaved,s!==null){o.interleaved=null;var u=s.next,d=o.pending;if(d!==null){var g=d.next;d.next=u,s.next=g}o.pending=s}Lr=null}return e}function lf(e,n){do{var o=nt;try{if(Ws(),gl.current=wl,ml){for(var s=Xe.memoizedState;s!==null;){var u=s.queue;u!==null&&(u.pending=null),s=s.next}ml=!1}if(Dr=0,at=it=Xe=null,ti=!1,ni=0,Pa.current=null,o===null||o.return===null){lt=1,si=n,nt=null;break}e:{var d=e,g=o.return,w=o,E=n;if(n=ft,w.flags|=32768,E!==null&&typeof E=="object"&&typeof E.then=="function"){var O=E,V=w,U=V.tag;if((V.mode&1)===0&&(U===0||U===11||U===15)){var b=V.alternate;b?(V.updateQueue=b.updateQueue,V.memoizedState=b.memoizedState,V.lanes=b.lanes):(V.updateQueue=null,V.memoizedState=null)}var Y=Ld(g);if(Y!==null){Y.flags&=-257,Nd(Y,g,w,d,n),Y.mode&1&&Fd(d,O,n),n=Y,E=O;var te=n.updateQueue;if(te===null){var re=new Set;re.add(E),n.updateQueue=re}else te.add(E);break e}else{if((n&1)===0){Fd(d,O,n),ja();break e}E=Error(i(426))}}else if(Ke&&w.mode&1){var tt=Ld(g);if(tt!==null){(tt.flags&65536)===0&&(tt.flags|=256),Nd(tt,g,w,d,n),Gs(ho(E,w));break e}}d=E=ho(E,w),lt!==4&&(lt=2),ai===null?ai=[d]:ai.push(d),d=g;do{switch(d.tag){case 3:d.flags|=65536,n&=-n,d.lanes|=n;var D=_d(d,E,n);ed(d,D);break e;case 1:w=E;var k=d.type,z=d.stateNode;if((d.flags&128)===0&&(typeof k.getDerivedStateFromError=="function"||z!==null&&typeof z.componentDidCatch=="function"&&(er===null||!er.has(z)))){d.flags|=65536,n&=-n,d.lanes|=n;var Q=Md(d,w,n);ed(d,Q);break e}}d=d.return}while(d!==null)}uf(o)}catch(oe){n=oe,nt===o&&o!==null&&(nt=o=o.return);continue}break}while(!0)}function sf(){var e=Pl.current;return Pl.current=wl,e===null?wl:e}function ja(){(lt===0||lt===3||lt===2)&&(lt=4),ut===null||(Tr&268435455)===0&&(kl&268435455)===0||rr(ut,ft)}function Dl(e,n){var o=ze;ze|=2;var s=sf();(ut!==e||ft!==n)&&(Dn=null,zr(e,n));do try{Ug();break}catch(u){lf(e,u)}while(!0);if(Ws(),ze=o,Pl.current=s,nt!==null)throw Error(i(261));return ut=null,ft=0,lt}function Ug(){for(;nt!==null;)af(nt)}function Hg(){for(;nt!==null&&!Et();)af(nt)}function af(e){var n=ff(e.alternate,e,At);e.memoizedProps=e.pendingProps,n===null?uf(e):nt=n,Pa.current=null}function uf(e){var n=e;do{var o=n.alternate;if(e=n.return,(n.flags&32768)===0){if(o=zg(o,n,At),o!==null){nt=o;return}}else{if(o=Og(o,n),o!==null){o.flags&=32767,nt=o;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{lt=6,nt=null;return}}if(n=n.sibling,n!==null){nt=n;return}nt=n=e}while(n!==null);lt===0&&(lt=5)}function Or(e,n,o){var s=be,u=Wt.transition;try{Wt.transition=null,be=1,Bg(e,n,o,s)}finally{Wt.transition=u,be=s}return null}function Bg(e,n,o,s){do vo();while(tr!==null);if((ze&6)!==0)throw Error(i(327));o=e.finishedWork;var u=e.finishedLanes;if(o===null)return null;if(e.finishedWork=null,e.finishedLanes=0,o===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var d=o.lanes|o.childLanes;if(Ep(e,d),e===ut&&(nt=ut=null,ft=0),(o.subtreeFlags&2064)===0&&(o.flags&2064)===0||Ml||(Ml=!0,hf(je,function(){return vo(),null})),d=(o.flags&15990)!==0,(o.subtreeFlags&15990)!==0||d){d=Wt.transition,Wt.transition=null;var g=be;be=1;var w=ze;ze|=4,Pa.current=null,$g(e,o),Zd(o,e),cg(js),Hi=!!Ts,js=Ts=null,e.current=o,Ag(o),Er(),ze=w,be=g,Wt.transition=d}else e.current=o;if(Ml&&(Ml=!1,tr=e,Fl=u),d=e.pendingLanes,d===0&&(er=null),Je(o.stateNode),Nt(e,Ue()),n!==null)for(s=e.onRecoverableError,o=0;o<n.length;o++)u=n[o],s(u.value,{componentStack:u.stack,digest:u.digest});if(_l)throw _l=!1,e=Ma,Ma=null,e;return(Fl&1)!==0&&e.tag!==0&&vo(),d=e.pendingLanes,(d&1)!==0?e===Fa?ui++:(ui=0,Fa=e):ui=0,Yn(),null}function vo(){if(tr!==null){var e=Yu(Fl),n=Wt.transition,o=be;try{if(Wt.transition=null,be=16>e?16:e,tr===null)var s=!1;else{if(e=tr,tr=null,Fl=0,(ze&6)!==0)throw Error(i(331));var u=ze;for(ze|=4,ee=e.current;ee!==null;){var d=ee,g=d.child;if((ee.flags&16)!==0){var w=d.deletions;if(w!==null){for(var E=0;E<w.length;E++){var O=w[E];for(ee=O;ee!==null;){var V=ee;switch(V.tag){case 0:case 11:case 15:li(8,V,d)}var U=V.child;if(U!==null)U.return=V,ee=U;else for(;ee!==null;){V=ee;var b=V.sibling,Y=V.return;if(Wd(V),V===O){ee=null;break}if(b!==null){b.return=Y,ee=b;break}ee=Y}}}var te=d.alternate;if(te!==null){var re=te.child;if(re!==null){te.child=null;do{var tt=re.sibling;re.sibling=null,re=tt}while(re!==null)}}ee=d}}if((d.subtreeFlags&2064)!==0&&g!==null)g.return=d,ee=g;else e:for(;ee!==null;){if(d=ee,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:li(9,d,d.return)}var D=d.sibling;if(D!==null){D.return=d.return,ee=D;break e}ee=d.return}}var k=e.current;for(ee=k;ee!==null;){g=ee;var z=g.child;if((g.subtreeFlags&2064)!==0&&z!==null)z.return=g,ee=z;else e:for(g=k;ee!==null;){if(w=ee,(w.flags&2048)!==0)try{switch(w.tag){case 0:case 11:case 15:El(9,w)}}catch(oe){et(w,w.return,oe)}if(w===g){ee=null;break e}var Q=w.sibling;if(Q!==null){Q.return=w.return,ee=Q;break e}ee=w.return}}if(ze=u,Yn(),kt&&typeof kt.onPostCommitFiberRoot=="function")try{kt.onPostCommitFiberRoot(En,e)}catch{}s=!0}return s}finally{be=o,Wt.transition=n}}return!1}function cf(e,n,o){n=ho(o,n),n=_d(e,n,1),e=Zn(e,n,1),n=xt(),e!==null&&(To(e,1,n),Nt(e,n))}function et(e,n,o){if(e.tag===3)cf(e,e,o);else for(;n!==null;){if(n.tag===3){cf(n,e,o);break}else if(n.tag===1){var s=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof s.componentDidCatch=="function"&&(er===null||!er.has(s))){e=ho(o,e),e=Md(n,e,1),n=Zn(n,e,1),e=xt(),n!==null&&(To(n,1,e),Nt(n,e));break}}n=n.return}}function Gg(e,n,o){var s=e.pingCache;s!==null&&s.delete(n),n=xt(),e.pingedLanes|=e.suspendedLanes&o,ut===e&&(ft&o)===o&&(lt===4||lt===3&&(ft&130023424)===ft&&500>Ue()-_a?zr(e,0):ka|=o),Nt(e,n)}function df(e,n){n===0&&((e.mode&1)===0?n=1:(n=Ai,Ai<<=1,(Ai&130023424)===0&&(Ai=4194304)));var o=xt();e=Fn(e,n),e!==null&&(To(e,n,o),Nt(e,o))}function Qg(e){var n=e.memoizedState,o=0;n!==null&&(o=n.retryLane),df(e,o)}function Wg(e,n){var o=0;switch(e.tag){case 13:var s=e.stateNode,u=e.memoizedState;u!==null&&(o=u.retryLane);break;case 19:s=e.stateNode;break;default:throw Error(i(314))}s!==null&&s.delete(n),df(e,o)}var ff;ff=function(e,n,o){if(e!==null)if(e.memoizedProps!==n.pendingProps||_t.current)Ft=!0;else{if((e.lanes&o)===0&&(n.flags&128)===0)return Ft=!1,jg(e,n,o);Ft=(e.flags&131072)!==0}else Ft=!1,Ke&&(n.flags&1048576)!==0&&Bc(n,sl,n.index);switch(n.lanes=0,n.tag){case 2:var s=n.type;Cl(e,n),e=n.pendingProps;var u=oo(n,pt.current);co(n,o),u=oa(null,n,s,e,u,o);var d=ia();return n.flags|=1,typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,Mt(s)?(d=!0,ol(n)):d=!1,n.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,Xs(n),u.updater=Sl,n.stateNode=u,u._reactInternals=n,da(n,s,e,o),n=ga(null,n,s,!0,d,o)):(n.tag=0,Ke&&d&&Vs(n),St(null,n,u,o),n=n.child),n;case 16:s=n.elementType;e:{switch(Cl(e,n),e=n.pendingProps,u=s._init,s=u(s._payload),n.type=s,u=n.tag=Kg(s),e=ln(s,e),u){case 0:n=pa(null,n,s,e,o);break e;case 1:n=Id(null,n,s,e,o);break e;case 11:n=Dd(null,n,s,e,o);break e;case 14:n=Td(null,n,s,ln(s.type,e),o);break e}throw Error(i(306,s,""))}return n;case 0:return s=n.type,u=n.pendingProps,u=n.elementType===s?u:ln(s,u),pa(e,n,s,u,o);case 1:return s=n.type,u=n.pendingProps,u=n.elementType===s?u:ln(s,u),Id(e,n,s,u,o);case 3:e:{if($d(n),e===null)throw Error(i(387));s=n.pendingProps,d=n.memoizedState,u=d.element,Jc(e,n),hl(n,s,null,o);var g=n.memoizedState;if(s=g.element,d.isDehydrated)if(d={element:s,isDehydrated:!1,cache:g.cache,pendingSuspenseBoundaries:g.pendingSuspenseBoundaries,transitions:g.transitions},n.updateQueue.baseState=d,n.memoizedState=d,n.flags&256){u=ho(Error(i(423)),n),n=Ad(e,n,s,o,u);break e}else if(s!==u){u=ho(Error(i(424)),n),n=Ad(e,n,s,o,u);break e}else for($t=Wn(n.stateNode.containerInfo.firstChild),It=n,Ke=!0,on=null,o=Xc(n,null,s,o),n.child=o;o;)o.flags=o.flags&-3|4096,o=o.sibling;else{if(so(),s===u){n=Nn(e,n,o);break e}St(e,n,s,o)}n=n.child}return n;case 5:return nd(n),e===null&&Bs(n),s=n.type,u=n.pendingProps,d=e!==null?e.memoizedProps:null,g=u.children,zs(s,u)?g=null:d!==null&&zs(s,d)&&(n.flags|=32),Od(e,n),St(e,n,g,o),n.child;case 6:return e===null&&Bs(n),null;case 13:return bd(e,n,o);case 4:return Zs(n,n.stateNode.containerInfo),s=n.pendingProps,e===null?n.child=ao(n,null,s,o):St(e,n,s,o),n.child;case 11:return s=n.type,u=n.pendingProps,u=n.elementType===s?u:ln(s,u),Dd(e,n,s,u,o);case 7:return St(e,n,n.pendingProps,o),n.child;case 8:return St(e,n,n.pendingProps.children,o),n.child;case 12:return St(e,n,n.pendingProps.children,o),n.child;case 10:e:{if(s=n.type._context,u=n.pendingProps,d=n.memoizedProps,g=u.value,Ge(cl,s._currentValue),s._currentValue=g,d!==null)if(rn(d.value,g)){if(d.children===u.children&&!_t.current){n=Nn(e,n,o);break e}}else for(d=n.child,d!==null&&(d.return=n);d!==null;){var w=d.dependencies;if(w!==null){g=d.child;for(var E=w.firstContext;E!==null;){if(E.context===s){if(d.tag===1){E=Ln(-1,o&-o),E.tag=2;var O=d.updateQueue;if(O!==null){O=O.shared;var V=O.pending;V===null?E.next=E:(E.next=V.next,V.next=E),O.pending=E}}d.lanes|=o,E=d.alternate,E!==null&&(E.lanes|=o),Ks(d.return,o,n),w.lanes|=o;break}E=E.next}}else if(d.tag===10)g=d.type===n.type?null:d.child;else if(d.tag===18){if(g=d.return,g===null)throw Error(i(341));g.lanes|=o,w=g.alternate,w!==null&&(w.lanes|=o),Ks(g,o,n),g=d.sibling}else g=d.child;if(g!==null)g.return=d;else for(g=d;g!==null;){if(g===n){g=null;break}if(d=g.sibling,d!==null){d.return=g.return,g=d;break}g=g.return}d=g}St(e,n,u.children,o),n=n.child}return n;case 9:return u=n.type,s=n.pendingProps.children,co(n,o),u=Gt(u),s=s(u),n.flags|=1,St(e,n,s,o),n.child;case 14:return s=n.type,u=ln(s,n.pendingProps),u=ln(s.type,u),Td(e,n,s,u,o);case 15:return jd(e,n,n.type,n.pendingProps,o);case 17:return s=n.type,u=n.pendingProps,u=n.elementType===s?u:ln(s,u),Cl(e,n),n.tag=1,Mt(s)?(e=!0,ol(n)):e=!1,co(n,o),Pd(n,s,u),da(n,s,u,o),ga(null,n,s,!0,e,o);case 19:return Ud(e,n,o);case 22:return zd(e,n,o)}throw Error(i(156,n.tag))};function hf(e,n){return hn(e,n)}function qg(e,n,o,s){this.tag=e,this.key=o,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=s,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function qt(e,n,o,s){return new qg(e,n,o,s)}function za(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Kg(e){if(typeof e=="function")return za(e)?1:0;if(e!=null){if(e=e.$$typeof,e===xe)return 11;if(e===He)return 14}return 2}function or(e,n){var o=e.alternate;return o===null?(o=qt(e.tag,n,e.key,e.mode),o.elementType=e.elementType,o.type=e.type,o.stateNode=e.stateNode,o.alternate=e,e.alternate=o):(o.pendingProps=n,o.type=e.type,o.flags=0,o.subtreeFlags=0,o.deletions=null),o.flags=e.flags&14680064,o.childLanes=e.childLanes,o.lanes=e.lanes,o.child=e.child,o.memoizedProps=e.memoizedProps,o.memoizedState=e.memoizedState,o.updateQueue=e.updateQueue,n=e.dependencies,o.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},o.sibling=e.sibling,o.index=e.index,o.ref=e.ref,o}function Tl(e,n,o,s,u,d){var g=2;if(s=e,typeof e=="function")za(e)&&(g=1);else if(typeof e=="string")g=5;else e:switch(e){case ie:return Ir(o.children,u,d,n);case se:g=8,u|=8;break;case ue:return e=qt(12,o,n,u|2),e.elementType=ue,e.lanes=d,e;case Re:return e=qt(13,o,n,u),e.elementType=Re,e.lanes=d,e;case Pe:return e=qt(19,o,n,u),e.elementType=Pe,e.lanes=d,e;case _e:return jl(o,u,d,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ce:g=10;break e;case Ie:g=9;break e;case xe:g=11;break e;case He:g=14;break e;case pe:g=16,s=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return n=qt(g,o,n,u),n.elementType=e,n.type=s,n.lanes=d,n}function Ir(e,n,o,s){return e=qt(7,e,s,n),e.lanes=o,e}function jl(e,n,o,s){return e=qt(22,e,s,n),e.elementType=_e,e.lanes=o,e.stateNode={isHidden:!1},e}function Oa(e,n,o){return e=qt(6,e,null,n),e.lanes=o,e}function Ia(e,n,o){return n=qt(4,e.children!==null?e.children:[],e.key,n),n.lanes=o,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function Yg(e,n,o,s,u){this.tag=n,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=cs(0),this.expirationTimes=cs(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=cs(0),this.identifierPrefix=s,this.onRecoverableError=u,this.mutableSourceEagerHydrationData=null}function $a(e,n,o,s,u,d,g,w,E){return e=new Yg(e,n,o,w,E),n===1?(n=1,d===!0&&(n|=8)):n=0,d=qt(3,null,null,n),e.current=d,d.stateNode=e,d.memoizedState={element:s,isDehydrated:o,cache:null,transitions:null,pendingSuspenseBoundaries:null},Xs(d),e}function Xg(e,n,o){var s=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:j,key:s==null?null:""+s,children:e,containerInfo:n,implementation:o}}function pf(e){if(!e)return Kn;e=e._reactInternals;e:{if(Te(e)!==e||e.tag!==1)throw Error(i(170));var n=e;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(Mt(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(i(171))}if(e.tag===1){var o=e.type;if(Mt(o))return Vc(e,o,n)}return n}function gf(e,n,o,s,u,d,g,w,E){return e=$a(o,s,!0,e,u,d,g,w,E),e.context=pf(null),o=e.current,s=xt(),u=nr(o),d=Ln(s,u),d.callback=n??null,Zn(o,d,u),e.current.lanes=u,To(e,u,s),Nt(e,s),e}function zl(e,n,o,s){var u=n.current,d=xt(),g=nr(u);return o=pf(o),n.context===null?n.context=o:n.pendingContext=o,n=Ln(d,g),n.payload={element:e},s=s===void 0?null:s,s!==null&&(n.callback=s),e=Zn(u,n,g),e!==null&&(un(e,u,g,d),fl(e,u,g)),g}function Ol(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function mf(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var o=e.retryLane;e.retryLane=o!==0&&o<n?o:n}}function Aa(e,n){mf(e,n),(e=e.alternate)&&mf(e,n)}function Zg(){return null}var vf=typeof reportError=="function"?reportError:function(e){console.error(e)};function ba(e){this._internalRoot=e}Il.prototype.render=ba.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(i(409));zl(e,n,null,null)},Il.prototype.unmount=ba.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;jr(function(){zl(null,e,null,null)}),n[Pn]=null}};function Il(e){this._internalRoot=e}Il.prototype.unstable_scheduleHydration=function(e){if(e){var n=Ju();e={blockedOn:null,target:e,priority:n};for(var o=0;o<Bn.length&&n!==0&&n<Bn[o].priority;o++);Bn.splice(o,0,e),o===0&&nc(e)}};function Va(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function yf(){}function Jg(e,n,o,s,u){if(u){if(typeof s=="function"){var d=s;s=function(){var O=Ol(g);d.call(O)}}var g=gf(n,s,e,0,null,!1,!1,"",yf);return e._reactRootContainer=g,e[Pn]=g.current,Wo(e.nodeType===8?e.parentNode:e),jr(),g}for(;u=e.lastChild;)e.removeChild(u);if(typeof s=="function"){var w=s;s=function(){var O=Ol(E);w.call(O)}}var E=$a(e,0,!1,null,null,!1,!1,"",yf);return e._reactRootContainer=E,e[Pn]=E.current,Wo(e.nodeType===8?e.parentNode:e),jr(function(){zl(n,E,o,s)}),E}function Al(e,n,o,s,u){var d=o._reactRootContainer;if(d){var g=d;if(typeof u=="function"){var w=u;u=function(){var E=Ol(g);w.call(E)}}zl(n,g,e,u)}else g=Jg(o,n,e,u,s);return Ol(g)}Xu=function(e){switch(e.tag){case 3:var n=e.stateNode;if(n.current.memoizedState.isDehydrated){var o=Do(n.pendingLanes);o!==0&&(ds(n,o|1),Nt(n,Ue()),(ze&6)===0&&(mo=Ue()+500,Yn()))}break;case 13:jr(function(){var s=Fn(e,1);if(s!==null){var u=xt();un(s,e,1,u)}}),Aa(e,1)}},fs=function(e){if(e.tag===13){var n=Fn(e,134217728);if(n!==null){var o=xt();un(n,e,134217728,o)}Aa(e,134217728)}},Zu=function(e){if(e.tag===13){var n=nr(e),o=Fn(e,n);if(o!==null){var s=xt();un(o,e,n,s)}Aa(e,n)}},Ju=function(){return be},ec=function(e,n){var o=be;try{return be=e,n()}finally{be=o}},Fo=function(e,n,o){switch(n){case"input":if(Br(e,o),n=o.name,o.type==="radio"&&n!=null){for(o=e;o.parentNode;)o=o.parentNode;for(o=o.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<o.length;n++){var s=o[n];if(s!==e&&s.form===e.form){var u=nl(s);if(!u)throw Error(i(90));Eo(s),Br(s,u)}}}break;case"textarea":ko(e,o);break;case"select":n=o.value,n!=null&&zn(e,!!o.multiple,n,!1)}},zi=Da,Oi=jr;var em={usingClientEntryPoint:!1,Events:[Yo,no,nl,Cr,Rr,Da]},ci={findFiberByHostInstance:kr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},tm={bundleType:ci.bundleType,version:ci.version,rendererPackageName:ci.rendererPackageName,rendererConfig:ci.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:K.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=De(e),e===null?null:e.stateNode},findFiberByHostInstance:ci.findFiberByHostInstance||Zg,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var bl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!bl.isDisabled&&bl.supportsFiber)try{En=bl.inject(tm),kt=bl}catch{}}return Dt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=em,Dt.createPortal=function(e,n){var o=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Va(n))throw Error(i(200));return Xg(e,n,null,o)},Dt.createRoot=function(e,n){if(!Va(e))throw Error(i(299));var o=!1,s="",u=vf;return n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(u=n.onRecoverableError)),n=$a(e,1,!1,null,null,o,!1,s,u),e[Pn]=n.current,Wo(e.nodeType===8?e.parentNode:e),new ba(n)},Dt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=De(n),e=e===null?null:e.stateNode,e},Dt.flushSync=function(e){return jr(e)},Dt.hydrate=function(e,n,o){if(!$l(n))throw Error(i(200));return Al(null,e,n,!0,o)},Dt.hydrateRoot=function(e,n,o){if(!Va(e))throw Error(i(405));var s=o!=null&&o.hydratedSources||null,u=!1,d="",g=vf;if(o!=null&&(o.unstable_strictMode===!0&&(u=!0),o.identifierPrefix!==void 0&&(d=o.identifierPrefix),o.onRecoverableError!==void 0&&(g=o.onRecoverableError)),n=gf(n,null,e,1,o??null,u,!1,d,g),e[Pn]=n.current,Wo(e),s)for(e=0;e<s.length;e++)o=s[e],u=o._getVersion,u=u(o._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[o,u]:n.mutableSourceEagerHydrationData.push(o,u);return new Il(n)},Dt.render=function(e,n,o){if(!$l(n))throw Error(i(200));return Al(null,e,n,!1,o)},Dt.unmountComponentAtNode=function(e){if(!$l(e))throw Error(i(40));return e._reactRootContainer?(jr(function(){Al(null,null,e,!1,function(){e._reactRootContainer=null,e[Pn]=null})}),!0):!1},Dt.unstable_batchedUpdates=Da,Dt.unstable_renderSubtreeIntoContainer=function(e,n,o,s){if(!$l(o))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return Al(e,n,o,!1,s)},Dt.version="18.3.1-next-f1338f8080-20240426",Dt}var kf;function gh(){if(kf)return Ba.exports;kf=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}return t(),Ba.exports=um(),Ba.exports}var _f;function cm(){if(_f)return Vl;_f=1;var t=gh();return Vl.createRoot=t.createRoot,Vl.hydrateRoot=t.hydrateRoot,Vl}var dm=cm();const fm=ph(dm);var fi={},Mf;function hm(){if(Mf)return fi;Mf=1,Object.defineProperty(fi,"__esModule",{value:!0}),fi.parse=f,fi.serialize=m;const t=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,l=/^[\u0020-\u003A\u003D-\u007E]*$/,a=Object.prototype.toString,c=(()=>{const y=function(){};return y.prototype=Object.create(null),y})();function f(y,x){const C=new c,P=y.length;if(P<2)return C;const T=x?.decode||v;let F=0;do{const B=y.indexOf("=",F);if(B===-1)break;const A=y.indexOf(";",F),K=A===-1?P:A;if(B>K){F=y.lastIndexOf(";",B-1)+1;continue}const Z=h(y,F,B),j=p(y,B,Z),ie=y.slice(Z,j);if(C[ie]===void 0){let se=h(y,B+1,K),ue=p(y,K,se);const Ce=T(y.slice(se,ue));C[ie]=Ce}F=K+1}while(F<P);return C}function h(y,x,C){do{const P=y.charCodeAt(x);if(P!==32&&P!==9)return x}while(++x<C);return C}function p(y,x,C){for(;x>C;){const P=y.charCodeAt(--x);if(P!==32&&P!==9)return x+1}return C}function m(y,x,C){const P=C?.encode||encodeURIComponent;if(!t.test(y))throw new TypeError(`argument name is invalid: ${y}`);const T=P(x);if(!r.test(T))throw new TypeError(`argument val is invalid: ${x}`);let F=y+"="+T;if(!C)return F;if(C.maxAge!==void 0){if(!Number.isInteger(C.maxAge))throw new TypeError(`option maxAge is invalid: ${C.maxAge}`);F+="; Max-Age="+C.maxAge}if(C.domain){if(!i.test(C.domain))throw new TypeError(`option domain is invalid: ${C.domain}`);F+="; Domain="+C.domain}if(C.path){if(!l.test(C.path))throw new TypeError(`option path is invalid: ${C.path}`);F+="; Path="+C.path}if(C.expires){if(!S(C.expires)||!Number.isFinite(C.expires.valueOf()))throw new TypeError(`option expires is invalid: ${C.expires}`);F+="; Expires="+C.expires.toUTCString()}if(C.httpOnly&&(F+="; HttpOnly"),C.secure&&(F+="; Secure"),C.partitioned&&(F+="; Partitioned"),C.priority)switch(typeof C.priority=="string"?C.priority.toLowerCase():void 0){case"low":F+="; Priority=Low";break;case"medium":F+="; Priority=Medium";break;case"high":F+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${C.priority}`)}if(C.sameSite)switch(typeof C.sameSite=="string"?C.sameSite.toLowerCase():C.sameSite){case!0:case"strict":F+="; SameSite=Strict";break;case"lax":F+="; SameSite=Lax";break;case"none":F+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${C.sameSite}`)}return F}function v(y){if(y.indexOf("%")===-1)return y;try{return decodeURIComponent(y)}catch{return y}}function S(y){return a.call(y)==="[object Date]"}return fi}hm();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var mh=t=>{throw TypeError(t)},pm=(t,r,i)=>r.has(t)||mh("Cannot "+i),Wa=(t,r,i)=>(pm(t,r,"read from private field"),i?i.call(t):r.get(t)),gm=(t,r,i)=>r.has(t)?mh("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(t):r.set(t,i),Ff="popstate";function mm(t={}){function r(l,a){let{pathname:c,search:f,hash:h}=l.location;return Ri("",{pathname:c,search:f,hash:h},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function i(l,a){return typeof a=="string"?a:hr(a)}return ym(r,i,null,t)}function Ne(t,r){if(t===!1||t===null||typeof t>"u")throw new Error(r)}function rt(t,r){if(!t){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function vm(){return Math.random().toString(36).substring(2,10)}function Lf(t,r){return{usr:t.state,key:t.key,idx:r}}function Ri(t,r,i=null,l){return{pathname:typeof t=="string"?t:t.pathname,search:"",hash:"",...typeof r=="string"?mr(r):r,state:i,key:r&&r.key||l||vm()}}function hr({pathname:t="/",search:r="",hash:i=""}){return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),i&&i!=="#"&&(t+=i.charAt(0)==="#"?i:"#"+i),t}function mr(t){let r={};if(t){let i=t.indexOf("#");i>=0&&(r.hash=t.substring(i),t=t.substring(0,i));let l=t.indexOf("?");l>=0&&(r.search=t.substring(l),t=t.substring(0,l)),t&&(r.pathname=t)}return r}function ym(t,r,i,l={}){let{window:a=document.defaultView,v5Compat:c=!1}=l,f=a.history,h="POP",p=null,m=v();m==null&&(m=0,f.replaceState({...f.state,idx:m},""));function v(){return(f.state||{idx:null}).idx}function S(){h="POP";let T=v(),F=T==null?null:T-m;m=T,p&&p({action:h,location:P.location,delta:F})}function y(T,F){h="PUSH";let B=Ri(P.location,T,F);m=v()+1;let A=Lf(B,m),K=P.createHref(B);try{f.pushState(A,"",K)}catch(Z){if(Z instanceof DOMException&&Z.name==="DataCloneError")throw Z;a.location.assign(K)}c&&p&&p({action:h,location:P.location,delta:1})}function x(T,F){h="REPLACE";let B=Ri(P.location,T,F);m=v();let A=Lf(B,m),K=P.createHref(B);f.replaceState(A,"",K),c&&p&&p({action:h,location:P.location,delta:0})}function C(T){return vh(T)}let P={get action(){return h},get location(){return t(a,f)},listen(T){if(p)throw new Error("A history only accepts one active listener");return a.addEventListener(Ff,S),p=T,()=>{a.removeEventListener(Ff,S),p=null}},createHref(T){return r(a,T)},createURL:C,encodeLocation(T){let F=C(T);return{pathname:F.pathname,search:F.search,hash:F.hash}},push:y,replace:x,go(T){return f.go(T)}};return P}function vh(t,r=!1){let i="http://localhost";typeof window<"u"&&(i=window.location.origin!=="null"?window.location.origin:window.location.href),Ne(i,"No window.location.(origin|href) available to create URL");let l=typeof t=="string"?t:hr(t);return l=l.replace(/ $/,"%20"),!r&&l.startsWith("//")&&(l=i+l),new URL(l,i)}var Si,Nf=class{constructor(t){if(gm(this,Si,new Map),t)for(let[r,i]of t)this.set(r,i)}get(t){if(Wa(this,Si).has(t))return Wa(this,Si).get(t);if(t.defaultValue!==void 0)return t.defaultValue;throw new Error("No value found for context")}set(t,r){Wa(this,Si).set(t,r)}};Si=new WeakMap;var wm=new Set(["lazy","caseSensitive","path","id","index","children"]);function Sm(t){return wm.has(t)}var xm=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Cm(t){return xm.has(t)}function Rm(t){return t.index===!0}function Jl(t,r,i=[],l={}){return t.map((a,c)=>{let f=[...i,String(c)],h=typeof a.id=="string"?a.id:f.join("-");if(Ne(a.index!==!0||!a.children,"Cannot specify children on an index route"),Ne(!l[h],`Found a route id collision on id "${h}".  Route id's must be globally unique within Data Router usages`),Rm(a)){let p={...a,...r(a),id:h};return l[h]=p,p}else{let p={...a,...r(a),id:h,children:void 0};return l[h]=p,a.children&&(p.children=Jl(a.children,r,f,l)),p}})}function cr(t,r,i="/"){return ql(t,r,i,!1)}function ql(t,r,i,l){let a=typeof r=="string"?mr(r):r,c=Yt(a.pathname||"/",i);if(c==null)return null;let f=yh(t);Pm(f);let h=null;for(let p=0;h==null&&p<f.length;++p){let m=Om(c);h=jm(f[p],m,l)}return h}function Em(t,r){let{route:i,pathname:l,params:a}=t;return{id:i.id,pathname:l,params:a,data:r[i.id],handle:i.handle}}function yh(t,r=[],i=[],l=""){let a=(c,f,h)=>{let p={relativePath:h===void 0?c.path||"":h,caseSensitive:c.caseSensitive===!0,childrenIndex:f,route:c};p.relativePath.startsWith("/")&&(Ne(p.relativePath.startsWith(l),`Absolute route path "${p.relativePath}" nested under path "${l}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(l.length));let m=Sn([l,p.relativePath]),v=i.concat(p);c.children&&c.children.length>0&&(Ne(c.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),yh(c.children,r,v,m)),!(c.path==null&&!c.index)&&r.push({path:m,score:Dm(m,c.index),routesMeta:v})};return t.forEach((c,f)=>{if(c.path===""||!c.path?.includes("?"))a(c,f);else for(let h of wh(c.path))a(c,f,h)}),r}function wh(t){let r=t.split("/");if(r.length===0)return[];let[i,...l]=r,a=i.endsWith("?"),c=i.replace(/\?$/,"");if(l.length===0)return a?[c,""]:[c];let f=wh(l.join("/")),h=[];return h.push(...f.map(p=>p===""?c:[c,p].join("/"))),a&&h.push(...f),h.map(p=>t.startsWith("/")&&p===""?"/":p)}function Pm(t){t.sort((r,i)=>r.score!==i.score?i.score-r.score:Tm(r.routesMeta.map(l=>l.childrenIndex),i.routesMeta.map(l=>l.childrenIndex)))}var km=/^:[\w-]+$/,_m=3,Mm=2,Fm=1,Lm=10,Nm=-2,Df=t=>t==="*";function Dm(t,r){let i=t.split("/"),l=i.length;return i.some(Df)&&(l+=Nm),r&&(l+=Mm),i.filter(a=>!Df(a)).reduce((a,c)=>a+(km.test(c)?_m:c===""?Fm:Lm),l)}function Tm(t,r){return t.length===r.length&&t.slice(0,-1).every((l,a)=>l===r[a])?t[t.length-1]-r[r.length-1]:0}function jm(t,r,i=!1){let{routesMeta:l}=t,a={},c="/",f=[];for(let h=0;h<l.length;++h){let p=l[h],m=h===l.length-1,v=c==="/"?r:r.slice(c.length)||"/",S=es({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},v),y=p.route;if(!S&&m&&i&&!l[l.length-1].route.index&&(S=es({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},v)),!S)return null;Object.assign(a,S.params),f.push({params:a,pathname:Sn([c,S.pathname]),pathnameBase:Am(Sn([c,S.pathnameBase])),route:y}),S.pathnameBase!=="/"&&(c=Sn([c,S.pathnameBase]))}return f}function es(t,r){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[i,l]=zm(t.path,t.caseSensitive,t.end),a=r.match(i);if(!a)return null;let c=a[0],f=c.replace(/(.)\/+$/,"$1"),h=a.slice(1);return{params:l.reduce((m,{paramName:v,isOptional:S},y)=>{if(v==="*"){let C=h[y]||"";f=c.slice(0,c.length-C.length).replace(/(.)\/+$/,"$1")}const x=h[y];return S&&!x?m[v]=void 0:m[v]=(x||"").replace(/%2F/g,"/"),m},{}),pathname:c,pathnameBase:f,pattern:t}}function zm(t,r=!1,i=!0){rt(t==="*"||!t.endsWith("*")||t.endsWith("/*"),`Route path "${t}" will be treated as if it were "${t.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${t.replace(/\*$/,"/*")}".`);let l=[],a="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(f,h,p)=>(l.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(l.push({paramName:"*"}),a+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?a+="\\/*$":t!==""&&t!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,r?void 0:"i"),l]}function Om(t){try{return t.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return rt(!1,`The URL path "${t}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),t}}function Yt(t,r){if(r==="/")return t;if(!t.toLowerCase().startsWith(r.toLowerCase()))return null;let i=r.endsWith("/")?r.length-1:r.length,l=t.charAt(i);return l&&l!=="/"?null:t.slice(i)||"/"}function Im(t,r="/"){let{pathname:i,search:l="",hash:a=""}=typeof t=="string"?mr(t):t;return{pathname:i?i.startsWith("/")?i:$m(i,r):r,search:bm(l),hash:Vm(a)}}function $m(t,r){let i=r.replace(/\/+$/,"").split("/");return t.split("/").forEach(a=>{a===".."?i.length>1&&i.pop():a!=="."&&i.push(a)}),i.length>1?i.join("/"):"/"}function qa(t,r,i,l){return`Cannot include a '${t}' character in a manually specified \`to.${r}\` field [${JSON.stringify(l)}].  Please separate it out to the \`to.${i}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Sh(t){return t.filter((r,i)=>i===0||r.route.path&&r.route.path.length>0)}function ku(t){let r=Sh(t);return r.map((i,l)=>l===r.length-1?i.pathname:i.pathnameBase)}function _u(t,r,i,l=!1){let a;typeof t=="string"?a=mr(t):(a={...t},Ne(!a.pathname||!a.pathname.includes("?"),qa("?","pathname","search",a)),Ne(!a.pathname||!a.pathname.includes("#"),qa("#","pathname","hash",a)),Ne(!a.search||!a.search.includes("#"),qa("#","search","hash",a)));let c=t===""||a.pathname==="",f=c?"/":a.pathname,h;if(f==null)h=i;else{let S=r.length-1;if(!l&&f.startsWith("..")){let y=f.split("/");for(;y[0]==="..";)y.shift(),S-=1;a.pathname=y.join("/")}h=S>=0?r[S]:"/"}let p=Im(a,h),m=f&&f!=="/"&&f.endsWith("/"),v=(c||f===".")&&i.endsWith("/");return!p.pathname.endsWith("/")&&(m||v)&&(p.pathname+="/"),p}var Sn=t=>t.join("/").replace(/\/\/+/g,"/"),Am=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),bm=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,Vm=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t,ts=class{constructor(t,r,i,l=!1){this.status=t,this.statusText=r||"",this.internal=l,i instanceof Error?(this.data=i.toString(),this.error=i):this.data=i}};function Ei(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}var xh=["POST","PUT","PATCH","DELETE"],Um=new Set(xh),Hm=["GET",...xh],Bm=new Set(Hm),Gm=new Set([301,302,303,307,308]),Qm=new Set([307,308]),Ka={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Wm={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},hi={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Mu=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,qm=t=>({hasErrorBoundary:!!t.hasErrorBoundary}),Ch="remix-router-transitions",Rh=Symbol("ResetLoaderData");function Km(t){const r=t.window?t.window:typeof window<"u"?window:void 0,i=typeof r<"u"&&typeof r.document<"u"&&typeof r.document.createElement<"u";Ne(t.routes.length>0,"You must provide a non-empty routes array to createRouter");let l=t.hydrationRouteProperties||[],a=t.mapRouteProperties||qm,c={},f=Jl(t.routes,a,void 0,c),h,p=t.basename||"/",m=t.dataStrategy||ev,v={unstable_middleware:!1,...t.future},S=null,y=new Set,x=null,C=null,P=null,T=t.hydrationData!=null,F=cr(f,t.history.location,p),B=!1,A=null,K;if(F==null&&!t.patchRoutesOnNavigation){let R=Kt(404,{pathname:t.history.location.pathname}),{matches:N,route:I}=Bf(f);K=!0,F=N,A={[I.id]:R}}else if(F&&!t.hydrationData&&Cr(F,f,t.history.location.pathname).active&&(F=null),F)if(F.some(R=>R.route.lazy))K=!1;else if(!F.some(R=>R.route.loader))K=!0;else{let R=t.hydrationData?t.hydrationData.loaderData:null,N=t.hydrationData?t.hydrationData.errors:null;if(N){let I=F.findIndex(H=>N[H.route.id]!==void 0);K=F.slice(0,I+1).every(H=>!au(H.route,R,N))}else K=F.every(I=>!au(I.route,R,N))}else{K=!1,F=[];let R=Cr(null,f,t.history.location.pathname);R.active&&R.matches&&(B=!0,F=R.matches)}let Z,j={historyAction:t.history.action,location:t.history.location,matches:F,initialized:K,navigation:Ka,restoreScrollPosition:t.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:t.hydrationData&&t.hydrationData.loaderData||{},actionData:t.hydrationData&&t.hydrationData.actionData||null,errors:t.hydrationData&&t.hydrationData.errors||A,fetchers:new Map,blockers:new Map},ie="POP",se=!1,ue,Ce=!1,Ie=new Map,xe=null,Re=!1,Pe=!1,He=new Set,pe=new Map,_e=0,G=-1,J=new Map,W=new Set,_=new Map,$=new Map,he=new Set,we=new Map,Le,Me=null;function Ve(){if(S=t.history.listen(({action:R,location:N,delta:I})=>{if(Le){Le(),Le=void 0;return}rt(we.size===0||I!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let H=qr({currentLocation:j.location,nextLocation:N,historyAction:R});if(H&&I!=null){let q=new Promise(ae=>{Le=ae});t.history.go(I*-1),In(H,{state:"blocked",location:N,proceed(){In(H,{state:"proceeding",proceed:void 0,reset:void 0,location:N}),q.then(()=>t.history.go(I))},reset(){let ae=new Map(j.blockers);ae.set(H,hi),Ae({blockers:ae})}});return}return Zt(R,N)}),i){dv(r,Ie);let R=()=>fv(r,Ie);r.addEventListener("pagehide",R),xe=()=>r.removeEventListener("pagehide",R)}return j.initialized||Zt("POP",j.location,{initialHydration:!0}),Z}function $e(){S&&S(),xe&&xe(),y.clear(),ue&&ue.abort(),j.fetchers.forEach((R,N)=>Qr(N)),j.blockers.forEach((R,N)=>Wr(N))}function Be(R){return y.add(R),()=>y.delete(R)}function Ae(R,N={}){j={...j,...R};let I=[],H=[];j.fetchers.forEach((q,ae)=>{q.state==="idle"&&(he.has(ae)?I.push(ae):H.push(ae))}),he.forEach(q=>{!j.fetchers.has(q)&&!pe.has(q)&&I.push(q)}),[...y].forEach(q=>q(j,{deletedFetchers:I,viewTransitionOpts:N.viewTransitionOpts,flushSync:N.flushSync===!0})),I.forEach(q=>Qr(q)),H.forEach(q=>j.fetchers.delete(q))}function Xt(R,N,{flushSync:I}={}){let H=j.actionData!=null&&j.navigation.formMethod!=null&&Vt(j.navigation.formMethod)&&j.navigation.state==="loading"&&R.state?._isRedirect!==!0,q;N.actionData?Object.keys(N.actionData).length>0?q=N.actionData:q=null:H?q=j.actionData:q=null;let ae=N.loaderData?Uf(j.loaderData,N.loaderData,N.matches||[],N.errors):j.loaderData,ce=j.blockers;ce.size>0&&(ce=new Map(ce),ce.forEach((ne,Se)=>ce.set(Se,hi)));let X=se===!0||j.navigation.formMethod!=null&&Vt(j.navigation.formMethod)&&R.state?._isRedirect!==!0;h&&(f=h,h=void 0),Re||ie==="POP"||(ie==="PUSH"?t.history.push(R,R.state):ie==="REPLACE"&&t.history.replace(R,R.state));let le;if(ie==="POP"){let ne=Ie.get(j.location.pathname);ne&&ne.has(R.pathname)?le={currentLocation:j.location,nextLocation:R}:Ie.has(R.pathname)&&(le={currentLocation:R,nextLocation:j.location})}else if(Ce){let ne=Ie.get(j.location.pathname);ne?ne.add(R.pathname):(ne=new Set([R.pathname]),Ie.set(j.location.pathname,ne)),le={currentLocation:j.location,nextLocation:R}}Ae({...N,actionData:q,loaderData:ae,historyAction:ie,location:R,initialized:!0,navigation:Ka,revalidation:"idle",restoreScrollPosition:Lo(R,N.matches||j.matches),preventScrollReset:X,blockers:ce},{viewTransitionOpts:le,flushSync:I===!0}),ie="POP",se=!1,Ce=!1,Re=!1,Pe=!1,Me?.resolve(),Me=null}async function Eo(R,N){if(typeof R=="number"){t.history.go(R);return}let I=su(j.location,j.matches,p,R,N?.fromRouteId,N?.relative),{path:H,submission:q,error:ae}=Tf(!1,I,N),ce=j.location,X=Ri(j.location,H,N&&N.state);X={...X,...t.history.encodeLocation(X)};let le=N&&N.replace!=null?N.replace:void 0,ne="PUSH";le===!0?ne="REPLACE":le===!1||q!=null&&Vt(q.formMethod)&&q.formAction===j.location.pathname+j.location.search&&(ne="REPLACE");let Se=N&&"preventScrollReset"in N?N.preventScrollReset===!0:void 0,ye=(N&&N.flushSync)===!0,ke=qr({currentLocation:ce,nextLocation:X,historyAction:ne});if(ke){In(ke,{state:"blocked",location:X,proceed(){In(ke,{state:"proceeding",proceed:void 0,reset:void 0,location:X}),Eo(R,N)},reset(){let Te=new Map(j.blockers);Te.set(ke,hi),Ae({blockers:Te})}});return}await Zt(ne,X,{submission:q,pendingError:ae,preventScrollReset:Se,replace:N&&N.replace,enableViewTransition:N&&N.viewTransition,flushSync:ye})}function Hr(){Me||(Me=hv()),Gr(),Ae({revalidation:"loading"});let R=Me.promise;return j.navigation.state==="submitting"?R:j.navigation.state==="idle"?(Zt(j.historyAction,j.location,{startUninterruptedRevalidation:!0}),R):(Zt(ie||j.historyAction,j.navigation.location,{overrideNavigation:j.navigation,enableViewTransition:Ce===!0}),R)}async function Zt(R,N,I){ue&&ue.abort(),ue=null,ie=R,Re=(I&&I.startUninterruptedRevalidation)===!0,$n(j.location,j.matches),se=(I&&I.preventScrollReset)===!0,Ce=(I&&I.enableViewTransition)===!0;let H=h||f,q=I&&I.overrideNavigation,ae=I?.initialHydration&&j.matches&&j.matches.length>0&&!B?j.matches:cr(H,N,p),ce=(I&&I.flushSync)===!0;if(ae&&j.initialized&&!Pe&&lv(j.location,N)&&!(I&&I.submission&&Vt(I.submission.formMethod))){Xt(N,{matches:ae},{flushSync:ce});return}let X=Cr(ae,H,N.pathname);if(X.active&&X.matches&&(ae=X.matches),!ae){let{error:ot,notFoundMatches:wt,route:De}=xr(N.pathname);Xt(N,{matches:wt,loaderData:{},errors:{[De.id]:ot}},{flushSync:ce});return}ue=new AbortController;let le=yo(t.history,N,ue.signal,I&&I.submission),ne=new Nf(t.unstable_getContext?await t.unstable_getContext():void 0),Se;if(I&&I.pendingError)Se=[Ar(ae).route.id,{type:"error",error:I.pendingError}];else if(I&&I.submission&&Vt(I.submission.formMethod)){let ot=await Ni(le,N,I.submission,ae,ne,X.active,I&&I.initialHydration===!0,{replace:I.replace,flushSync:ce});if(ot.shortCircuited)return;if(ot.pendingActionResult){let[wt,De]=ot.pendingActionResult;if(bt(De)&&Ei(De.error)&&De.error.status===404){ue=null,Xt(N,{matches:ot.matches,loaderData:{},errors:{[wt]:De.error}});return}}ae=ot.matches||ae,Se=ot.pendingActionResult,q=Ya(N,I.submission),ce=!1,X.active=!1,le=yo(t.history,le.url,le.signal)}let{shortCircuited:ye,matches:ke,loaderData:Te,errors:Ze}=await Di(le,N,ae,ne,X.active,q,I&&I.submission,I&&I.fetcherSubmission,I&&I.replace,I&&I.initialHydration===!0,ce,Se);ye||(ue=null,Xt(N,{matches:ke||ae,...Hf(Se),loaderData:Te,errors:Ze}))}async function Ni(R,N,I,H,q,ae,ce,X={}){Gr();let le=uv(N,I);if(Ae({navigation:le},{flushSync:X.flushSync===!0}),ae){let ye=await Rr(H,N.pathname,R.signal);if(ye.type==="aborted")return{shortCircuited:!0};if(ye.type==="error"){let ke=Ar(ye.partialMatches).route.id;return{matches:ye.partialMatches,pendingActionResult:[ke,{type:"error",error:ye.error}]}}else if(ye.matches)H=ye.matches;else{let{notFoundMatches:ke,error:Te,route:Ze}=xr(N.pathname);return{matches:ke,pendingActionResult:[Ze.id,{type:"error",error:Te}]}}}let ne,Se=xi(H,N);if(!Se.route.action&&!Se.route.lazy)ne={type:"error",error:Kt(405,{method:R.method,pathname:N.pathname,routeId:Se.route.id})};else{let ye=wo(a,c,R,H,Se,ce?[]:l,q),ke=await On(R,ye,q,null);if(ne=ke[Se.route.id],!ne){for(let Te of H)if(ke[Te.route.id]){ne=ke[Te.route.id];break}}if(R.signal.aborted)return{shortCircuited:!0}}if(br(ne)){let ye;return X&&X.replace!=null?ye=X.replace:ye=Af(ne.response.headers.get("Location"),new URL(R.url),p)===j.location.pathname+j.location.search,await Jt(R,ne,!0,{submission:I,replace:ye}),{shortCircuited:!0}}if(bt(ne)){let ye=Ar(H,Se.route.id);return(X&&X.replace)!==!0&&(ie="PUSH"),{matches:H,pendingActionResult:[ye.route.id,ne,Se.route.id]}}return{matches:H,pendingActionResult:[Se.route.id,ne]}}async function Di(R,N,I,H,q,ae,ce,X,le,ne,Se,ye){let ke=ae||Ya(N,ce),Te=ce||X||Qf(ke),Ze=!Re&&!ne;if(q){if(Ze){let Pt=Br(ye);Ae({navigation:ke,...Pt!==void 0?{actionData:Pt}:{}},{flushSync:Se})}let je=await Rr(I,N.pathname,R.signal);if(je.type==="aborted")return{shortCircuited:!0};if(je.type==="error"){let Pt=Ar(je.partialMatches).route.id;return{matches:je.partialMatches,loaderData:{},errors:{[Pt]:je.error}}}else if(je.matches)I=je.matches;else{let{error:Pt,notFoundMatches:nn,route:En}=xr(N.pathname);return{matches:nn,loaderData:{},errors:{[En.id]:Pt}}}}let ot=h||f,{dsMatches:wt,revalidatingFetchers:De}=jf(R,H,a,c,t.history,j,I,Te,N,ne?[]:l,ne===!0,Pe,He,he,_,W,ot,p,t.patchRoutesOnNavigation!=null,ye);if(G=++_e,!t.dataStrategy&&!wt.some(je=>je.shouldLoad)&&De.length===0){let je=_o();return Xt(N,{matches:I,loaderData:{},errors:ye&&bt(ye[1])?{[ye[0]]:ye[1].error}:null,...Hf(ye),...je?{fetchers:new Map(j.fetchers)}:{}},{flushSync:Se}),{shortCircuited:!0}}if(Ze){let je={};if(!q){je.navigation=ke;let Pt=Br(ye);Pt!==void 0&&(je.actionData=Pt)}De.length>0&&(je.fetchers=Ti(De)),Ae(je,{flushSync:Se})}De.forEach(je=>{Rt(je.key),je.controller&&pe.set(je.key,je.controller)});let An=()=>De.forEach(je=>Rt(je.key));ue&&ue.signal.addEventListener("abort",An);let{loaderResults:hn,fetcherResults:bn}=await ko(wt,De,R,H);if(R.signal.aborted)return{shortCircuited:!0};ue&&ue.signal.removeEventListener("abort",An),De.forEach(je=>pe.delete(je.key));let Et=Ul(hn);if(Et)return await Jt(R,Et.result,!0,{replace:le}),{shortCircuited:!0};if(Et=Ul(bn),Et)return W.add(Et.key),await Jt(R,Et.result,!0,{replace:le}),{shortCircuited:!0};let{loaderData:Er,errors:Ue}=Vf(j,I,hn,ye,De,bn);ne&&j.errors&&(Ue={...j.errors,...Ue});let No=_o(),tn=Mo(G),Pr=No||tn||De.length>0;return{matches:I,loaderData:Er,errors:Ue,...Pr?{fetchers:new Map(j.fetchers)}:{}}}function Br(R){if(R&&!bt(R[1]))return{[R[0]]:R[1].data};if(j.actionData)return Object.keys(j.actionData).length===0?null:j.actionData}function Ti(R){return R.forEach(N=>{let I=j.fetchers.get(N.key),H=pi(void 0,I?I.data:void 0);j.fetchers.set(N.key,H)}),new Map(j.fetchers)}async function Po(R,N,I,H){Rt(R);let q=(H&&H.flushSync)===!0,ae=h||f,ce=su(j.location,j.matches,p,I,N,H?.relative),X=cr(ae,ce,p),le=Cr(X,ae,ce);if(le.active&&le.matches&&(X=le.matches),!X){zt(R,N,Kt(404,{pathname:ce}),{flushSync:q});return}let{path:ne,submission:Se,error:ye}=Tf(!0,ce,H);if(ye){zt(R,N,ye,{flushSync:q});return}let ke=xi(X,ne),Te=new Nf(t.unstable_getContext?await t.unstable_getContext():void 0),Ze=(H&&H.preventScrollReset)===!0;if(Se&&Vt(Se.formMethod)){await yr(R,N,ne,ke,X,Te,le.active,q,Ze,Se);return}_.set(R,{routeId:N,path:ne}),await zn(R,N,ne,ke,X,Te,le.active,q,Ze,Se)}async function yr(R,N,I,H,q,ae,ce,X,le,ne){Gr(),_.delete(R);function Se(Je){if(!Je.route.action&&!Je.route.lazy){let st=Kt(405,{method:ne.formMethod,pathname:I,routeId:N});return zt(R,N,st,{flushSync:X}),!0}return!1}if(!ce&&Se(H))return;let ye=j.fetchers.get(R);en(R,cv(ne,ye),{flushSync:X});let ke=new AbortController,Te=yo(t.history,I,ke.signal,ne);if(ce){let Je=await Rr(q,I,Te.signal,R);if(Je.type==="aborted")return;if(Je.type==="error"){zt(R,N,Je.error,{flushSync:X});return}else if(Je.matches){if(q=Je.matches,H=xi(q,I),Se(H))return}else{zt(R,N,Kt(404,{pathname:I}),{flushSync:X});return}}pe.set(R,ke);let Ze=_e,ot=wo(a,c,Te,q,H,l,ae),De=(await On(Te,ot,ae,R))[H.route.id];if(Te.signal.aborted){pe.get(R)===ke&&pe.delete(R);return}if(he.has(R)){if(br(De)||bt(De)){en(R,ur(void 0));return}}else{if(br(De))if(pe.delete(R),G>Ze){en(R,ur(void 0));return}else return W.add(R),en(R,pi(ne)),Jt(Te,De,!1,{fetcherSubmission:ne,preventScrollReset:le});if(bt(De)){zt(R,N,De.error);return}}let An=j.navigation.location||j.location,hn=yo(t.history,An,ke.signal),bn=h||f,Et=j.navigation.state!=="idle"?cr(bn,j.navigation.location,p):j.matches;Ne(Et,"Didn't find any matches after fetcher action");let Er=++_e;J.set(R,Er);let Ue=pi(ne,De.data);j.fetchers.set(R,Ue);let{dsMatches:No,revalidatingFetchers:tn}=jf(hn,ae,a,c,t.history,j,Et,ne,An,l,!1,Pe,He,he,_,W,bn,p,t.patchRoutesOnNavigation!=null,[H.route.id,De]);tn.filter(Je=>Je.key!==R).forEach(Je=>{let st=Je.key,Ii=j.fetchers.get(st),as=pi(void 0,Ii?Ii.data:void 0);j.fetchers.set(st,as),Rt(st),Je.controller&&pe.set(st,Je.controller)}),Ae({fetchers:new Map(j.fetchers)});let Pr=()=>tn.forEach(Je=>Rt(Je.key));ke.signal.addEventListener("abort",Pr);let{loaderResults:je,fetcherResults:Pt}=await ko(No,tn,hn,ae);if(ke.signal.aborted)return;if(ke.signal.removeEventListener("abort",Pr),J.delete(R),pe.delete(R),tn.forEach(Je=>pe.delete(Je.key)),j.fetchers.has(R)){let Je=ur(De.data);j.fetchers.set(R,Je)}let nn=Ul(je);if(nn)return Jt(hn,nn.result,!1,{preventScrollReset:le});if(nn=Ul(Pt),nn)return W.add(nn.key),Jt(hn,nn.result,!1,{preventScrollReset:le});let{loaderData:En,errors:kt}=Vf(j,Et,je,void 0,tn,Pt);Mo(Er),j.navigation.state==="loading"&&Er>G?(Ne(ie,"Expected pending action"),ue&&ue.abort(),Xt(j.navigation.location,{matches:Et,loaderData:En,errors:kt,fetchers:new Map(j.fetchers)})):(Ae({errors:kt,loaderData:Uf(j.loaderData,En,Et,kt),fetchers:new Map(j.fetchers)}),Pe=!1)}async function zn(R,N,I,H,q,ae,ce,X,le,ne){let Se=j.fetchers.get(R);en(R,pi(ne,Se?Se.data:void 0),{flushSync:X});let ye=new AbortController,ke=yo(t.history,I,ye.signal);if(ce){let De=await Rr(q,I,ke.signal,R);if(De.type==="aborted")return;if(De.type==="error"){zt(R,N,De.error,{flushSync:X});return}else if(De.matches)q=De.matches,H=xi(q,I);else{zt(R,N,Kt(404,{pathname:I}),{flushSync:X});return}}pe.set(R,ye);let Te=_e,Ze=wo(a,c,ke,q,H,l,ae),wt=(await On(ke,Ze,ae,R))[H.route.id];if(pe.get(R)===ye&&pe.delete(R),!ke.signal.aborted){if(he.has(R)){en(R,ur(void 0));return}if(br(wt))if(G>Te){en(R,ur(void 0));return}else{W.add(R),await Jt(ke,wt,!1,{preventScrollReset:le});return}if(bt(wt)){zt(R,N,wt.error);return}en(R,ur(wt.data))}}async function Jt(R,N,I,{submission:H,fetcherSubmission:q,preventScrollReset:ae,replace:ce}={}){N.response.headers.has("X-Remix-Revalidate")&&(Pe=!0);let X=N.response.headers.get("Location");Ne(X,"Expected a Location header on the redirect Response"),X=Af(X,new URL(R.url),p);let le=Ri(j.location,X,{_isRedirect:!0});if(i){let Ze=!1;if(N.response.headers.has("X-Remix-Reload-Document"))Ze=!0;else if(Mu.test(X)){const ot=vh(X,!0);Ze=ot.origin!==r.location.origin||Yt(ot.pathname,p)==null}if(Ze){ce?r.location.replace(X):r.location.assign(X);return}}ue=null;let ne=ce===!0||N.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:Se,formAction:ye,formEncType:ke}=j.navigation;!H&&!q&&Se&&ye&&ke&&(H=Qf(j.navigation));let Te=H||q;if(Qm.has(N.response.status)&&Te&&Vt(Te.formMethod))await Zt(ne,le,{submission:{...Te,formAction:X},preventScrollReset:ae||se,enableViewTransition:I?Ce:void 0});else{let Ze=Ya(le,H);await Zt(ne,le,{overrideNavigation:Ze,fetcherSubmission:q,preventScrollReset:ae||se,enableViewTransition:I?Ce:void 0})}}async function On(R,N,I,H){let q,ae={};try{q=await tv(m,R,N,H,I,!1)}catch(ce){return N.filter(X=>X.shouldLoad).forEach(X=>{ae[X.route.id]={type:"error",error:ce}}),ae}if(R.signal.aborted)return ae;for(let[ce,X]of Object.entries(q))if(sv(X)){let le=X.result;ae[ce]={type:"redirect",response:ov(le,R,ce,N,p)}}else ae[ce]=await rv(X);return ae}async function ko(R,N,I,H){let q=On(I,R,H,null),ae=Promise.all(N.map(async le=>{if(le.matches&&le.match&&le.request&&le.controller){let Se=(await On(le.request,le.matches,H,le.key))[le.match.route.id];return{[le.key]:Se}}else return Promise.resolve({[le.key]:{type:"error",error:Kt(404,{pathname:le.path})}})})),ce=await q,X=(await ae).reduce((le,ne)=>Object.assign(le,ne),{});return{loaderResults:ce,fetcherResults:X}}function Gr(){Pe=!0,_.forEach((R,N)=>{pe.has(N)&&He.add(N),Rt(N)})}function en(R,N,I={}){j.fetchers.set(R,N),Ae({fetchers:new Map(j.fetchers)},{flushSync:(I&&I.flushSync)===!0})}function zt(R,N,I,H={}){let q=Ar(j.matches,N);Qr(R),Ae({errors:{[q.route.id]:I},fetchers:new Map(j.fetchers)},{flushSync:(H&&H.flushSync)===!0})}function wr(R){return $.set(R,($.get(R)||0)+1),he.has(R)&&he.delete(R),j.fetchers.get(R)||Wm}function Qr(R){let N=j.fetchers.get(R);pe.has(R)&&!(N&&N.state==="loading"&&J.has(R))&&Rt(R),_.delete(R),J.delete(R),W.delete(R),he.delete(R),He.delete(R),j.fetchers.delete(R)}function Sr(R){let N=($.get(R)||0)-1;N<=0?($.delete(R),he.add(R)):$.set(R,N),Ae({fetchers:new Map(j.fetchers)})}function Rt(R){let N=pe.get(R);N&&(N.abort(),pe.delete(R))}function ji(R){for(let N of R){let I=wr(N),H=ur(I.data);j.fetchers.set(N,H)}}function _o(){let R=[],N=!1;for(let I of W){let H=j.fetchers.get(I);Ne(H,`Expected fetcher: ${I}`),H.state==="loading"&&(W.delete(I),R.push(I),N=!0)}return ji(R),N}function Mo(R){let N=[];for(let[I,H]of J)if(H<R){let q=j.fetchers.get(I);Ne(q,`Expected fetcher: ${I}`),q.state==="loading"&&(Rt(I),J.delete(I),N.push(I))}return ji(N),N.length>0}function ss(R,N){let I=j.blockers.get(R)||hi;return we.get(R)!==N&&we.set(R,N),I}function Wr(R){j.blockers.delete(R),we.delete(R)}function In(R,N){let I=j.blockers.get(R)||hi;Ne(I.state==="unblocked"&&N.state==="blocked"||I.state==="blocked"&&N.state==="blocked"||I.state==="blocked"&&N.state==="proceeding"||I.state==="blocked"&&N.state==="unblocked"||I.state==="proceeding"&&N.state==="unblocked",`Invalid blocker state transition: ${I.state} -> ${N.state}`);let H=new Map(j.blockers);H.set(R,N),Ae({blockers:H})}function qr({currentLocation:R,nextLocation:N,historyAction:I}){if(we.size===0)return;we.size>1&&rt(!1,"A router only supports one blocker at a time");let H=Array.from(we.entries()),[q,ae]=H[H.length-1],ce=j.blockers.get(q);if(!(ce&&ce.state==="proceeding")&&ae({currentLocation:R,nextLocation:N,historyAction:I}))return q}function xr(R){let N=Kt(404,{pathname:R}),I=h||f,{matches:H,route:q}=Bf(I);return{notFoundMatches:H,route:q,error:N}}function Fo(R,N,I){if(x=R,P=N,C=I||null,!T&&j.navigation===Ka){T=!0;let H=Lo(j.location,j.matches);H!=null&&Ae({restoreScrollPosition:H})}return()=>{x=null,P=null,C=null}}function Rn(R,N){return C&&C(R,N.map(H=>Em(H,j.loaderData)))||R.key}function $n(R,N){if(x&&P){let I=Rn(R,N);x[I]=P()}}function Lo(R,N){if(x){let I=Rn(R,N),H=x[I];if(typeof H=="number")return H}return null}function Cr(R,N,I){if(t.patchRoutesOnNavigation)if(R){if(Object.keys(R[0].params).length>0)return{active:!0,matches:ql(N,I,p,!0)}}else return{active:!0,matches:ql(N,I,p,!0)||[]};return{active:!1,matches:null}}async function Rr(R,N,I,H){if(!t.patchRoutesOnNavigation)return{type:"success",matches:R};let q=R;for(;;){let ae=h==null,ce=h||f,X=c;try{await t.patchRoutesOnNavigation({signal:I,path:N,matches:q,fetcherKey:H,patch:(Se,ye)=>{I.aborted||zf(Se,ye,ce,X,a)}})}catch(Se){return{type:"error",error:Se,partialMatches:q}}finally{ae&&!I.aborted&&(f=[...f])}if(I.aborted)return{type:"aborted"};let le=cr(ce,N,p);if(le)return{type:"success",matches:le};let ne=ql(ce,N,p,!0);if(!ne||q.length===ne.length&&q.every((Se,ye)=>Se.route.id===ne[ye].route.id))return{type:"success",matches:null};q=ne}}function zi(R){c={},h=Jl(R,a,void 0,c)}function Oi(R,N){let I=h==null;zf(R,N,h||f,c,a),I&&(f=[...f],Ae({}))}return Z={get basename(){return p},get future(){return v},get state(){return j},get routes(){return f},get window(){return r},initialize:Ve,subscribe:Be,enableScrollRestoration:Fo,navigate:Eo,fetch:Po,revalidate:Hr,createHref:R=>t.history.createHref(R),encodeLocation:R=>t.history.encodeLocation(R),getFetcher:wr,deleteFetcher:Sr,dispose:$e,getBlocker:ss,deleteBlocker:Wr,patchRoutes:Oi,_internalFetchControllers:pe,_internalSetRoutes:zi},Z}function Ym(t){return t!=null&&("formData"in t&&t.formData!=null||"body"in t&&t.body!==void 0)}function su(t,r,i,l,a,c){let f,h;if(a){f=[];for(let m of r)if(f.push(m),m.route.id===a){h=m;break}}else f=r,h=r[r.length-1];let p=_u(l||".",ku(f),Yt(t.pathname,i)||t.pathname,c==="path");if(l==null&&(p.search=t.search,p.hash=t.hash),(l==null||l===""||l===".")&&h){let m=Fu(p.search);if(h.route.index&&!m)p.search=p.search?p.search.replace(/^\?/,"?index&"):"?index";else if(!h.route.index&&m){let v=new URLSearchParams(p.search),S=v.getAll("index");v.delete("index"),S.filter(x=>x).forEach(x=>v.append("index",x));let y=v.toString();p.search=y?`?${y}`:""}}return i!=="/"&&(p.pathname=p.pathname==="/"?i:Sn([i,p.pathname])),hr(p)}function Tf(t,r,i){if(!i||!Ym(i))return{path:r};if(i.formMethod&&!av(i.formMethod))return{path:r,error:Kt(405,{method:i.formMethod})};let l=()=>({path:r,error:Kt(400,{type:"invalid-body"})}),c=(i.formMethod||"get").toUpperCase(),f=Fh(r);if(i.body!==void 0){if(i.formEncType==="text/plain"){if(!Vt(c))return l();let S=typeof i.body=="string"?i.body:i.body instanceof FormData||i.body instanceof URLSearchParams?Array.from(i.body.entries()).reduce((y,[x,C])=>`${y}${x}=${C}
`,""):String(i.body);return{path:r,submission:{formMethod:c,formAction:f,formEncType:i.formEncType,formData:void 0,json:void 0,text:S}}}else if(i.formEncType==="application/json"){if(!Vt(c))return l();try{let S=typeof i.body=="string"?JSON.parse(i.body):i.body;return{path:r,submission:{formMethod:c,formAction:f,formEncType:i.formEncType,formData:void 0,json:S,text:void 0}}}catch{return l()}}}Ne(typeof FormData=="function","FormData is not available in this environment");let h,p;if(i.formData)h=cu(i.formData),p=i.formData;else if(i.body instanceof FormData)h=cu(i.body),p=i.body;else if(i.body instanceof URLSearchParams)h=i.body,p=bf(h);else if(i.body==null)h=new URLSearchParams,p=new FormData;else try{h=new URLSearchParams(i.body),p=bf(h)}catch{return l()}let m={formMethod:c,formAction:f,formEncType:i&&i.formEncType||"application/x-www-form-urlencoded",formData:p,json:void 0,text:void 0};if(Vt(m.formMethod))return{path:r,submission:m};let v=mr(r);return t&&v.search&&Fu(v.search)&&h.append("index",""),v.search=`?${h}`,{path:hr(v),submission:m}}function jf(t,r,i,l,a,c,f,h,p,m,v,S,y,x,C,P,T,F,B,A){let K=A?bt(A[1])?A[1].error:A[1].data:void 0,Z=a.createURL(c.location),j=a.createURL(p),ie;if(v&&c.errors){let Re=Object.keys(c.errors)[0];ie=f.findIndex(Pe=>Pe.route.id===Re)}else if(A&&bt(A[1])){let Re=A[0];ie=f.findIndex(Pe=>Pe.route.id===Re)-1}let se=A?A[1].statusCode:void 0,ue=se&&se>=400,Ce={currentUrl:Z,currentParams:c.matches[0]?.params||{},nextUrl:j,nextParams:f[0].params,...h,actionResult:K,actionStatus:se},Ie=f.map((Re,Pe)=>{let{route:He}=Re,pe=null;if(ie!=null&&Pe>ie?pe=!1:He.lazy?pe=!0:He.loader==null?pe=!1:v?pe=au(He,c.loaderData,c.errors):Xm(c.loaderData,c.matches[Pe],Re)&&(pe=!0),pe!==null)return uu(i,l,t,Re,m,r,pe);let _e=ue?!1:S||Z.pathname+Z.search===j.pathname+j.search||Z.search!==j.search||Zm(c.matches[Pe],Re),G={...Ce,defaultShouldRevalidate:_e},J=ns(Re,G);return uu(i,l,t,Re,m,r,J,G)}),xe=[];return C.forEach((Re,Pe)=>{if(v||!f.some($=>$.route.id===Re.routeId)||x.has(Pe))return;let He=c.fetchers.get(Pe),pe=He&&He.state!=="idle"&&He.data===void 0,_e=cr(T,Re.path,F);if(!_e){if(B&&pe)return;xe.push({key:Pe,routeId:Re.routeId,path:Re.path,matches:null,match:null,request:null,controller:null});return}if(P.has(Pe))return;let G=xi(_e,Re.path),J=new AbortController,W=yo(a,Re.path,J.signal),_=null;if(y.has(Pe))y.delete(Pe),_=wo(i,l,W,_e,G,m,r);else if(pe)S&&(_=wo(i,l,W,_e,G,m,r));else{let $={...Ce,defaultShouldRevalidate:ue?!1:S};ns(G,$)&&(_=wo(i,l,W,_e,G,m,r,$))}_&&xe.push({key:Pe,routeId:Re.routeId,path:Re.path,matches:_,match:G,request:W,controller:J})}),{dsMatches:Ie,revalidatingFetchers:xe}}function au(t,r,i){if(t.lazy)return!0;if(!t.loader)return!1;let l=r!=null&&t.id in r,a=i!=null&&i[t.id]!==void 0;return!l&&a?!1:typeof t.loader=="function"&&t.loader.hydrate===!0?!0:!l&&!a}function Xm(t,r,i){let l=!r||i.route.id!==r.route.id,a=!t.hasOwnProperty(i.route.id);return l||a}function Zm(t,r){let i=t.route.path;return t.pathname!==r.pathname||i!=null&&i.endsWith("*")&&t.params["*"]!==r.params["*"]}function ns(t,r){if(t.route.shouldRevalidate){let i=t.route.shouldRevalidate(r);if(typeof i=="boolean")return i}return r.defaultShouldRevalidate}function zf(t,r,i,l,a){let c;if(t){let p=l[t];Ne(p,`No route found to patch children into: routeId = ${t}`),p.children||(p.children=[]),c=p.children}else c=i;let f=r.filter(p=>!c.some(m=>Eh(p,m))),h=Jl(f,a,[t||"_","patch",String(c?.length||"0")],l);c.push(...h)}function Eh(t,r){return"id"in t&&"id"in r&&t.id===r.id?!0:t.index===r.index&&t.path===r.path&&t.caseSensitive===r.caseSensitive?(!t.children||t.children.length===0)&&(!r.children||r.children.length===0)?!0:t.children.every((i,l)=>r.children?.some(a=>Eh(i,a))):!1}var Of=new WeakMap,Ph=({key:t,route:r,manifest:i,mapRouteProperties:l})=>{let a=i[r.id];if(Ne(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let c=a.lazy[t];if(!c)return;let f=Of.get(a);f||(f={},Of.set(a,f));let h=f[t];if(h)return h;let p=(async()=>{let m=Sm(t),S=a[t]!==void 0&&t!=="hasErrorBoundary";if(m)rt(!m,"Route property "+t+" is not a supported lazy route property. This property will be ignored."),f[t]=Promise.resolve();else if(S)rt(!1,`Route "${a.id}" has a static property "${t}" defined. The lazy property will be ignored.`);else{let y=await c();y!=null&&(Object.assign(a,{[t]:y}),Object.assign(a,l(a)))}typeof a.lazy=="object"&&(a.lazy[t]=void 0,Object.values(a.lazy).every(y=>y===void 0)&&(a.lazy=void 0))})();return f[t]=p,p},If=new WeakMap;function Jm(t,r,i,l,a){let c=i[t.id];if(Ne(c,"No route found in manifest"),!t.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof t.lazy=="function"){let v=If.get(c);if(v)return{lazyRoutePromise:v,lazyHandlerPromise:v};let S=(async()=>{Ne(typeof t.lazy=="function","No lazy route function found");let y=await t.lazy(),x={};for(let C in y){let P=y[C];if(P===void 0)continue;let T=Cm(C),B=c[C]!==void 0&&C!=="hasErrorBoundary";T?rt(!T,"Route property "+C+" is not a supported property to be returned from a lazy route function. This property will be ignored."):B?rt(!B,`Route "${c.id}" has a static property "${C}" defined but its lazy function is also returning a value for this property. The lazy route property "${C}" will be ignored.`):x[C]=P}Object.assign(c,x),Object.assign(c,{...l(c),lazy:void 0})})();return If.set(c,S),S.catch(()=>{}),{lazyRoutePromise:S,lazyHandlerPromise:S}}let f=Object.keys(t.lazy),h=[],p;for(let v of f){if(a&&a.includes(v))continue;let S=Ph({key:v,route:t,manifest:i,mapRouteProperties:l});S&&(h.push(S),v===r&&(p=S))}let m=h.length>0?Promise.all(h).then(()=>{}):void 0;return m?.catch(()=>{}),p?.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:p}}async function $f(t){let r=t.matches.filter(a=>a.shouldLoad),i={};return(await Promise.all(r.map(a=>a.resolve()))).forEach((a,c)=>{i[r[c].route.id]=a}),i}async function ev(t){return t.matches.some(r=>r.route.unstable_middleware)?kh(t,!1,()=>$f(t),(r,i)=>({[i]:{type:"error",result:r}})):$f(t)}async function kh(t,r,i,l){let{matches:a,request:c,params:f,context:h}=t,p={handlerResult:void 0};try{let m=a.flatMap(S=>S.route.unstable_middleware?S.route.unstable_middleware.map(y=>[S.route.id,y]):[]),v=await _h({request:c,params:f,context:h},m,r,p,i);return r?v:p.handlerResult}catch(m){if(!p.middlewareError)throw m;let v=await l(p.middlewareError.error,p.middlewareError.routeId);return p.handlerResult?Object.assign(p.handlerResult,v):v}}async function _h(t,r,i,l,a,c=0){let{request:f}=t;if(f.signal.aborted)throw f.signal.reason?f.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${f.method} ${f.url}`);let h=r[c];if(!h)return l.handlerResult=await a(),l.handlerResult;let[p,m]=h,v=!1,S,y=async()=>{if(v)throw new Error("You may only call `next()` once per middleware");v=!0,await _h(t,r,i,l,a,c+1)};try{let x=await m({request:t.request,params:t.params,context:t.context},y);return v?x===void 0?S:x:y()}catch(x){throw l.middlewareError?l.middlewareError.error!==x&&(l.middlewareError={routeId:p,error:x}):l.middlewareError={routeId:p,error:x},x}}function Mh(t,r,i,l,a){let c=Ph({key:"unstable_middleware",route:l.route,manifest:r,mapRouteProperties:t}),f=Jm(l.route,Vt(i.method)?"action":"loader",r,t,a);return{middleware:c,route:f.lazyRoutePromise,handler:f.lazyHandlerPromise}}function uu(t,r,i,l,a,c,f,h=null){let p=!1,m=Mh(t,r,i,l,a);return{...l,_lazyPromises:m,shouldLoad:f,unstable_shouldRevalidateArgs:h,unstable_shouldCallHandler(v){return p=!0,h?typeof v=="boolean"?ns(l,{...h,defaultShouldRevalidate:v}):ns(l,h):f},resolve(v){return p||f||v&&i.method==="GET"&&(l.route.lazy||l.route.loader)?nv({request:i,match:l,lazyHandlerPromise:m?.handler,lazyRoutePromise:m?.route,handlerOverride:v,scopedContext:c}):Promise.resolve({type:"data",result:void 0})}}}function wo(t,r,i,l,a,c,f,h=null){return l.map(p=>p.route.id!==a.route.id?{...p,shouldLoad:!1,unstable_shouldRevalidateArgs:h,unstable_shouldCallHandler:()=>!1,_lazyPromises:Mh(t,r,i,p,c),resolve:()=>Promise.resolve({type:"data",result:void 0})}:uu(t,r,i,p,c,f,!0,h))}async function tv(t,r,i,l,a,c){i.some(m=>m._lazyPromises?.middleware)&&await Promise.all(i.map(m=>m._lazyPromises?.middleware));let f={request:r,params:i[0].params,context:a,matches:i},p=await t({...f,fetcherKey:l,unstable_runClientMiddleware:m=>{let v=f;return kh(v,!1,()=>m({...v,fetcherKey:l,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(S,y)=>({[y]:{type:"error",result:S}}))}});try{await Promise.all(i.flatMap(m=>[m._lazyPromises?.handler,m._lazyPromises?.route]))}catch{}return p}async function nv({request:t,match:r,lazyHandlerPromise:i,lazyRoutePromise:l,handlerOverride:a,scopedContext:c}){let f,h,p=Vt(t.method),m=p?"action":"loader",v=S=>{let y,x=new Promise((T,F)=>y=F);h=()=>y(),t.signal.addEventListener("abort",h);let C=T=>typeof S!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${m}" [routeId: ${r.route.id}]`)):S({request:t,params:r.params,context:c},...T!==void 0?[T]:[]),P=(async()=>{try{return{type:"data",result:await(a?a(F=>C(F)):C())}}catch(T){return{type:"error",result:T}}})();return Promise.race([P,x])};try{let S=p?r.route.action:r.route.loader;if(i||l)if(S){let y,[x]=await Promise.all([v(S).catch(C=>{y=C}),i,l]);if(y!==void 0)throw y;f=x}else{await i;let y=p?r.route.action:r.route.loader;if(y)[f]=await Promise.all([v(y),l]);else if(m==="action"){let x=new URL(t.url),C=x.pathname+x.search;throw Kt(405,{method:t.method,pathname:C,routeId:r.route.id})}else return{type:"data",result:void 0}}else if(S)f=await v(S);else{let y=new URL(t.url),x=y.pathname+y.search;throw Kt(404,{pathname:x})}}catch(S){return{type:"error",result:S}}finally{h&&t.signal.removeEventListener("abort",h)}return f}async function rv(t){let{result:r,type:i}=t;if(Lh(r)){let l;try{let a=r.headers.get("Content-Type");a&&/\bapplication\/json\b/.test(a)?r.body==null?l=null:l=await r.json():l=await r.text()}catch(a){return{type:"error",error:a}}return i==="error"?{type:"error",error:new ts(r.status,r.statusText,l),statusCode:r.status,headers:r.headers}:{type:"data",data:l,statusCode:r.status,headers:r.headers}}return i==="error"?Gf(r)?r.data instanceof Error?{type:"error",error:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:new ts(r.init?.status||500,void 0,r.data),statusCode:Ei(r)?r.status:void 0,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:r,statusCode:Ei(r)?r.status:void 0}:Gf(r)?{type:"data",data:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"data",data:r}}function ov(t,r,i,l,a){let c=t.headers.get("Location");if(Ne(c,"Redirects returned/thrown from loaders/actions must have a Location header"),!Mu.test(c)){let f=l.slice(0,l.findIndex(h=>h.route.id===i)+1);c=su(new URL(r.url),f,a,c),t.headers.set("Location",c)}return t}function Af(t,r,i){if(Mu.test(t)){let l=t,a=l.startsWith("//")?new URL(r.protocol+l):new URL(l),c=Yt(a.pathname,i)!=null;if(a.origin===r.origin&&c)return a.pathname+a.search+a.hash}return t}function yo(t,r,i,l){let a=t.createURL(Fh(r)).toString(),c={signal:i};if(l&&Vt(l.formMethod)){let{formMethod:f,formEncType:h}=l;c.method=f.toUpperCase(),h==="application/json"?(c.headers=new Headers({"Content-Type":h}),c.body=JSON.stringify(l.json)):h==="text/plain"?c.body=l.text:h==="application/x-www-form-urlencoded"&&l.formData?c.body=cu(l.formData):c.body=l.formData}return new Request(a,c)}function cu(t){let r=new URLSearchParams;for(let[i,l]of t.entries())r.append(i,typeof l=="string"?l:l.name);return r}function bf(t){let r=new FormData;for(let[i,l]of t.entries())r.append(i,l);return r}function iv(t,r,i,l=!1,a=!1){let c={},f=null,h,p=!1,m={},v=i&&bt(i[1])?i[1].error:void 0;return t.forEach(S=>{if(!(S.route.id in r))return;let y=S.route.id,x=r[y];if(Ne(!br(x),"Cannot handle redirect results in processLoaderData"),bt(x)){let C=x.error;if(v!==void 0&&(C=v,v=void 0),f=f||{},a)f[y]=C;else{let P=Ar(t,y);f[P.route.id]==null&&(f[P.route.id]=C)}l||(c[y]=Rh),p||(p=!0,h=Ei(x.error)?x.error.status:500),x.headers&&(m[y]=x.headers)}else c[y]=x.data,x.statusCode&&x.statusCode!==200&&!p&&(h=x.statusCode),x.headers&&(m[y]=x.headers)}),v!==void 0&&i&&(f={[i[0]]:v},i[2]&&(c[i[2]]=void 0)),{loaderData:c,errors:f,statusCode:h||200,loaderHeaders:m}}function Vf(t,r,i,l,a,c){let{loaderData:f,errors:h}=iv(r,i,l);return a.filter(p=>!p.matches||p.matches.some(m=>m.shouldLoad)).forEach(p=>{let{key:m,match:v,controller:S}=p,y=c[m];if(Ne(y,"Did not find corresponding fetcher result"),!(S&&S.signal.aborted))if(bt(y)){let x=Ar(t.matches,v?.route.id);h&&h[x.route.id]||(h={...h,[x.route.id]:y.error}),t.fetchers.delete(m)}else if(br(y))Ne(!1,"Unhandled fetcher revalidation redirect");else{let x=ur(y.data);t.fetchers.set(m,x)}}),{loaderData:f,errors:h}}function Uf(t,r,i,l){let a=Object.entries(r).filter(([,c])=>c!==Rh).reduce((c,[f,h])=>(c[f]=h,c),{});for(let c of i){let f=c.route.id;if(!r.hasOwnProperty(f)&&t.hasOwnProperty(f)&&c.route.loader&&(a[f]=t[f]),l&&l.hasOwnProperty(f))break}return a}function Hf(t){return t?bt(t[1])?{actionData:{}}:{actionData:{[t[0]]:t[1].data}}:{}}function Ar(t,r){return(r?t.slice(0,t.findIndex(l=>l.route.id===r)+1):[...t]).reverse().find(l=>l.route.hasErrorBoundary===!0)||t[0]}function Bf(t){let r=t.length===1?t[0]:t.find(i=>i.index||!i.path||i.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:r}],route:r}}function Kt(t,{pathname:r,routeId:i,method:l,type:a,message:c}={}){let f="Unknown Server Error",h="Unknown @remix-run/router error";return t===400?(f="Bad Request",l&&r&&i?h=`You made a ${l} request to "${r}" but did not provide a \`loader\` for route "${i}", so there is no way to handle the request.`:a==="invalid-body"&&(h="Unable to encode submission body")):t===403?(f="Forbidden",h=`Route "${i}" does not match URL "${r}"`):t===404?(f="Not Found",h=`No route matches URL "${r}"`):t===405&&(f="Method Not Allowed",l&&r&&i?h=`You made a ${l.toUpperCase()} request to "${r}" but did not provide an \`action\` for route "${i}", so there is no way to handle the request.`:l&&(h=`Invalid request method "${l.toUpperCase()}"`)),new ts(t||500,f,new Error(h),!0)}function Ul(t){let r=Object.entries(t);for(let i=r.length-1;i>=0;i--){let[l,a]=r[i];if(br(a))return{key:l,result:a}}}function Fh(t){let r=typeof t=="string"?mr(t):t;return hr({...r,hash:""})}function lv(t,r){return t.pathname!==r.pathname||t.search!==r.search?!1:t.hash===""?r.hash!=="":t.hash===r.hash?!0:r.hash!==""}function sv(t){return Lh(t.result)&&Gm.has(t.result.status)}function bt(t){return t.type==="error"}function br(t){return(t&&t.type)==="redirect"}function Gf(t){return typeof t=="object"&&t!=null&&"type"in t&&"data"in t&&"init"in t&&t.type==="DataWithResponseInit"}function Lh(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.headers=="object"&&typeof t.body<"u"}function av(t){return Bm.has(t.toUpperCase())}function Vt(t){return Um.has(t.toUpperCase())}function Fu(t){return new URLSearchParams(t).getAll("index").some(r=>r==="")}function xi(t,r){let i=typeof r=="string"?mr(r).search:r.search;if(t[t.length-1].route.index&&Fu(i||""))return t[t.length-1];let l=Sh(t);return l[l.length-1]}function Qf(t){let{formMethod:r,formAction:i,formEncType:l,text:a,formData:c,json:f}=t;if(!(!r||!i||!l)){if(a!=null)return{formMethod:r,formAction:i,formEncType:l,formData:void 0,json:void 0,text:a};if(c!=null)return{formMethod:r,formAction:i,formEncType:l,formData:c,json:void 0,text:void 0};if(f!==void 0)return{formMethod:r,formAction:i,formEncType:l,formData:void 0,json:f,text:void 0}}}function Ya(t,r){return r?{state:"loading",location:t,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}:{state:"loading",location:t,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function uv(t,r){return{state:"submitting",location:t,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}}function pi(t,r){return t?{state:"loading",formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text,data:r}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:r}}function cv(t,r){return{state:"submitting",formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text,data:r?r.data:void 0}}function ur(t){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function dv(t,r){try{let i=t.sessionStorage.getItem(Ch);if(i){let l=JSON.parse(i);for(let[a,c]of Object.entries(l||{}))c&&Array.isArray(c)&&r.set(a,new Set(c||[]))}}catch{}}function fv(t,r){if(r.size>0){let i={};for(let[l,a]of r)i[l]=[...a];try{t.sessionStorage.setItem(Ch,JSON.stringify(i))}catch(l){rt(!1,`Failed to save applied view transitions in sessionStorage (${l}).`)}}}function hv(){let t,r,i=new Promise((l,a)=>{t=async c=>{l(c);try{await i}catch{}},r=async c=>{a(c);try{await i}catch{}}});return{promise:i,resolve:t,reject:r}}var Ur=L.createContext(null);Ur.displayName="DataRouter";var _i=L.createContext(null);_i.displayName="DataRouterState";var Lu=L.createContext({isTransitioning:!1});Lu.displayName="ViewTransition";var Nh=L.createContext(new Map);Nh.displayName="Fetchers";var pv=L.createContext(null);pv.displayName="Await";var xn=L.createContext(null);xn.displayName="Navigation";var os=L.createContext(null);os.displayName="Location";var Cn=L.createContext({outlet:null,matches:[],isDataRoute:!1});Cn.displayName="Route";var Nu=L.createContext(null);Nu.displayName="RouteError";function gv(t,{relative:r}={}){Ne(Mi(),"useHref() may be used only in the context of a <Router> component.");let{basename:i,navigator:l}=L.useContext(xn),{hash:a,pathname:c,search:f}=Fi(t,{relative:r}),h=c;return i!=="/"&&(h=c==="/"?i:Sn([i,c])),l.createHref({pathname:h,search:f,hash:a})}function Mi(){return L.useContext(os)!=null}function vr(){return Ne(Mi(),"useLocation() may be used only in the context of a <Router> component."),L.useContext(os).location}var Dh="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function Th(t){L.useContext(xn).static||L.useLayoutEffect(t)}function jh(){let{isDataRoute:t}=L.useContext(Cn);return t?Lv():mv()}function mv(){Ne(Mi(),"useNavigate() may be used only in the context of a <Router> component.");let t=L.useContext(Ur),{basename:r,navigator:i}=L.useContext(xn),{matches:l}=L.useContext(Cn),{pathname:a}=vr(),c=JSON.stringify(ku(l)),f=L.useRef(!1);return Th(()=>{f.current=!0}),L.useCallback((p,m={})=>{if(rt(f.current,Dh),!f.current)return;if(typeof p=="number"){i.go(p);return}let v=_u(p,JSON.parse(c),a,m.relative==="path");t==null&&r!=="/"&&(v.pathname=v.pathname==="/"?r:Sn([r,v.pathname])),(m.replace?i.replace:i.push)(v,m.state,m)},[r,i,c,a,t])}var vv=L.createContext(null);function yv(t){let r=L.useContext(Cn).outlet;return r&&L.createElement(vv.Provider,{value:t},r)}function Fi(t,{relative:r}={}){let{matches:i}=L.useContext(Cn),{pathname:l}=vr(),a=JSON.stringify(ku(i));return L.useMemo(()=>_u(t,JSON.parse(a),l,r==="path"),[t,a,l,r])}function wv(t,r,i,l){Ne(Mi(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=L.useContext(xn),{matches:c}=L.useContext(Cn),f=c[c.length-1],h=f?f.params:{},p=f?f.pathname:"/",m=f?f.pathnameBase:"/",v=f&&f.route;{let F=v&&v.path||"";zh(p,!v||F.endsWith("*")||F.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${F}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${F}"> to <Route path="${F==="/"?"*":`${F}/*`}">.`)}let S=vr(),y;y=S;let x=y.pathname||"/",C=x;if(m!=="/"){let F=m.replace(/^\//,"").split("/");C="/"+x.replace(/^\//,"").split("/").slice(F.length).join("/")}let P=cr(t,{pathname:C});return rt(v||P!=null,`No routes matched location "${y.pathname}${y.search}${y.hash}" `),rt(P==null||P[P.length-1].route.element!==void 0||P[P.length-1].route.Component!==void 0||P[P.length-1].route.lazy!==void 0,`Matched leaf route at location "${y.pathname}${y.search}${y.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Ev(P&&P.map(F=>Object.assign({},F,{params:Object.assign({},h,F.params),pathname:Sn([m,a.encodeLocation?a.encodeLocation(F.pathname).pathname:F.pathname]),pathnameBase:F.pathnameBase==="/"?m:Sn([m,a.encodeLocation?a.encodeLocation(F.pathnameBase).pathname:F.pathnameBase])})),c,i,l)}function Sv(){let t=Fv(),r=Ei(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),i=t instanceof Error?t.stack:null,l="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:l},c={padding:"2px 4px",backgroundColor:l},f=null;return console.error("Error handled by React Router default ErrorBoundary:",t),f=L.createElement(L.Fragment,null,L.createElement("p",null,"💿 Hey developer 👋"),L.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",L.createElement("code",{style:c},"ErrorBoundary")," or"," ",L.createElement("code",{style:c},"errorElement")," prop on your route.")),L.createElement(L.Fragment,null,L.createElement("h2",null,"Unexpected Application Error!"),L.createElement("h3",{style:{fontStyle:"italic"}},r),i?L.createElement("pre",{style:a},i):null,f)}var xv=L.createElement(Sv,null),Cv=class extends L.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?L.createElement(Cn.Provider,{value:this.props.routeContext},L.createElement(Nu.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Rv({routeContext:t,match:r,children:i}){let l=L.useContext(Ur);return l&&l.static&&l.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=r.route.id),L.createElement(Cn.Provider,{value:t},i)}function Ev(t,r=[],i=null,l=null){if(t==null){if(!i)return null;if(i.errors)t=i.matches;else if(r.length===0&&!i.initialized&&i.matches.length>0)t=i.matches;else return null}let a=t,c=i?.errors;if(c!=null){let p=a.findIndex(m=>m.route.id&&c?.[m.route.id]!==void 0);Ne(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(c).join(",")}`),a=a.slice(0,Math.min(a.length,p+1))}let f=!1,h=-1;if(i)for(let p=0;p<a.length;p++){let m=a[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:v,errors:S}=i,y=m.route.loader&&!v.hasOwnProperty(m.route.id)&&(!S||S[m.route.id]===void 0);if(m.route.lazy||y){f=!0,h>=0?a=a.slice(0,h+1):a=[a[0]];break}}}return a.reduceRight((p,m,v)=>{let S,y=!1,x=null,C=null;i&&(S=c&&m.route.id?c[m.route.id]:void 0,x=m.route.errorElement||xv,f&&(h<0&&v===0?(zh("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),y=!0,C=null):h===v&&(y=!0,C=m.route.hydrateFallbackElement||null)));let P=r.concat(a.slice(0,v+1)),T=()=>{let F;return S?F=x:y?F=C:m.route.Component?F=L.createElement(m.route.Component,null):m.route.element?F=m.route.element:F=p,L.createElement(Rv,{match:m,routeContext:{outlet:p,matches:P,isDataRoute:i!=null},children:F})};return i&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?L.createElement(Cv,{location:i.location,revalidation:i.revalidation,component:x,error:S,children:T(),routeContext:{outlet:null,matches:P,isDataRoute:!0}}):T()},null)}function Du(t){return`${t} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Pv(t){let r=L.useContext(Ur);return Ne(r,Du(t)),r}function kv(t){let r=L.useContext(_i);return Ne(r,Du(t)),r}function _v(t){let r=L.useContext(Cn);return Ne(r,Du(t)),r}function Tu(t){let r=_v(t),i=r.matches[r.matches.length-1];return Ne(i.route.id,`${t} can only be used on routes that contain a unique "id"`),i.route.id}function Mv(){return Tu("useRouteId")}function Fv(){let t=L.useContext(Nu),r=kv("useRouteError"),i=Tu("useRouteError");return t!==void 0?t:r.errors?.[i]}function Lv(){let{router:t}=Pv("useNavigate"),r=Tu("useNavigate"),i=L.useRef(!1);return Th(()=>{i.current=!0}),L.useCallback(async(a,c={})=>{rt(i.current,Dh),i.current&&(typeof a=="number"?t.navigate(a):await t.navigate(a,{fromRouteId:r,...c}))},[t,r])}var Wf={};function zh(t,r,i){!r&&!Wf[t]&&(Wf[t]=!0,rt(!1,i))}var qf={};function Kf(t,r){!t&&!qf[r]&&(qf[r]=!0,console.warn(r))}function Nv(t){let r={hasErrorBoundary:t.hasErrorBoundary||t.ErrorBoundary!=null||t.errorElement!=null};return t.Component&&(t.element&&rt(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(r,{element:L.createElement(t.Component),Component:void 0})),t.HydrateFallback&&(t.hydrateFallbackElement&&rt(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(r,{hydrateFallbackElement:L.createElement(t.HydrateFallback),HydrateFallback:void 0})),t.ErrorBoundary&&(t.errorElement&&rt(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(r,{errorElement:L.createElement(t.ErrorBoundary),ErrorBoundary:void 0})),r}var Dv=["HydrateFallback","hydrateFallbackElement"],Tv=class{constructor(){this.status="pending",this.promise=new Promise((t,r)=>{this.resolve=i=>{this.status==="pending"&&(this.status="resolved",t(i))},this.reject=i=>{this.status==="pending"&&(this.status="rejected",r(i))}})}};function jv({router:t,flushSync:r}){let[i,l]=L.useState(t.state),[a,c]=L.useState(),[f,h]=L.useState({isTransitioning:!1}),[p,m]=L.useState(),[v,S]=L.useState(),[y,x]=L.useState(),C=L.useRef(new Map),P=L.useCallback((A,{deletedFetchers:K,flushSync:Z,viewTransitionOpts:j})=>{A.fetchers.forEach((se,ue)=>{se.data!==void 0&&C.current.set(ue,se.data)}),K.forEach(se=>C.current.delete(se)),Kf(Z===!1||r!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let ie=t.window!=null&&t.window.document!=null&&typeof t.window.document.startViewTransition=="function";if(Kf(j==null||ie,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!j||!ie){r&&Z?r(()=>l(A)):L.startTransition(()=>l(A));return}if(r&&Z){r(()=>{v&&(p&&p.resolve(),v.skipTransition()),h({isTransitioning:!0,flushSync:!0,currentLocation:j.currentLocation,nextLocation:j.nextLocation})});let se=t.window.document.startViewTransition(()=>{r(()=>l(A))});se.finished.finally(()=>{r(()=>{m(void 0),S(void 0),c(void 0),h({isTransitioning:!1})})}),r(()=>S(se));return}v?(p&&p.resolve(),v.skipTransition(),x({state:A,currentLocation:j.currentLocation,nextLocation:j.nextLocation})):(c(A),h({isTransitioning:!0,flushSync:!1,currentLocation:j.currentLocation,nextLocation:j.nextLocation}))},[t.window,r,v,p]);L.useLayoutEffect(()=>t.subscribe(P),[t,P]),L.useEffect(()=>{f.isTransitioning&&!f.flushSync&&m(new Tv)},[f]),L.useEffect(()=>{if(p&&a&&t.window){let A=a,K=p.promise,Z=t.window.document.startViewTransition(async()=>{L.startTransition(()=>l(A)),await K});Z.finished.finally(()=>{m(void 0),S(void 0),c(void 0),h({isTransitioning:!1})}),S(Z)}},[a,p,t.window]),L.useEffect(()=>{p&&a&&i.location.key===a.location.key&&p.resolve()},[p,v,i.location,a]),L.useEffect(()=>{!f.isTransitioning&&y&&(c(y.state),h({isTransitioning:!0,flushSync:!1,currentLocation:y.currentLocation,nextLocation:y.nextLocation}),x(void 0))},[f.isTransitioning,y]);let T=L.useMemo(()=>({createHref:t.createHref,encodeLocation:t.encodeLocation,go:A=>t.navigate(A),push:(A,K,Z)=>t.navigate(A,{state:K,preventScrollReset:Z?.preventScrollReset}),replace:(A,K,Z)=>t.navigate(A,{replace:!0,state:K,preventScrollReset:Z?.preventScrollReset})}),[t]),F=t.basename||"/",B=L.useMemo(()=>({router:t,navigator:T,static:!1,basename:F}),[t,T,F]);return L.createElement(L.Fragment,null,L.createElement(Ur.Provider,{value:B},L.createElement(_i.Provider,{value:i},L.createElement(Nh.Provider,{value:C.current},L.createElement(Lu.Provider,{value:f},L.createElement($v,{basename:F,location:i.location,navigationType:i.historyAction,navigator:T},L.createElement(zv,{routes:t.routes,future:t.future,state:i})))))),null)}var zv=L.memo(Ov);function Ov({routes:t,future:r,state:i}){return wv(t,void 0,i,r)}function Iv(t){return yv(t.context)}function $v({basename:t="/",children:r=null,location:i,navigationType:l="POP",navigator:a,static:c=!1}){Ne(!Mi(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let f=t.replace(/^\/*/,"/"),h=L.useMemo(()=>({basename:f,navigator:a,static:c,future:{}}),[f,a,c]);typeof i=="string"&&(i=mr(i));let{pathname:p="/",search:m="",hash:v="",state:S=null,key:y="default"}=i,x=L.useMemo(()=>{let C=Yt(p,f);return C==null?null:{location:{pathname:C,search:m,hash:v,state:S,key:y},navigationType:l}},[f,p,m,v,S,y,l]);return rt(x!=null,`<Router basename="${f}"> is not able to match the URL "${p}${m}${v}" because it does not start with the basename, so the <Router> won't render anything.`),x==null?null:L.createElement(xn.Provider,{value:h},L.createElement(os.Provider,{children:r,value:x}))}var Kl="get",Yl="application/x-www-form-urlencoded";function is(t){return t!=null&&typeof t.tagName=="string"}function Av(t){return is(t)&&t.tagName.toLowerCase()==="button"}function bv(t){return is(t)&&t.tagName.toLowerCase()==="form"}function Vv(t){return is(t)&&t.tagName.toLowerCase()==="input"}function Uv(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function Hv(t,r){return t.button===0&&(!r||r==="_self")&&!Uv(t)}var Hl=null;function Bv(){if(Hl===null)try{new FormData(document.createElement("form"),0),Hl=!1}catch{Hl=!0}return Hl}var Gv=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Xa(t){return t!=null&&!Gv.has(t)?(rt(!1,`"${t}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Yl}"`),null):t}function Qv(t,r){let i,l,a,c,f;if(bv(t)){let h=t.getAttribute("action");l=h?Yt(h,r):null,i=t.getAttribute("method")||Kl,a=Xa(t.getAttribute("enctype"))||Yl,c=new FormData(t)}else if(Av(t)||Vv(t)&&(t.type==="submit"||t.type==="image")){let h=t.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=t.getAttribute("formaction")||h.getAttribute("action");if(l=p?Yt(p,r):null,i=t.getAttribute("formmethod")||h.getAttribute("method")||Kl,a=Xa(t.getAttribute("formenctype"))||Xa(h.getAttribute("enctype"))||Yl,c=new FormData(h,t),!Bv()){let{name:m,type:v,value:S}=t;if(v==="image"){let y=m?`${m}.`:"";c.append(`${y}x`,"0"),c.append(`${y}y`,"0")}else m&&c.append(m,S)}}else{if(is(t))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');i=Kl,l=null,a=Yl,f=t}return c&&a==="text/plain"&&(f=c,c=void 0),{action:l,method:i.toLowerCase(),encType:a,formData:c,body:f}}function ju(t,r){if(t===!1||t===null||typeof t>"u")throw new Error(r)}async function Wv(t,r){if(t.id in r)return r[t.id];try{let i=await import(t.module);return r[t.id]=i,i}catch(i){return console.error(`Error loading route module \`${t.module}\`, reloading page...`),console.error(i),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function qv(t){return t==null?!1:t.href==null?t.rel==="preload"&&typeof t.imageSrcSet=="string"&&typeof t.imageSizes=="string":typeof t.rel=="string"&&typeof t.href=="string"}async function Kv(t,r,i){let l=await Promise.all(t.map(async a=>{let c=r.routes[a.route.id];if(c){let f=await Wv(c,i);return f.links?f.links():[]}return[]}));return Jv(l.flat(1).filter(qv).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Yf(t,r,i,l,a,c){let f=(p,m)=>i[m]?p.route.id!==i[m].route.id:!0,h=(p,m)=>i[m].pathname!==p.pathname||i[m].route.path?.endsWith("*")&&i[m].params["*"]!==p.params["*"];return c==="assets"?r.filter((p,m)=>f(p,m)||h(p,m)):c==="data"?r.filter((p,m)=>{let v=l.routes[p.route.id];if(!v||!v.hasLoader)return!1;if(f(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let S=p.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:i[0]?.params||{},nextUrl:new URL(t,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof S=="boolean")return S}return!0}):[]}function Yv(t,r,{includeHydrateFallback:i}={}){return Xv(t.map(l=>{let a=r.routes[l.route.id];if(!a)return[];let c=[a.module];return a.clientActionModule&&(c=c.concat(a.clientActionModule)),a.clientLoaderModule&&(c=c.concat(a.clientLoaderModule)),i&&a.hydrateFallbackModule&&(c=c.concat(a.hydrateFallbackModule)),a.imports&&(c=c.concat(a.imports)),c}).flat(1))}function Xv(t){return[...new Set(t)]}function Zv(t){let r={},i=Object.keys(t).sort();for(let l of i)r[l]=t[l];return r}function Jv(t,r){let i=new Set;return new Set(r),t.reduce((l,a)=>{let c=JSON.stringify(Zv(a));return i.has(c)||(i.add(c),l.push({key:c,link:a})),l},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var ey=new Set([100,101,204,205]);function ty(t,r){let i=typeof t=="string"?new URL(t,typeof window>"u"?"server://singlefetch/":window.location.origin):t;return i.pathname==="/"?i.pathname="_root.data":r&&Yt(i.pathname,r)==="/"?i.pathname=`${r.replace(/\/$/,"")}/_root.data`:i.pathname=`${i.pathname.replace(/\/$/,"")}.data`,i}function Oh(){let t=L.useContext(Ur);return ju(t,"You must render this element inside a <DataRouterContext.Provider> element"),t}function ny(){let t=L.useContext(_i);return ju(t,"You must render this element inside a <DataRouterStateContext.Provider> element"),t}var zu=L.createContext(void 0);zu.displayName="FrameworkContext";function Ih(){let t=L.useContext(zu);return ju(t,"You must render this element inside a <HydratedRouter> element"),t}function ry(t,r){let i=L.useContext(zu),[l,a]=L.useState(!1),[c,f]=L.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:v,onTouchStart:S}=r,y=L.useRef(null);L.useEffect(()=>{if(t==="render"&&f(!0),t==="viewport"){let P=F=>{F.forEach(B=>{f(B.isIntersecting)})},T=new IntersectionObserver(P,{threshold:.5});return y.current&&T.observe(y.current),()=>{T.disconnect()}}},[t]),L.useEffect(()=>{if(l){let P=setTimeout(()=>{f(!0)},100);return()=>{clearTimeout(P)}}},[l]);let x=()=>{a(!0)},C=()=>{a(!1),f(!1)};return i?t!=="intent"?[c,y,{}]:[c,y,{onFocus:gi(h,x),onBlur:gi(p,C),onMouseEnter:gi(m,x),onMouseLeave:gi(v,C),onTouchStart:gi(S,x)}]:[!1,y,{}]}function gi(t,r){return i=>{t&&t(i),i.defaultPrevented||r(i)}}function oy({page:t,...r}){let{router:i}=Oh(),l=L.useMemo(()=>cr(i.routes,t,i.basename),[i.routes,t,i.basename]);return l?L.createElement(ly,{page:t,matches:l,...r}):null}function iy(t){let{manifest:r,routeModules:i}=Ih(),[l,a]=L.useState([]);return L.useEffect(()=>{let c=!1;return Kv(t,r,i).then(f=>{c||a(f)}),()=>{c=!0}},[t,r,i]),l}function ly({page:t,matches:r,...i}){let l=vr(),{manifest:a,routeModules:c}=Ih(),{basename:f}=Oh(),{loaderData:h,matches:p}=ny(),m=L.useMemo(()=>Yf(t,r,p,a,l,"data"),[t,r,p,a,l]),v=L.useMemo(()=>Yf(t,r,p,a,l,"assets"),[t,r,p,a,l]),S=L.useMemo(()=>{if(t===l.pathname+l.search+l.hash)return[];let C=new Set,P=!1;if(r.forEach(F=>{let B=a.routes[F.route.id];!B||!B.hasLoader||(!m.some(A=>A.route.id===F.route.id)&&F.route.id in h&&c[F.route.id]?.shouldRevalidate||B.hasClientLoader?P=!0:C.add(F.route.id))}),C.size===0)return[];let T=ty(t,f);return P&&C.size>0&&T.searchParams.set("_routes",r.filter(F=>C.has(F.route.id)).map(F=>F.route.id).join(",")),[T.pathname+T.search]},[f,h,l,a,m,r,t,c]),y=L.useMemo(()=>Yv(v,a),[v,a]),x=iy(v);return L.createElement(L.Fragment,null,S.map(C=>L.createElement("link",{key:C,rel:"prefetch",as:"fetch",href:C,...i})),y.map(C=>L.createElement("link",{key:C,rel:"modulepreload",href:C,...i})),x.map(({key:C,link:P})=>L.createElement("link",{key:C,...P})))}function sy(...t){return r=>{t.forEach(i=>{typeof i=="function"?i(r):i!=null&&(i.current=r)})}}var $h=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{$h&&(window.__reactRouterVersion="7.6.3")}catch{}function ay(t,r){return Km({basename:r?.basename,unstable_getContext:r?.unstable_getContext,future:r?.future,history:mm({window:r?.window}),hydrationData:uy(),routes:t,mapRouteProperties:Nv,hydrationRouteProperties:Dv,dataStrategy:r?.dataStrategy,patchRoutesOnNavigation:r?.patchRoutesOnNavigation,window:r?.window}).initialize()}function uy(){let t=window?.__staticRouterHydrationData;return t&&t.errors&&(t={...t,errors:cy(t.errors)}),t}function cy(t){if(!t)return null;let r=Object.entries(t),i={};for(let[l,a]of r)if(a&&a.__type==="RouteErrorResponse")i[l]=new ts(a.status,a.statusText,a.data,a.internal===!0);else if(a&&a.__type==="Error"){if(a.__subType){let c=window[a.__subType];if(typeof c=="function")try{let f=new c(a.message);f.stack="",i[l]=f}catch{}}if(i[l]==null){let c=new Error(a.message);c.stack="",i[l]=c}}else i[l]=a;return i}var Ah=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,bh=L.forwardRef(function({onClick:r,discover:i="render",prefetch:l="none",relative:a,reloadDocument:c,replace:f,state:h,target:p,to:m,preventScrollReset:v,viewTransition:S,...y},x){let{basename:C}=L.useContext(xn),P=typeof m=="string"&&Ah.test(m),T,F=!1;if(typeof m=="string"&&P&&(T=m,$h))try{let ue=new URL(window.location.href),Ce=m.startsWith("//")?new URL(ue.protocol+m):new URL(m),Ie=Yt(Ce.pathname,C);Ce.origin===ue.origin&&Ie!=null?m=Ie+Ce.search+Ce.hash:F=!0}catch{rt(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let B=gv(m,{relative:a}),[A,K,Z]=ry(l,y),j=py(m,{replace:f,state:h,target:p,preventScrollReset:v,relative:a,viewTransition:S});function ie(ue){r&&r(ue),ue.defaultPrevented||j(ue)}let se=L.createElement("a",{...y,...Z,href:T||B,onClick:F||c?r:ie,ref:sy(x,K),target:p,"data-discover":!P&&i==="render"?"true":void 0});return A&&!P?L.createElement(L.Fragment,null,se,L.createElement(oy,{page:B})):se});bh.displayName="Link";var dy=L.forwardRef(function({"aria-current":r="page",caseSensitive:i=!1,className:l="",end:a=!1,style:c,to:f,viewTransition:h,children:p,...m},v){let S=Fi(f,{relative:m.relative}),y=vr(),x=L.useContext(_i),{navigator:C,basename:P}=L.useContext(xn),T=x!=null&&wy(S)&&h===!0,F=C.encodeLocation?C.encodeLocation(S).pathname:S.pathname,B=y.pathname,A=x&&x.navigation&&x.navigation.location?x.navigation.location.pathname:null;i||(B=B.toLowerCase(),A=A?A.toLowerCase():null,F=F.toLowerCase()),A&&P&&(A=Yt(A,P)||A);const K=F!=="/"&&F.endsWith("/")?F.length-1:F.length;let Z=B===F||!a&&B.startsWith(F)&&B.charAt(K)==="/",j=A!=null&&(A===F||!a&&A.startsWith(F)&&A.charAt(F.length)==="/"),ie={isActive:Z,isPending:j,isTransitioning:T},se=Z?r:void 0,ue;typeof l=="function"?ue=l(ie):ue=[l,Z?"active":null,j?"pending":null,T?"transitioning":null].filter(Boolean).join(" ");let Ce=typeof c=="function"?c(ie):c;return L.createElement(bh,{...m,"aria-current":se,className:ue,ref:v,style:Ce,to:f,viewTransition:h},typeof p=="function"?p(ie):p)});dy.displayName="NavLink";var fy=L.forwardRef(({discover:t="render",fetcherKey:r,navigate:i,reloadDocument:l,replace:a,state:c,method:f=Kl,action:h,onSubmit:p,relative:m,preventScrollReset:v,viewTransition:S,...y},x)=>{let C=vy(),P=yy(h,{relative:m}),T=f.toLowerCase()==="get"?"get":"post",F=typeof h=="string"&&Ah.test(h),B=A=>{if(p&&p(A),A.defaultPrevented)return;A.preventDefault();let K=A.nativeEvent.submitter,Z=K?.getAttribute("formmethod")||f;C(K||A.currentTarget,{fetcherKey:r,method:Z,navigate:i,replace:a,state:c,relative:m,preventScrollReset:v,viewTransition:S})};return L.createElement("form",{ref:x,method:T,action:P,onSubmit:l?p:B,...y,"data-discover":!F&&t==="render"?"true":void 0})});fy.displayName="Form";function hy(t){return`${t} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Vh(t){let r=L.useContext(Ur);return Ne(r,hy(t)),r}function py(t,{target:r,replace:i,state:l,preventScrollReset:a,relative:c,viewTransition:f}={}){let h=jh(),p=vr(),m=Fi(t,{relative:c});return L.useCallback(v=>{if(Hv(v,r)){v.preventDefault();let S=i!==void 0?i:hr(p)===hr(m);h(t,{replace:S,state:l,preventScrollReset:a,relative:c,viewTransition:f})}},[p,h,m,i,l,r,t,a,c,f])}var gy=0,my=()=>`__${String(++gy)}__`;function vy(){let{router:t}=Vh("useSubmit"),{basename:r}=L.useContext(xn),i=Mv();return L.useCallback(async(l,a={})=>{let{action:c,method:f,encType:h,formData:p,body:m}=Qv(l,r);if(a.navigate===!1){let v=a.fetcherKey||my();await t.fetch(v,i,a.action||c,{preventScrollReset:a.preventScrollReset,formData:p,body:m,formMethod:a.method||f,formEncType:a.encType||h,flushSync:a.flushSync})}else await t.navigate(a.action||c,{preventScrollReset:a.preventScrollReset,formData:p,body:m,formMethod:a.method||f,formEncType:a.encType||h,replace:a.replace,state:a.state,fromRouteId:i,flushSync:a.flushSync,viewTransition:a.viewTransition})},[t,r,i])}function yy(t,{relative:r}={}){let{basename:i}=L.useContext(xn),l=L.useContext(Cn);Ne(l,"useFormAction must be used inside a RouteContext");let[a]=l.matches.slice(-1),c={...Fi(t||".",{relative:r})},f=vr();if(t==null){c.search=f.search;let h=new URLSearchParams(c.search),p=h.getAll("index");if(p.some(v=>v==="")){h.delete("index"),p.filter(S=>S).forEach(S=>h.append("index",S));let v=h.toString();c.search=v?`?${v}`:""}}return(!t||t===".")&&a.route.index&&(c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index"),i!=="/"&&(c.pathname=c.pathname==="/"?i:Sn([i,c.pathname])),hr(c)}function wy(t,r={}){let i=L.useContext(Lu);Ne(i!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:l}=Vh("useViewTransitionState"),a=Fi(t,{relative:r.relative});if(!i.isTransitioning)return!1;let c=Yt(i.currentLocation.pathname,l)||i.currentLocation.pathname,f=Yt(i.nextLocation.pathname,l)||i.nextLocation.pathname;return es(a.pathname,f)!=null||es(a.pathname,c)!=null}[...ey];var Sy=gh();/**
 * react-router v7.6.3
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function xy(t){return L.createElement(jv,{flushSync:Sy.flushSync,...t})}/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Uh=(...t)=>t.filter((r,i,l)=>!!r&&r.trim()!==""&&l.indexOf(r)===i).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Ry={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ey=L.forwardRef(({color:t="currentColor",size:r=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:a="",children:c,iconNode:f,...h},p)=>L.createElement("svg",{ref:p,...Ry,width:r,height:r,stroke:t,strokeWidth:l?Number(i)*24/Number(r):i,className:Uh("lucide",a),...h},[...f.map(([m,v])=>L.createElement(m,v)),...Array.isArray(c)?c:[c]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jt=(t,r)=>{const i=L.forwardRef(({className:l,...a},c)=>L.createElement(Ey,{ref:c,iconNode:r,className:Uh(`lucide-${Cy(t)}`,l),...a}));return i.displayName=`${t}`,i};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Py=jt("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xl=jt("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ky=jt("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=jt("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const My=jt("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hh=jt("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const du=jt("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bh=jt("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fy=jt("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ly=jt("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=jt("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dy=jt("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ty=jt("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=jt("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),zy=[{id:"dashboard",title:"仪表盘",icon:Xl,path:"/"},{id:"publish",title:"项目管理",icon:du,children:[{id:"publish-service",title:"构建记录",icon:du,path:"/system/builds"},{id:"task-service",title:"崩溃记录",icon:Xl,path:""}]},{id:"tools",title:"Agent管理",icon:Fy,children:[{id:"package-management",title:"LLM管理",icon:Hh,path:"/tools/packages"},{id:"publish-stats",title:"Agents",icon:Xl,path:"/tools/stats"}]}],Oy=({collapsed:t,onToggle:r})=>{const[i,l]=L.useState(["publish","tools"]),a=jh(),f=vr().pathname,h=v=>{l(S=>S.includes(v)?S.filter(y=>y!==v):[...S,v])},p=v=>{v.children&&v.children.length>0?h(v.id):v.path&&a(v.path)},m=(v,S=0)=>{const y=v.children&&v.children.length>0,x=i.includes(v.id),C=f===v.path,P=v.icon;return M.jsxs("div",{className:"mb-1",children:[M.jsxs("div",{className:`
            flex items-center px-3 py-2 text-sm rounded-lg cursor-pointer
            transition-all duration-200 hover:bg-blue-700/50
            ${S>0?"ml-4 text-blue-200":"text-blue-100"}
            ${t&&S===0?"justify-center":""}
            ${C?"bg-blue-700 text-white":""}
          `,onClick:()=>p(v),children:[M.jsx(P,{className:`${t&&S===0?"w-5 h-5":"w-4 h-4 mr-3"} flex-shrink-0`}),(!t||S>0)&&M.jsxs(M.Fragment,{children:[M.jsx("span",{className:"flex-1",children:v.title}),y&&(x?M.jsx(ky,{className:"w-4 h-4"}):M.jsx(_y,{className:"w-4 h-4"}))]})]}),y&&x&&(!t||S>0)&&M.jsx("div",{className:"mt-1",children:v.children?.map(T=>m(T,S+1))})]},v.id)};return M.jsx("div",{className:`
      bg-slate-800 text-white transition-all duration-300 flex-shrink-0
      ${t?"w-16":"w-64"}
    `,children:M.jsxs("div",{className:"p-4",children:[M.jsxs("div",{className:"flex items-center justify-between mb-6",children:[!t&&M.jsx("h1",{className:"text-lg font-semibold text-blue-100",children:"管理后台"}),M.jsx("button",{onClick:r,className:"p-1 rounded-lg hover:bg-blue-700/50 transition-colors",children:M.jsx(Bh,{className:"w-5 h-5"})})]}),M.jsx("nav",{className:"space-y-2",children:zy.map(v=>m(v))})]})})},Iy=({onMenuToggle:t,sidebarCollapsed:r})=>M.jsx("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:M.jsxs("div",{className:"flex items-center justify-between",children:[M.jsxs("div",{className:"flex items-center space-x-4",children:[M.jsx("button",{onClick:t,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors",children:M.jsx(Bh,{className:"w-5 h-5 text-gray-600"})}),M.jsxs("div",{className:"text-sm text-gray-500",children:[M.jsx("span",{className:"font-medium",children:"版本信息:"})," ver 1.6.0.1600"]})]}),M.jsxs("div",{className:"flex items-center space-x-4",children:[M.jsxs("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors relative",children:[M.jsx(Py,{className:"w-5 h-5 text-gray-600"}),M.jsx("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"})]}),M.jsxs("div",{className:"flex items-center space-x-3",children:[M.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:M.jsx(Ty,{className:"w-4 h-4 text-white"})}),M.jsxs("div",{className:"text-sm",children:[M.jsx("div",{className:"font-medium text-gray-900",children:"管理员"}),M.jsx("div",{className:"text-gray-500",children:"administrator"})]})]})]})]})}),$y=()=>{const[t,r]=L.useState(!1);return M.jsxs("div",{className:"min-h-screen bg-gray-50 flex w-full",children:[M.jsx(Oy,{collapsed:t,onToggle:()=>r(!t)}),M.jsxs("div",{className:"flex-1 flex flex-col",children:[M.jsx(Iy,{onMenuToggle:()=>r(!t),sidebarCollapsed:t}),M.jsx("main",{className:"flex-1 p-6 overflow-auto",children:M.jsx(Iv,{})})]})]})};function Gh(t){var r,i,l="";if(typeof t=="string"||typeof t=="number")l+=t;else if(typeof t=="object")if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(i=Gh(t[r]))&&(l&&(l+=" "),l+=i)}else for(i in t)t[i]&&(l&&(l+=" "),l+=i);return l}function Qh(){for(var t,r,i=0,l="",a=arguments.length;i<a;i++)(t=arguments[i])&&(r=Gh(t))&&(l&&(l+=" "),l+=r);return l}const Ou="-",Ay=t=>{const r=Vy(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:l}=t;return{getClassGroupId:f=>{const h=f.split(Ou);return h[0]===""&&h.length!==1&&h.shift(),Wh(h,r)||by(f)},getConflictingClassGroupIds:(f,h)=>{const p=i[f]||[];return h&&l[f]?[...p,...l[f]]:p}}},Wh=(t,r)=>{if(t.length===0)return r.classGroupId;const i=t[0],l=r.nextPart.get(i),a=l?Wh(t.slice(1),l):void 0;if(a)return a;if(r.validators.length===0)return;const c=t.join(Ou);return r.validators.find(({validator:f})=>f(c))?.classGroupId},Xf=/^\[(.+)\]$/,by=t=>{if(Xf.test(t)){const r=Xf.exec(t)[1],i=r?.substring(0,r.indexOf(":"));if(i)return"arbitrary.."+i}},Vy=t=>{const{theme:r,prefix:i}=t,l={nextPart:new Map,validators:[]};return Hy(Object.entries(t.classGroups),i).forEach(([c,f])=>{fu(f,l,c,r)}),l},fu=(t,r,i,l)=>{t.forEach(a=>{if(typeof a=="string"){const c=a===""?r:Zf(r,a);c.classGroupId=i;return}if(typeof a=="function"){if(Uy(a)){fu(a(l),r,i,l);return}r.validators.push({validator:a,classGroupId:i});return}Object.entries(a).forEach(([c,f])=>{fu(f,Zf(r,c),i,l)})})},Zf=(t,r)=>{let i=t;return r.split(Ou).forEach(l=>{i.nextPart.has(l)||i.nextPart.set(l,{nextPart:new Map,validators:[]}),i=i.nextPart.get(l)}),i},Uy=t=>t.isThemeGetter,Hy=(t,r)=>r?t.map(([i,l])=>{const a=l.map(c=>typeof c=="string"?r+c:typeof c=="object"?Object.fromEntries(Object.entries(c).map(([f,h])=>[r+f,h])):c);return[i,a]}):t,By=t=>{if(t<1)return{get:()=>{},set:()=>{}};let r=0,i=new Map,l=new Map;const a=(c,f)=>{i.set(c,f),r++,r>t&&(r=0,l=i,i=new Map)};return{get(c){let f=i.get(c);if(f!==void 0)return f;if((f=l.get(c))!==void 0)return a(c,f),f},set(c,f){i.has(c)?i.set(c,f):a(c,f)}}},qh="!",Gy=t=>{const{separator:r,experimentalParseClassName:i}=t,l=r.length===1,a=r[0],c=r.length,f=h=>{const p=[];let m=0,v=0,S;for(let T=0;T<h.length;T++){let F=h[T];if(m===0){if(F===a&&(l||h.slice(T,T+c)===r)){p.push(h.slice(v,T)),v=T+c;continue}if(F==="/"){S=T;continue}}F==="["?m++:F==="]"&&m--}const y=p.length===0?h:h.substring(v),x=y.startsWith(qh),C=x?y.substring(1):y,P=S&&S>v?S-v:void 0;return{modifiers:p,hasImportantModifier:x,baseClassName:C,maybePostfixModifierPosition:P}};return i?h=>i({className:h,parseClassName:f}):f},Qy=t=>{if(t.length<=1)return t;const r=[];let i=[];return t.forEach(l=>{l[0]==="["?(r.push(...i.sort(),l),i=[]):i.push(l)}),r.push(...i.sort()),r},Wy=t=>({cache:By(t.cacheSize),parseClassName:Gy(t),...Ay(t)}),qy=/\s+/,Ky=(t,r)=>{const{parseClassName:i,getClassGroupId:l,getConflictingClassGroupIds:a}=r,c=[],f=t.trim().split(qy);let h="";for(let p=f.length-1;p>=0;p-=1){const m=f[p],{modifiers:v,hasImportantModifier:S,baseClassName:y,maybePostfixModifierPosition:x}=i(m);let C=!!x,P=l(C?y.substring(0,x):y);if(!P){if(!C){h=m+(h.length>0?" "+h:h);continue}if(P=l(y),!P){h=m+(h.length>0?" "+h:h);continue}C=!1}const T=Qy(v).join(":"),F=S?T+qh:T,B=F+P;if(c.includes(B))continue;c.push(B);const A=a(P,C);for(let K=0;K<A.length;++K){const Z=A[K];c.push(F+Z)}h=m+(h.length>0?" "+h:h)}return h};function Yy(){let t=0,r,i,l="";for(;t<arguments.length;)(r=arguments[t++])&&(i=Kh(r))&&(l&&(l+=" "),l+=i);return l}const Kh=t=>{if(typeof t=="string")return t;let r,i="";for(let l=0;l<t.length;l++)t[l]&&(r=Kh(t[l]))&&(i&&(i+=" "),i+=r);return i};function Xy(t,...r){let i,l,a,c=f;function f(p){const m=r.reduce((v,S)=>S(v),t());return i=Wy(m),l=i.cache.get,a=i.cache.set,c=h,h(p)}function h(p){const m=l(p);if(m)return m;const v=Ky(p,i);return a(p,v),v}return function(){return c(Yy.apply(null,arguments))}}const qe=t=>{const r=i=>i[t]||[];return r.isThemeGetter=!0,r},Yh=/^\[(?:([a-z-]+):)?(.+)\]$/i,Zy=/^\d+\/\d+$/,Jy=new Set(["px","full","screen"]),e0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,t0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,n0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,r0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,o0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Tn=t=>So(t)||Jy.has(t)||Zy.test(t),lr=t=>Ro(t,"length",f0),So=t=>!!t&&!Number.isNaN(Number(t)),Za=t=>Ro(t,"number",So),mi=t=>!!t&&Number.isInteger(Number(t)),i0=t=>t.endsWith("%")&&So(t.slice(0,-1)),Ee=t=>Yh.test(t),sr=t=>e0.test(t),l0=new Set(["length","size","percentage"]),s0=t=>Ro(t,l0,Xh),a0=t=>Ro(t,"position",Xh),u0=new Set(["image","url"]),c0=t=>Ro(t,u0,p0),d0=t=>Ro(t,"",h0),vi=()=>!0,Ro=(t,r,i)=>{const l=Yh.exec(t);return l?l[1]?typeof r=="string"?l[1]===r:r.has(l[1]):i(l[2]):!1},f0=t=>t0.test(t)&&!n0.test(t),Xh=()=>!1,h0=t=>r0.test(t),p0=t=>o0.test(t),g0=()=>{const t=qe("colors"),r=qe("spacing"),i=qe("blur"),l=qe("brightness"),a=qe("borderColor"),c=qe("borderRadius"),f=qe("borderSpacing"),h=qe("borderWidth"),p=qe("contrast"),m=qe("grayscale"),v=qe("hueRotate"),S=qe("invert"),y=qe("gap"),x=qe("gradientColorStops"),C=qe("gradientColorStopPositions"),P=qe("inset"),T=qe("margin"),F=qe("opacity"),B=qe("padding"),A=qe("saturate"),K=qe("scale"),Z=qe("sepia"),j=qe("skew"),ie=qe("space"),se=qe("translate"),ue=()=>["auto","contain","none"],Ce=()=>["auto","hidden","clip","visible","scroll"],Ie=()=>["auto",Ee,r],xe=()=>[Ee,r],Re=()=>["",Tn,lr],Pe=()=>["auto",So,Ee],He=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],pe=()=>["solid","dashed","dotted","double","none"],_e=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],G=()=>["start","end","center","between","around","evenly","stretch"],J=()=>["","0",Ee],W=()=>["auto","avoid","all","avoid-page","page","left","right","column"],_=()=>[So,Ee];return{cacheSize:500,separator:":",theme:{colors:[vi],spacing:[Tn,lr],blur:["none","",sr,Ee],brightness:_(),borderColor:[t],borderRadius:["none","","full",sr,Ee],borderSpacing:xe(),borderWidth:Re(),contrast:_(),grayscale:J(),hueRotate:_(),invert:J(),gap:xe(),gradientColorStops:[t],gradientColorStopPositions:[i0,lr],inset:Ie(),margin:Ie(),opacity:_(),padding:xe(),saturate:_(),scale:_(),sepia:J(),skew:_(),space:xe(),translate:xe()},classGroups:{aspect:[{aspect:["auto","square","video",Ee]}],container:["container"],columns:[{columns:[sr]}],"break-after":[{"break-after":W()}],"break-before":[{"break-before":W()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...He(),Ee]}],overflow:[{overflow:Ce()}],"overflow-x":[{"overflow-x":Ce()}],"overflow-y":[{"overflow-y":Ce()}],overscroll:[{overscroll:ue()}],"overscroll-x":[{"overscroll-x":ue()}],"overscroll-y":[{"overscroll-y":ue()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[P]}],"inset-x":[{"inset-x":[P]}],"inset-y":[{"inset-y":[P]}],start:[{start:[P]}],end:[{end:[P]}],top:[{top:[P]}],right:[{right:[P]}],bottom:[{bottom:[P]}],left:[{left:[P]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",mi,Ee]}],basis:[{basis:Ie()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Ee]}],grow:[{grow:J()}],shrink:[{shrink:J()}],order:[{order:["first","last","none",mi,Ee]}],"grid-cols":[{"grid-cols":[vi]}],"col-start-end":[{col:["auto",{span:["full",mi,Ee]},Ee]}],"col-start":[{"col-start":Pe()}],"col-end":[{"col-end":Pe()}],"grid-rows":[{"grid-rows":[vi]}],"row-start-end":[{row:["auto",{span:[mi,Ee]},Ee]}],"row-start":[{"row-start":Pe()}],"row-end":[{"row-end":Pe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Ee]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Ee]}],gap:[{gap:[y]}],"gap-x":[{"gap-x":[y]}],"gap-y":[{"gap-y":[y]}],"justify-content":[{justify:["normal",...G()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...G(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...G(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[B]}],px:[{px:[B]}],py:[{py:[B]}],ps:[{ps:[B]}],pe:[{pe:[B]}],pt:[{pt:[B]}],pr:[{pr:[B]}],pb:[{pb:[B]}],pl:[{pl:[B]}],m:[{m:[T]}],mx:[{mx:[T]}],my:[{my:[T]}],ms:[{ms:[T]}],me:[{me:[T]}],mt:[{mt:[T]}],mr:[{mr:[T]}],mb:[{mb:[T]}],ml:[{ml:[T]}],"space-x":[{"space-x":[ie]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[ie]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Ee,r]}],"min-w":[{"min-w":[Ee,r,"min","max","fit"]}],"max-w":[{"max-w":[Ee,r,"none","full","min","max","fit","prose",{screen:[sr]},sr]}],h:[{h:[Ee,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Ee,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Ee,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Ee,r,"auto","min","max","fit"]}],"font-size":[{text:["base",sr,lr]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Za]}],"font-family":[{font:[vi]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Ee]}],"line-clamp":[{"line-clamp":["none",So,Za]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Tn,Ee]}],"list-image":[{"list-image":["none",Ee]}],"list-style-type":[{list:["none","disc","decimal",Ee]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[F]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...pe(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Tn,lr]}],"underline-offset":[{"underline-offset":["auto",Tn,Ee]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:xe()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[F]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...He(),a0]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",s0]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},c0]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[C]}],"gradient-via-pos":[{via:[C]}],"gradient-to-pos":[{to:[C]}],"gradient-from":[{from:[x]}],"gradient-via":[{via:[x]}],"gradient-to":[{to:[x]}],rounded:[{rounded:[c]}],"rounded-s":[{"rounded-s":[c]}],"rounded-e":[{"rounded-e":[c]}],"rounded-t":[{"rounded-t":[c]}],"rounded-r":[{"rounded-r":[c]}],"rounded-b":[{"rounded-b":[c]}],"rounded-l":[{"rounded-l":[c]}],"rounded-ss":[{"rounded-ss":[c]}],"rounded-se":[{"rounded-se":[c]}],"rounded-ee":[{"rounded-ee":[c]}],"rounded-es":[{"rounded-es":[c]}],"rounded-tl":[{"rounded-tl":[c]}],"rounded-tr":[{"rounded-tr":[c]}],"rounded-br":[{"rounded-br":[c]}],"rounded-bl":[{"rounded-bl":[c]}],"border-w":[{border:[h]}],"border-w-x":[{"border-x":[h]}],"border-w-y":[{"border-y":[h]}],"border-w-s":[{"border-s":[h]}],"border-w-e":[{"border-e":[h]}],"border-w-t":[{"border-t":[h]}],"border-w-r":[{"border-r":[h]}],"border-w-b":[{"border-b":[h]}],"border-w-l":[{"border-l":[h]}],"border-opacity":[{"border-opacity":[F]}],"border-style":[{border:[...pe(),"hidden"]}],"divide-x":[{"divide-x":[h]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[h]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[F]}],"divide-style":[{divide:pe()}],"border-color":[{border:[a]}],"border-color-x":[{"border-x":[a]}],"border-color-y":[{"border-y":[a]}],"border-color-s":[{"border-s":[a]}],"border-color-e":[{"border-e":[a]}],"border-color-t":[{"border-t":[a]}],"border-color-r":[{"border-r":[a]}],"border-color-b":[{"border-b":[a]}],"border-color-l":[{"border-l":[a]}],"divide-color":[{divide:[a]}],"outline-style":[{outline:["",...pe()]}],"outline-offset":[{"outline-offset":[Tn,Ee]}],"outline-w":[{outline:[Tn,lr]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:Re()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[F]}],"ring-offset-w":[{"ring-offset":[Tn,lr]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",sr,d0]}],"shadow-color":[{shadow:[vi]}],opacity:[{opacity:[F]}],"mix-blend":[{"mix-blend":[..._e(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":_e()}],filter:[{filter:["","none"]}],blur:[{blur:[i]}],brightness:[{brightness:[l]}],contrast:[{contrast:[p]}],"drop-shadow":[{"drop-shadow":["","none",sr,Ee]}],grayscale:[{grayscale:[m]}],"hue-rotate":[{"hue-rotate":[v]}],invert:[{invert:[S]}],saturate:[{saturate:[A]}],sepia:[{sepia:[Z]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[i]}],"backdrop-brightness":[{"backdrop-brightness":[l]}],"backdrop-contrast":[{"backdrop-contrast":[p]}],"backdrop-grayscale":[{"backdrop-grayscale":[m]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[v]}],"backdrop-invert":[{"backdrop-invert":[S]}],"backdrop-opacity":[{"backdrop-opacity":[F]}],"backdrop-saturate":[{"backdrop-saturate":[A]}],"backdrop-sepia":[{"backdrop-sepia":[Z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[f]}],"border-spacing-x":[{"border-spacing-x":[f]}],"border-spacing-y":[{"border-spacing-y":[f]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Ee]}],duration:[{duration:_()}],ease:[{ease:["linear","in","out","in-out",Ee]}],delay:[{delay:_()}],animate:[{animate:["none","spin","ping","pulse","bounce",Ee]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[K]}],"scale-x":[{"scale-x":[K]}],"scale-y":[{"scale-y":[K]}],rotate:[{rotate:[mi,Ee]}],"translate-x":[{"translate-x":[se]}],"translate-y":[{"translate-y":[se]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Ee]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Ee]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":xe()}],"scroll-mx":[{"scroll-mx":xe()}],"scroll-my":[{"scroll-my":xe()}],"scroll-ms":[{"scroll-ms":xe()}],"scroll-me":[{"scroll-me":xe()}],"scroll-mt":[{"scroll-mt":xe()}],"scroll-mr":[{"scroll-mr":xe()}],"scroll-mb":[{"scroll-mb":xe()}],"scroll-ml":[{"scroll-ml":xe()}],"scroll-p":[{"scroll-p":xe()}],"scroll-px":[{"scroll-px":xe()}],"scroll-py":[{"scroll-py":xe()}],"scroll-ps":[{"scroll-ps":xe()}],"scroll-pe":[{"scroll-pe":xe()}],"scroll-pt":[{"scroll-pt":xe()}],"scroll-pr":[{"scroll-pr":xe()}],"scroll-pb":[{"scroll-pb":xe()}],"scroll-pl":[{"scroll-pl":xe()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Ee]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[Tn,lr,Za]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},m0=Xy(g0);function Ct(...t){return m0(Qh(t))}const yn=L.forwardRef(({className:t,...r},i)=>M.jsx("div",{ref:i,className:Ct("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r}));yn.displayName="Card";const xo=L.forwardRef(({className:t,...r},i)=>M.jsx("div",{ref:i,className:Ct("flex flex-col space-y-1.5 p-6",t),...r}));xo.displayName="CardHeader";const Co=L.forwardRef(({className:t,...r},i)=>M.jsx("h3",{ref:i,className:Ct("text-2xl font-semibold leading-none tracking-tight",t),...r}));Co.displayName="CardTitle";const v0=L.forwardRef(({className:t,...r},i)=>M.jsx("p",{ref:i,className:Ct("text-sm text-muted-foreground",t),...r}));v0.displayName="CardDescription";const wn=L.forwardRef(({className:t,...r},i)=>M.jsx("div",{ref:i,className:Ct("p-6 pt-0",t),...r}));wn.displayName="CardContent";const y0=L.forwardRef(({className:t,...r},i)=>M.jsx("div",{ref:i,className:Ct("flex items-center p-6 pt-0",t),...r}));y0.displayName="CardFooter";const w0=()=>M.jsxs("div",{className:"container mx-auto px-4 py-6",children:[M.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Azure 管理后台"}),M.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[M.jsx(yn,{children:M.jsxs(wn,{className:"flex items-center justify-between p-6",children:[M.jsxs("div",{children:[M.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"构建数量"}),M.jsx("p",{className:"text-2xl font-bold",children:"128"})]}),M.jsx("div",{className:"p-2 bg-primary/10 rounded-full",children:M.jsx(Xl,{className:"h-6 w-6 text-primary"})})]})}),M.jsx(yn,{children:M.jsxs(wn,{className:"flex items-center justify-between p-6",children:[M.jsxs("div",{children:[M.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"活跃用户"}),M.jsx("p",{className:"text-2xl font-bold",children:"28"})]}),M.jsx("div",{className:"p-2 bg-blue-500/10 rounded-full",children:M.jsx(jy,{className:"h-6 w-6 text-blue-500"})})]})}),M.jsx(yn,{children:M.jsxs(wn,{className:"flex items-center justify-between p-6",children:[M.jsxs("div",{children:[M.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"应用数量"}),M.jsx("p",{className:"text-2xl font-bold",children:"12"})]}),M.jsx("div",{className:"p-2 bg-green-500/10 rounded-full",children:M.jsx(Hh,{className:"h-6 w-6 text-green-500"})})]})}),M.jsx(yn,{children:M.jsxs(wn,{className:"flex items-center justify-between p-6",children:[M.jsxs("div",{children:[M.jsx("p",{className:"text-sm font-medium text-muted-foreground",children:"安全状态"}),M.jsx("p",{className:"text-2xl font-bold",children:"良好"})]}),M.jsx("div",{className:"p-2 bg-yellow-500/10 rounded-full",children:M.jsx(Ly,{className:"h-6 w-6 text-yellow-500"})})]})})]}),M.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[M.jsxs(yn,{className:"col-span-1",children:[M.jsx(xo,{children:M.jsx(Co,{children:"最近构建"})}),M.jsx(wn,{children:M.jsx("div",{className:"space-y-4",children:[1,2,3].map(t=>M.jsxs("div",{className:"flex items-center justify-between border-b pb-2",children:[M.jsxs("div",{children:[M.jsxs("p",{className:"font-medium",children:["构建 #",94233-t]}),M.jsxs("p",{className:"text-sm text-muted-foreground",children:["repository-",t,".git (main)"]})]}),M.jsx("div",{className:"text-right",children:M.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${t===1?"bg-green-100 text-green-800":t===2?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:t===1?"成功":t===2?"构建错误":"失败"})})]},t))})})]}),M.jsxs(yn,{className:"col-span-1",children:[M.jsx(xo,{children:M.jsx(Co,{children:"最近活动"})}),M.jsx(wn,{children:M.jsxs("div",{className:"space-y-4",children:[M.jsxs("div",{className:"flex items-start",children:[M.jsx("div",{className:"mr-4 mt-1",children:M.jsx("span",{className:"flex h-2 w-2 rounded-full bg-blue-500"})}),M.jsxs("div",{children:[M.jsx("p",{className:"font-medium",children:"系统更新"}),M.jsx("p",{className:"text-sm text-muted-foreground",children:"系统已更新至最新版本"}),M.jsx("p",{className:"text-xs text-muted-foreground",children:"10分钟前"})]})]}),M.jsxs("div",{className:"flex items-start",children:[M.jsx("div",{className:"mr-4 mt-1",children:M.jsx("span",{className:"flex h-2 w-2 rounded-full bg-green-500"})}),M.jsxs("div",{children:[M.jsx("p",{className:"font-medium",children:"新用户注册"}),M.jsx("p",{className:"text-sm text-muted-foreground",children:'用户"张三"已成功注册'}),M.jsx("p",{className:"text-xs text-muted-foreground",children:"2小时前"})]})]}),M.jsxs("div",{className:"flex items-start",children:[M.jsx("div",{className:"mr-4 mt-1",children:M.jsx("span",{className:"flex h-2 w-2 rounded-full bg-yellow-500"})}),M.jsxs("div",{children:[M.jsx("p",{className:"font-medium",children:"应用发布"}),M.jsx("p",{className:"text-sm text-muted-foreground",children:'应用"监控平台"已发布新版本'}),M.jsx("p",{className:"text-xs text-muted-foreground",children:"1天前"})]})]})]})})]})]})]});var Li=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Vr=typeof window>"u"||"Deno"in globalThis;function Tt(){}function S0(t,r){return typeof t=="function"?t(r):t}function hu(t){return typeof t=="number"&&t>=0&&t!==1/0}function Zh(t,r){return Math.max(t+(r||0)-Date.now(),0)}function fr(t,r){return typeof t=="function"?t(r):t}function cn(t,r){return typeof t=="function"?t(r):t}function Jf(t,r){const{type:i="all",exact:l,fetchStatus:a,predicate:c,queryKey:f,stale:h}=t;if(f){if(l){if(r.queryHash!==Iu(f,r.options))return!1}else if(!ki(r.queryKey,f))return!1}if(i!=="all"){const p=r.isActive();if(i==="active"&&!p||i==="inactive"&&p)return!1}return!(typeof h=="boolean"&&r.isStale()!==h||a&&a!==r.state.fetchStatus||c&&!c(r))}function eh(t,r){const{exact:i,status:l,predicate:a,mutationKey:c}=t;if(c){if(!r.options.mutationKey)return!1;if(i){if(Pi(r.options.mutationKey)!==Pi(c))return!1}else if(!ki(r.options.mutationKey,c))return!1}return!(l&&r.state.status!==l||a&&!a(r))}function Iu(t,r){return(r?.queryKeyHashFn||Pi)(t)}function Pi(t){return JSON.stringify(t,(r,i)=>gu(i)?Object.keys(i).sort().reduce((l,a)=>(l[a]=i[a],l),{}):i)}function ki(t,r){return t===r?!0:typeof t!=typeof r?!1:t&&r&&typeof t=="object"&&typeof r=="object"?Object.keys(r).every(i=>ki(t[i],r[i])):!1}function Jh(t,r){if(t===r)return t;const i=th(t)&&th(r);if(i||gu(t)&&gu(r)){const l=i?t:Object.keys(t),a=l.length,c=i?r:Object.keys(r),f=c.length,h=i?[]:{},p=new Set(l);let m=0;for(let v=0;v<f;v++){const S=i?v:c[v];(!i&&p.has(S)||i)&&t[S]===void 0&&r[S]===void 0?(h[S]=void 0,m++):(h[S]=Jh(t[S],r[S]),h[S]===t[S]&&t[S]!==void 0&&m++)}return a===f&&m===a?t:h}return r}function pu(t,r){if(!r||Object.keys(t).length!==Object.keys(r).length)return!1;for(const i in t)if(t[i]!==r[i])return!1;return!0}function th(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function gu(t){if(!nh(t))return!1;const r=t.constructor;if(r===void 0)return!0;const i=r.prototype;return!(!nh(i)||!i.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function nh(t){return Object.prototype.toString.call(t)==="[object Object]"}function x0(t){return new Promise(r=>{setTimeout(r,t)})}function mu(t,r,i){return typeof i.structuralSharing=="function"?i.structuralSharing(t,r):i.structuralSharing!==!1?Jh(t,r):r}function C0(t,r,i=0){const l=[...t,r];return i&&l.length>i?l.slice(1):l}function R0(t,r,i=0){const l=[r,...t];return i&&l.length>i?l.slice(0,-1):l}var $u=Symbol();function ep(t,r){return!t.queryFn&&r?.initialPromise?()=>r.initialPromise:!t.queryFn||t.queryFn===$u?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}function E0(t,r){return typeof t=="function"?t(...r):!!t}var P0=class extends Li{#t;#e;#n;constructor(){super(),this.#n=t=>{if(!Vr&&window.addEventListener){const r=()=>t();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(r=>{r(t)})}isFocused(){return typeof this.#t=="boolean"?this.#t:globalThis.document?.visibilityState!=="hidden"}},Au=new P0,k0=class extends Li{#t=!0;#e;#n;constructor(){super(),this.#n=t=>{if(!Vr&&window.addEventListener){const r=()=>t(!0),i=()=>t(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",i)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#t!==t&&(this.#t=t,this.listeners.forEach(i=>{i(t)}))}isOnline(){return this.#t}},rs=new k0;function vu(){let t,r;const i=new Promise((a,c)=>{t=a,r=c});i.status="pending",i.catch(()=>{});function l(a){Object.assign(i,a),delete i.resolve,delete i.reject}return i.resolve=a=>{l({status:"fulfilled",value:a}),t(a)},i.reject=a=>{l({status:"rejected",reason:a}),r(a)},i}function _0(t){return Math.min(1e3*2**t,3e4)}function tp(t){return(t??"online")==="online"?rs.isOnline():!0}var np=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function Ja(t){return t instanceof np}function rp(t){let r=!1,i=0,l=!1,a;const c=vu(),f=P=>{l||(y(new np(P)),t.abort?.())},h=()=>{r=!0},p=()=>{r=!1},m=()=>Au.isFocused()&&(t.networkMode==="always"||rs.isOnline())&&t.canRun(),v=()=>tp(t.networkMode)&&t.canRun(),S=P=>{l||(l=!0,t.onSuccess?.(P),a?.(),c.resolve(P))},y=P=>{l||(l=!0,t.onError?.(P),a?.(),c.reject(P))},x=()=>new Promise(P=>{a=T=>{(l||m())&&P(T)},t.onPause?.()}).then(()=>{a=void 0,l||t.onContinue?.()}),C=()=>{if(l)return;let P;const T=i===0?t.initialPromise:void 0;try{P=T??t.fn()}catch(F){P=Promise.reject(F)}Promise.resolve(P).then(S).catch(F=>{if(l)return;const B=t.retry??(Vr?0:3),A=t.retryDelay??_0,K=typeof A=="function"?A(i,F):A,Z=B===!0||typeof B=="number"&&i<B||typeof B=="function"&&B(i,F);if(r||!Z){y(F);return}i++,t.onFail?.(i,F),x0(K).then(()=>m()?void 0:x()).then(()=>{r?y(F):C()})})};return{promise:c,cancel:f,continue:()=>(a?.(),c),cancelRetry:h,continueRetry:p,canStart:v,start:()=>(v()?C():x().then(C),c)}}var M0=t=>setTimeout(t,0);function F0(){let t=[],r=0,i=h=>{h()},l=h=>{h()},a=M0;const c=h=>{r?t.push(h):a(()=>{i(h)})},f=()=>{const h=t;t=[],h.length&&a(()=>{l(()=>{h.forEach(p=>{i(p)})})})};return{batch:h=>{let p;r++;try{p=h()}finally{r--,r||f()}return p},batchCalls:h=>(...p)=>{c(()=>{h(...p)})},schedule:c,setNotifyFunction:h=>{i=h},setBatchNotifyFunction:h=>{l=h},setScheduler:h=>{a=h}}}var ht=F0(),op=class{#t;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),hu(this.gcTime)&&(this.#t=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(Vr?1/0:5*60*1e3))}clearGcTimeout(){this.#t&&(clearTimeout(this.#t),this.#t=void 0)}},L0=class extends op{#t;#e;#n;#r;#o;#s;#l;constructor(t){super(),this.#l=!1,this.#s=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#r=t.client,this.#n=this.#r.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#t=N0(this.options),this.state=t.state??this.#t,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#o?.promise}setOptions(t){this.options={...this.#s,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#n.remove(this)}setData(t,r){const i=mu(this.state.data,t,this.options);return this.#i({data:i,type:"success",dataUpdatedAt:r?.updatedAt,manual:r?.manual}),i}setState(t,r){this.#i({type:"setState",state:t,setStateOptions:r})}cancel(t){const r=this.#o?.promise;return this.#o?.cancel(t),r?r.then(Tt).catch(Tt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#t)}isActive(){return this.observers.some(t=>cn(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===$u||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>fr(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Zh(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(r=>r.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#o?.continue()}onOnline(){this.observers.find(r=>r.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#o?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(r=>r!==t),this.observers.length||(this.#o&&(this.#l?this.#o.cancel({revert:!0}):this.#o.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(t,r){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&r?.cancelRefetch)this.cancel({silent:!0});else if(this.#o)return this.#o.continueRetry(),this.#o.promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(m=>m.options.queryFn);p&&this.setOptions(p.options)}const i=new AbortController,l=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(this.#l=!0,i.signal)})},a=()=>{const p=ep(this.options,r),v=(()=>{const S={client:this.#r,queryKey:this.queryKey,meta:this.meta};return l(S),S})();return this.#l=!1,this.options.persister?this.options.persister(p,v,this):p(v)},f=(()=>{const p={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:this.#r,state:this.state,fetchFn:a};return l(p),p})();this.options.behavior?.onFetch(f,this),this.#e=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==f.fetchOptions?.meta)&&this.#i({type:"fetch",meta:f.fetchOptions?.meta});const h=p=>{Ja(p)&&p.silent||this.#i({type:"error",error:p}),Ja(p)||(this.#n.config.onError?.(p,this),this.#n.config.onSettled?.(this.state.data,p,this)),this.scheduleGc()};return this.#o=rp({initialPromise:r?.initialPromise,fn:f.fetchFn,abort:i.abort.bind(i),onSuccess:p=>{if(p===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(m){h(m);return}this.#n.config.onSuccess?.(p,this),this.#n.config.onSettled?.(p,this.state.error,this),this.scheduleGc()},onError:h,onFail:(p,m)=>{this.#i({type:"failed",failureCount:p,error:m})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:f.options.retry,retryDelay:f.options.retryDelay,networkMode:f.options.networkMode,canRun:()=>!0}),this.#o.start()}#i(t){const r=i=>{switch(t.type){case"failed":return{...i,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...i,fetchStatus:"paused"};case"continue":return{...i,fetchStatus:"fetching"};case"fetch":return{...i,...ip(i.data,this.options),fetchMeta:t.meta??null};case"success":return this.#e=void 0,{...i,data:t.data,dataUpdateCount:i.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const l=t.error;return Ja(l)&&l.revert&&this.#e?{...this.#e,fetchStatus:"idle"}:{...i,error:l,errorUpdateCount:i.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:i.fetchFailureCount+1,fetchFailureReason:l,fetchStatus:"idle",status:"error"};case"invalidate":return{...i,isInvalidated:!0};case"setState":return{...i,...t.state}}};this.state=r(this.state),ht.batch(()=>{this.observers.forEach(i=>{i.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:t})})}};function ip(t,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:tp(r.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function N0(t){const r=typeof t.initialData=="function"?t.initialData():t.initialData,i=r!==void 0,l=i?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:i?l??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}var D0=class extends Li{constructor(t={}){super(),this.config=t,this.#t=new Map}#t;build(t,r,i){const l=r.queryKey,a=r.queryHash??Iu(l,r);let c=this.get(a);return c||(c=new L0({client:t,queryKey:l,queryHash:a,options:t.defaultQueryOptions(r),state:i,defaultOptions:t.getQueryDefaults(l)}),this.add(c)),c}add(t){this.#t.has(t.queryHash)||(this.#t.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const r=this.#t.get(t.queryHash);r&&(t.destroy(),r===t&&this.#t.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){ht.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#t.get(t)}getAll(){return[...this.#t.values()]}find(t){const r={exact:!0,...t};return this.getAll().find(i=>Jf(r,i))}findAll(t={}){const r=this.getAll();return Object.keys(t).length>0?r.filter(i=>Jf(t,i)):r}notify(t){ht.batch(()=>{this.listeners.forEach(r=>{r(t)})})}onFocus(){ht.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){ht.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},T0=class extends op{#t;#e;#n;constructor(t){super(),this.mutationId=t.mutationId,this.#e=t.mutationCache,this.#t=[],this.state=t.state||j0(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#t.includes(t)||(this.#t.push(t),this.clearGcTimeout(),this.#e.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#t=this.#t.filter(r=>r!==t),this.scheduleGc(),this.#e.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#t.length||(this.state.status==="pending"?this.scheduleGc():this.#e.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(t){const r=()=>{this.#r({type:"continue"})};this.#n=rp({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(a,c)=>{this.#r({type:"failed",failureCount:a,error:c})},onPause:()=>{this.#r({type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#e.canRun(this)});const i=this.state.status==="pending",l=!this.#n.canStart();try{if(i)r();else{this.#r({type:"pending",variables:t,isPaused:l}),await this.#e.config.onMutate?.(t,this);const c=await this.options.onMutate?.(t);c!==this.state.context&&this.#r({type:"pending",context:c,variables:t,isPaused:l})}const a=await this.#n.start();return await this.#e.config.onSuccess?.(a,t,this.state.context,this),await this.options.onSuccess?.(a,t,this.state.context),await this.#e.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,t,this.state.context),this.#r({type:"success",data:a}),a}catch(a){try{throw await this.#e.config.onError?.(a,t,this.state.context,this),await this.options.onError?.(a,t,this.state.context),await this.#e.config.onSettled?.(void 0,a,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,a,t,this.state.context),a}finally{this.#r({type:"error",error:a})}}finally{this.#e.runNext(this)}}#r(t){const r=i=>{switch(t.type){case"failed":return{...i,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...i,isPaused:!0};case"continue":return{...i,isPaused:!1};case"pending":return{...i,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...i,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...i,data:void 0,error:t.error,failureCount:i.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=r(this.state),ht.batch(()=>{this.#t.forEach(i=>{i.onMutationUpdate(t)}),this.#e.notify({mutation:this,type:"updated",action:t})})}};function j0(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var z0=class extends Li{constructor(t={}){super(),this.config=t,this.#t=new Set,this.#e=new Map,this.#n=0}#t;#e;#n;build(t,r,i){const l=new T0({mutationCache:this,mutationId:++this.#n,options:t.defaultMutationOptions(r),state:i});return this.add(l),l}add(t){this.#t.add(t);const r=Bl(t);if(typeof r=="string"){const i=this.#e.get(r);i?i.push(t):this.#e.set(r,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#t.delete(t)){const r=Bl(t);if(typeof r=="string"){const i=this.#e.get(r);if(i)if(i.length>1){const l=i.indexOf(t);l!==-1&&i.splice(l,1)}else i[0]===t&&this.#e.delete(r)}}this.notify({type:"removed",mutation:t})}canRun(t){const r=Bl(t);if(typeof r=="string"){const l=this.#e.get(r)?.find(a=>a.state.status==="pending");return!l||l===t}else return!0}runNext(t){const r=Bl(t);return typeof r=="string"?this.#e.get(r)?.find(l=>l!==t&&l.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){ht.batch(()=>{this.#t.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#t.clear(),this.#e.clear()})}getAll(){return Array.from(this.#t)}find(t){const r={exact:!0,...t};return this.getAll().find(i=>eh(r,i))}findAll(t={}){return this.getAll().filter(r=>eh(t,r))}notify(t){ht.batch(()=>{this.listeners.forEach(r=>{r(t)})})}resumePausedMutations(){const t=this.getAll().filter(r=>r.state.isPaused);return ht.batch(()=>Promise.all(t.map(r=>r.continue().catch(Tt))))}};function Bl(t){return t.options.scope?.id}function rh(t){return{onFetch:(r,i)=>{const l=r.options,a=r.fetchOptions?.meta?.fetchMore?.direction,c=r.state.data?.pages||[],f=r.state.data?.pageParams||[];let h={pages:[],pageParams:[]},p=0;const m=async()=>{let v=!1;const S=C=>{Object.defineProperty(C,"signal",{enumerable:!0,get:()=>(r.signal.aborted?v=!0:r.signal.addEventListener("abort",()=>{v=!0}),r.signal)})},y=ep(r.options,r.fetchOptions),x=async(C,P,T)=>{if(v)return Promise.reject();if(P==null&&C.pages.length)return Promise.resolve(C);const B=(()=>{const j={client:r.client,queryKey:r.queryKey,pageParam:P,direction:T?"backward":"forward",meta:r.options.meta};return S(j),j})(),A=await y(B),{maxPages:K}=r.options,Z=T?R0:C0;return{pages:Z(C.pages,A,K),pageParams:Z(C.pageParams,P,K)}};if(a&&c.length){const C=a==="backward",P=C?O0:oh,T={pages:c,pageParams:f},F=P(l,T);h=await x(T,F,C)}else{const C=t??c.length;do{const P=p===0?f[0]??l.initialPageParam:oh(l,h);if(p>0&&P==null)break;h=await x(h,P),p++}while(p<C)}return h};r.options.persister?r.fetchFn=()=>r.options.persister?.(m,{client:r.client,queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},i):r.fetchFn=m}}}function oh(t,{pages:r,pageParams:i}){const l=r.length-1;return r.length>0?t.getNextPageParam(r[l],r,i[l],i):void 0}function O0(t,{pages:r,pageParams:i}){return r.length>0?t.getPreviousPageParam?.(r[0],r,i[0],i):void 0}var I0=class{#t;#e;#n;#r;#o;#s;#l;#i;constructor(t={}){this.#t=t.queryCache||new D0,this.#e=t.mutationCache||new z0,this.#n=t.defaultOptions||{},this.#r=new Map,this.#o=new Map,this.#s=0}mount(){this.#s++,this.#s===1&&(this.#l=Au.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onFocus())}),this.#i=rs.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#t.onOnline())}))}unmount(){this.#s--,this.#s===0&&(this.#l?.(),this.#l=void 0,this.#i?.(),this.#i=void 0)}isFetching(t){return this.#t.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#e.findAll({...t,status:"pending"}).length}getQueryData(t){const r=this.defaultQueryOptions({queryKey:t});return this.#t.get(r.queryHash)?.state.data}ensureQueryData(t){const r=this.defaultQueryOptions(t),i=this.#t.build(this,r),l=i.state.data;return l===void 0?this.fetchQuery(t):(t.revalidateIfStale&&i.isStaleByTime(fr(r.staleTime,i))&&this.prefetchQuery(r),Promise.resolve(l))}getQueriesData(t){return this.#t.findAll(t).map(({queryKey:r,state:i})=>{const l=i.data;return[r,l]})}setQueryData(t,r,i){const l=this.defaultQueryOptions({queryKey:t}),c=this.#t.get(l.queryHash)?.state.data,f=S0(r,c);if(f!==void 0)return this.#t.build(this,l).setData(f,{...i,manual:!0})}setQueriesData(t,r,i){return ht.batch(()=>this.#t.findAll(t).map(({queryKey:l})=>[l,this.setQueryData(l,r,i)]))}getQueryState(t){const r=this.defaultQueryOptions({queryKey:t});return this.#t.get(r.queryHash)?.state}removeQueries(t){const r=this.#t;ht.batch(()=>{r.findAll(t).forEach(i=>{r.remove(i)})})}resetQueries(t,r){const i=this.#t;return ht.batch(()=>(i.findAll(t).forEach(l=>{l.reset()}),this.refetchQueries({type:"active",...t},r)))}cancelQueries(t,r={}){const i={revert:!0,...r},l=ht.batch(()=>this.#t.findAll(t).map(a=>a.cancel(i)));return Promise.all(l).then(Tt).catch(Tt)}invalidateQueries(t,r={}){return ht.batch(()=>(this.#t.findAll(t).forEach(i=>{i.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},r)))}refetchQueries(t,r={}){const i={...r,cancelRefetch:r.cancelRefetch??!0},l=ht.batch(()=>this.#t.findAll(t).filter(a=>!a.isDisabled()&&!a.isStatic()).map(a=>{let c=a.fetch(void 0,i);return i.throwOnError||(c=c.catch(Tt)),a.state.fetchStatus==="paused"?Promise.resolve():c}));return Promise.all(l).then(Tt)}fetchQuery(t){const r=this.defaultQueryOptions(t);r.retry===void 0&&(r.retry=!1);const i=this.#t.build(this,r);return i.isStaleByTime(fr(r.staleTime,i))?i.fetch(r):Promise.resolve(i.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(Tt).catch(Tt)}fetchInfiniteQuery(t){return t.behavior=rh(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(Tt).catch(Tt)}ensureInfiniteQueryData(t){return t.behavior=rh(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return rs.isOnline()?this.#e.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#t}getMutationCache(){return this.#e}getDefaultOptions(){return this.#n}setDefaultOptions(t){this.#n=t}setQueryDefaults(t,r){this.#r.set(Pi(t),{queryKey:t,defaultOptions:r})}getQueryDefaults(t){const r=[...this.#r.values()],i={};return r.forEach(l=>{ki(t,l.queryKey)&&Object.assign(i,l.defaultOptions)}),i}setMutationDefaults(t,r){this.#o.set(Pi(t),{mutationKey:t,defaultOptions:r})}getMutationDefaults(t){const r=[...this.#o.values()],i={};return r.forEach(l=>{ki(t,l.mutationKey)&&Object.assign(i,l.defaultOptions)}),i}defaultQueryOptions(t){if(t._defaulted)return t;const r={...this.#n.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return r.queryHash||(r.queryHash=Iu(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.queryFn===$u&&(r.enabled=!1),r}defaultMutationOptions(t){return t?._defaulted?t:{...this.#n.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#t.clear(),this.#e.clear()}},$0=class extends Li{constructor(t,r){super(),this.options=r,this.#t=t,this.#i=null,this.#l=vu(),this.options.experimental_prefetchInRender||this.#l.reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(r)}#t;#e=void 0;#n=void 0;#r=void 0;#o;#s;#l;#i;#g;#f;#h;#u;#c;#a;#p=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(this.#e.addObserver(this),ih(this.#e,this.options)?this.#d():this.updateResult(),this.#w())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return yu(this.#e,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return yu(this.#e,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#S(),this.#x(),this.#e.removeObserver(this)}setOptions(t){const r=this.options,i=this.#e;if(this.options=this.#t.defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof cn(this.options.enabled,this.#e)!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#C(),this.#e.setOptions(this.options),r._defaulted&&!pu(this.options,r)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#e,observer:this});const l=this.hasListeners();l&&lh(this.#e,i,this.options,r)&&this.#d(),this.updateResult(),l&&(this.#e!==i||cn(this.options.enabled,this.#e)!==cn(r.enabled,this.#e)||fr(this.options.staleTime,this.#e)!==fr(r.staleTime,this.#e))&&this.#m();const a=this.#v();l&&(this.#e!==i||cn(this.options.enabled,this.#e)!==cn(r.enabled,this.#e)||a!==this.#a)&&this.#y(a)}getOptimisticResult(t){const r=this.#t.getQueryCache().build(this.#t,t),i=this.createResult(r,t);return b0(this,i)&&(this.#r=i,this.#s=this.options,this.#o=this.#e.state),i}getCurrentResult(){return this.#r}trackResult(t,r){return new Proxy(t,{get:(i,l)=>(this.trackProp(l),r?.(l),Reflect.get(i,l))})}trackProp(t){this.#p.add(t)}getCurrentQuery(){return this.#e}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const r=this.#t.defaultQueryOptions(t),i=this.#t.getQueryCache().build(this.#t,r);return i.fetch().then(()=>this.createResult(i,r))}fetch(t){return this.#d({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#r))}#d(t){this.#C();let r=this.#e.fetch(this.options,t);return t?.throwOnError||(r=r.catch(Tt)),r}#m(){this.#S();const t=fr(this.options.staleTime,this.#e);if(Vr||this.#r.isStale||!hu(t))return;const i=Zh(this.#r.dataUpdatedAt,t)+1;this.#u=setTimeout(()=>{this.#r.isStale||this.updateResult()},i)}#v(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.#e):this.options.refetchInterval)??!1}#y(t){this.#x(),this.#a=t,!(Vr||cn(this.options.enabled,this.#e)===!1||!hu(this.#a)||this.#a===0)&&(this.#c=setInterval(()=>{(this.options.refetchIntervalInBackground||Au.isFocused())&&this.#d()},this.#a))}#w(){this.#m(),this.#y(this.#v())}#S(){this.#u&&(clearTimeout(this.#u),this.#u=void 0)}#x(){this.#c&&(clearInterval(this.#c),this.#c=void 0)}createResult(t,r){const i=this.#e,l=this.options,a=this.#r,c=this.#o,f=this.#s,p=t!==i?t.state:this.#n,{state:m}=t;let v={...m},S=!1,y;if(r._optimisticResults){const se=this.hasListeners(),ue=!se&&ih(t,r),Ce=se&&lh(t,i,r,l);(ue||Ce)&&(v={...v,...ip(m.data,t.options)}),r._optimisticResults==="isRestoring"&&(v.fetchStatus="idle")}let{error:x,errorUpdatedAt:C,status:P}=v;y=v.data;let T=!1;if(r.placeholderData!==void 0&&y===void 0&&P==="pending"){let se;a?.isPlaceholderData&&r.placeholderData===f?.placeholderData?(se=a.data,T=!0):se=typeof r.placeholderData=="function"?r.placeholderData(this.#h?.state.data,this.#h):r.placeholderData,se!==void 0&&(P="success",y=mu(a?.data,se,r),S=!0)}if(r.select&&y!==void 0&&!T)if(a&&y===c?.data&&r.select===this.#g)y=this.#f;else try{this.#g=r.select,y=r.select(y),y=mu(a?.data,y,r),this.#f=y,this.#i=null}catch(se){this.#i=se}this.#i&&(x=this.#i,y=this.#f,C=Date.now(),P="error");const F=v.fetchStatus==="fetching",B=P==="pending",A=P==="error",K=B&&F,Z=y!==void 0,ie={status:P,fetchStatus:v.fetchStatus,isPending:B,isSuccess:P==="success",isError:A,isInitialLoading:K,isLoading:K,data:y,dataUpdatedAt:v.dataUpdatedAt,error:x,errorUpdatedAt:C,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>p.dataUpdateCount||v.errorUpdateCount>p.errorUpdateCount,isFetching:F,isRefetching:F&&!B,isLoadingError:A&&!Z,isPaused:v.fetchStatus==="paused",isPlaceholderData:S,isRefetchError:A&&Z,isStale:bu(t,r),refetch:this.refetch,promise:this.#l};if(this.options.experimental_prefetchInRender){const se=Ie=>{ie.status==="error"?Ie.reject(ie.error):ie.data!==void 0&&Ie.resolve(ie.data)},ue=()=>{const Ie=this.#l=ie.promise=vu();se(Ie)},Ce=this.#l;switch(Ce.status){case"pending":t.queryHash===i.queryHash&&se(Ce);break;case"fulfilled":(ie.status==="error"||ie.data!==Ce.value)&&ue();break;case"rejected":(ie.status!=="error"||ie.error!==Ce.reason)&&ue();break}}return ie}updateResult(){const t=this.#r,r=this.createResult(this.#e,this.options);if(this.#o=this.#e.state,this.#s=this.options,this.#o.data!==void 0&&(this.#h=this.#e),pu(r,t))return;this.#r=r;const i=()=>{if(!t)return!0;const{notifyOnChangeProps:l}=this.options,a=typeof l=="function"?l():l;if(a==="all"||!a&&!this.#p.size)return!0;const c=new Set(a??this.#p);return this.options.throwOnError&&c.add("error"),Object.keys(this.#r).some(f=>{const h=f;return this.#r[h]!==t[h]&&c.has(h)})};this.#R({listeners:i()})}#C(){const t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#e)return;const r=this.#e;this.#e=t,this.#n=t.state,this.hasListeners()&&(r?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#w()}#R(t){ht.batch(()=>{t.listeners&&this.listeners.forEach(r=>{r(this.#r)}),this.#t.getQueryCache().notify({query:this.#e,type:"observerResultsUpdated"})})}};function A0(t,r){return cn(r.enabled,t)!==!1&&t.state.data===void 0&&!(t.state.status==="error"&&r.retryOnMount===!1)}function ih(t,r){return A0(t,r)||t.state.data!==void 0&&yu(t,r,r.refetchOnMount)}function yu(t,r,i){if(cn(r.enabled,t)!==!1&&fr(r.staleTime,t)!=="static"){const l=typeof i=="function"?i(t):i;return l==="always"||l!==!1&&bu(t,r)}return!1}function lh(t,r,i,l){return(t!==r||cn(l.enabled,t)===!1)&&(!i.suspense||t.state.status!=="error")&&bu(t,i)}function bu(t,r){return cn(r.enabled,t)!==!1&&t.isStaleByTime(fr(r.staleTime,t))}function b0(t,r){return!pu(t.getCurrentResult(),r)}var lp=L.createContext(void 0),V0=t=>{const r=L.useContext(lp);if(!r)throw new Error("No QueryClient set, use QueryClientProvider to set one");return r},U0=({client:t,children:r})=>(L.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),M.jsx(lp.Provider,{value:t,children:r})),sp=L.createContext(!1),H0=()=>L.useContext(sp);sp.Provider;function B0(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var G0=L.createContext(B0()),Q0=()=>L.useContext(G0),W0=(t,r)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&(r.isReset()||(t.retryOnMount=!1))},q0=t=>{L.useEffect(()=>{t.clearReset()},[t])},K0=({result:t,errorResetBoundary:r,throwOnError:i,query:l,suspense:a})=>t.isError&&!r.isReset()&&!t.isFetching&&l&&(a&&t.data===void 0||E0(i,[t.error,l])),Y0=t=>{if(t.suspense){const r=l=>l==="static"?l:Math.max(l??1e3,1e3),i=t.staleTime;t.staleTime=typeof i=="function"?(...l)=>r(i(...l)):r(i),typeof t.gcTime=="number"&&(t.gcTime=Math.max(t.gcTime,1e3))}},X0=(t,r)=>t.isLoading&&t.isFetching&&!r,Z0=(t,r)=>t?.suspense&&r.isPending,sh=(t,r,i)=>r.fetchOptimistic(t).catch(()=>{i.clearReset()});function J0(t,r,i){const l=H0(),a=Q0(),c=V0(),f=c.defaultQueryOptions(t);c.getDefaultOptions().queries?._experimental_beforeQuery?.(f),f._optimisticResults=l?"isRestoring":"optimistic",Y0(f),W0(f,a),q0(a);const h=!c.getQueryCache().get(f.queryHash),[p]=L.useState(()=>new r(c,f)),m=p.getOptimisticResult(f),v=!l&&t.subscribed!==!1;if(L.useSyncExternalStore(L.useCallback(S=>{const y=v?p.subscribe(ht.batchCalls(S)):Tt;return p.updateResult(),y},[p,v]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),L.useEffect(()=>{p.setOptions(f)},[f,p]),Z0(f,m))throw sh(f,p,a);if(K0({result:m,errorResetBoundary:a,throwOnError:f.throwOnError,query:c.getQueryCache().get(f.queryHash),suspense:f.suspense}))throw m.error;return c.getDefaultOptions().queries?._experimental_afterQuery?.(f,m),f.experimental_prefetchInRender&&!Vr&&X0(m,l)&&(h?sh(f,p,a):c.getQueryCache().get(f.queryHash)?.promise)?.catch(Tt).finally(()=>{p.updateResult()}),f.notifyOnChangeProps?m:p.trackResult(m)}function Vu(t,r){return J0(t,$0)}/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Uu(){return{accessor:(t,r)=>typeof t=="function"?{...r,accessorFn:t}:{...r,accessorKey:t},display:t=>t,group:t=>t}}function dr(t,r){return typeof t=="function"?t(r):t}function Ut(t,r){return i=>{r.setState(l=>({...l,[t]:dr(i,l[t])}))}}function ls(t){return t instanceof Function}function ew(t){return Array.isArray(t)&&t.every(r=>typeof r=="number")}function tw(t,r){const i=[],l=a=>{a.forEach(c=>{i.push(c);const f=r(c);f!=null&&f.length&&l(f)})};return l(t),i}function me(t,r,i){let l=[],a;return c=>{let f;i.key&&i.debug&&(f=Date.now());const h=t(c);if(!(h.length!==l.length||h.some((v,S)=>l[S]!==v)))return a;l=h;let m;if(i.key&&i.debug&&(m=Date.now()),a=r(...h),i==null||i.onChange==null||i.onChange(a),i.key&&i.debug&&i!=null&&i.debug()){const v=Math.round((Date.now()-f)*100)/100,S=Math.round((Date.now()-m)*100)/100,y=S/16,x=(C,P)=>{for(C=String(C);C.length<P;)C=" "+C;return C};console.info(`%c⏱ ${x(S,5)} /${x(v,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*y,120))}deg 100% 31%);`,i?.key)}return a}}function ve(t,r,i,l){return{debug:()=>{var a;return(a=t?.debugAll)!=null?a:t[r]},key:!1,onChange:l}}function nw(t,r,i,l){const a=()=>{var f;return(f=c.getValue())!=null?f:t.options.renderFallbackValue},c={id:`${r.id}_${i.id}`,row:r,column:i,getValue:()=>r.getValue(l),renderValue:a,getContext:me(()=>[t,i,r,c],(f,h,p,m)=>({table:f,column:h,row:p,cell:m,getValue:m.getValue,renderValue:m.renderValue}),ve(t.options,"debugCells"))};return t._features.forEach(f=>{f.createCell==null||f.createCell(c,i,r,t)},{}),c}function rw(t,r,i,l){var a,c;const h={...t._getDefaultColumnDef(),...r},p=h.accessorKey;let m=(a=(c=h.id)!=null?c:p?typeof String.prototype.replaceAll=="function"?p.replaceAll(".","_"):p.replace(/\./g,"_"):void 0)!=null?a:typeof h.header=="string"?h.header:void 0,v;if(h.accessorFn?v=h.accessorFn:p&&(p.includes(".")?v=y=>{let x=y;for(const P of p.split(".")){var C;x=(C=x)==null?void 0:C[P]}return x}:v=y=>y[h.accessorKey]),!m)throw new Error;let S={id:`${String(m)}`,accessorFn:v,parent:l,depth:i,columnDef:h,columns:[],getFlatColumns:me(()=>[!0],()=>{var y;return[S,...(y=S.columns)==null?void 0:y.flatMap(x=>x.getFlatColumns())]},ve(t.options,"debugColumns")),getLeafColumns:me(()=>[t._getOrderColumnsFn()],y=>{var x;if((x=S.columns)!=null&&x.length){let C=S.columns.flatMap(P=>P.getLeafColumns());return y(C)}return[S]},ve(t.options,"debugColumns"))};for(const y of t._features)y.createColumn==null||y.createColumn(S,t);return S}const yt="debugHeaders";function ah(t,r,i){var l;let c={id:(l=i.id)!=null?l:r.id,column:r,index:i.index,isPlaceholder:!!i.isPlaceholder,placeholderId:i.placeholderId,depth:i.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const f=[],h=p=>{p.subHeaders&&p.subHeaders.length&&p.subHeaders.map(h),f.push(p)};return h(c),f},getContext:()=>({table:t,header:c,column:r})};return t._features.forEach(f=>{f.createHeader==null||f.createHeader(c,t)}),c}const ow={createTable:t=>{t.getHeaderGroups=me(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.left,t.getState().columnPinning.right],(r,i,l,a)=>{var c,f;const h=(c=l?.map(S=>i.find(y=>y.id===S)).filter(Boolean))!=null?c:[],p=(f=a?.map(S=>i.find(y=>y.id===S)).filter(Boolean))!=null?f:[],m=i.filter(S=>!(l!=null&&l.includes(S.id))&&!(a!=null&&a.includes(S.id)));return Gl(r,[...h,...m,...p],t)},ve(t.options,yt)),t.getCenterHeaderGroups=me(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.left,t.getState().columnPinning.right],(r,i,l,a)=>(i=i.filter(c=>!(l!=null&&l.includes(c.id))&&!(a!=null&&a.includes(c.id))),Gl(r,i,t,"center")),ve(t.options,yt)),t.getLeftHeaderGroups=me(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.left],(r,i,l)=>{var a;const c=(a=l?.map(f=>i.find(h=>h.id===f)).filter(Boolean))!=null?a:[];return Gl(r,c,t,"left")},ve(t.options,yt)),t.getRightHeaderGroups=me(()=>[t.getAllColumns(),t.getVisibleLeafColumns(),t.getState().columnPinning.right],(r,i,l)=>{var a;const c=(a=l?.map(f=>i.find(h=>h.id===f)).filter(Boolean))!=null?a:[];return Gl(r,c,t,"right")},ve(t.options,yt)),t.getFooterGroups=me(()=>[t.getHeaderGroups()],r=>[...r].reverse(),ve(t.options,yt)),t.getLeftFooterGroups=me(()=>[t.getLeftHeaderGroups()],r=>[...r].reverse(),ve(t.options,yt)),t.getCenterFooterGroups=me(()=>[t.getCenterHeaderGroups()],r=>[...r].reverse(),ve(t.options,yt)),t.getRightFooterGroups=me(()=>[t.getRightHeaderGroups()],r=>[...r].reverse(),ve(t.options,yt)),t.getFlatHeaders=me(()=>[t.getHeaderGroups()],r=>r.map(i=>i.headers).flat(),ve(t.options,yt)),t.getLeftFlatHeaders=me(()=>[t.getLeftHeaderGroups()],r=>r.map(i=>i.headers).flat(),ve(t.options,yt)),t.getCenterFlatHeaders=me(()=>[t.getCenterHeaderGroups()],r=>r.map(i=>i.headers).flat(),ve(t.options,yt)),t.getRightFlatHeaders=me(()=>[t.getRightHeaderGroups()],r=>r.map(i=>i.headers).flat(),ve(t.options,yt)),t.getCenterLeafHeaders=me(()=>[t.getCenterFlatHeaders()],r=>r.filter(i=>{var l;return!((l=i.subHeaders)!=null&&l.length)}),ve(t.options,yt)),t.getLeftLeafHeaders=me(()=>[t.getLeftFlatHeaders()],r=>r.filter(i=>{var l;return!((l=i.subHeaders)!=null&&l.length)}),ve(t.options,yt)),t.getRightLeafHeaders=me(()=>[t.getRightFlatHeaders()],r=>r.filter(i=>{var l;return!((l=i.subHeaders)!=null&&l.length)}),ve(t.options,yt)),t.getLeafHeaders=me(()=>[t.getLeftHeaderGroups(),t.getCenterHeaderGroups(),t.getRightHeaderGroups()],(r,i,l)=>{var a,c,f,h,p,m;return[...(a=(c=r[0])==null?void 0:c.headers)!=null?a:[],...(f=(h=i[0])==null?void 0:h.headers)!=null?f:[],...(p=(m=l[0])==null?void 0:m.headers)!=null?p:[]].map(v=>v.getLeafHeaders()).flat()},ve(t.options,yt))}};function Gl(t,r,i,l){var a,c;let f=0;const h=function(y,x){x===void 0&&(x=1),f=Math.max(f,x),y.filter(C=>C.getIsVisible()).forEach(C=>{var P;(P=C.columns)!=null&&P.length&&h(C.columns,x+1)},0)};h(t);let p=[];const m=(y,x)=>{const C={depth:x,id:[l,`${x}`].filter(Boolean).join("_"),headers:[]},P=[];y.forEach(T=>{const F=[...P].reverse()[0],B=T.column.depth===C.depth;let A,K=!1;if(B&&T.column.parent?A=T.column.parent:(A=T.column,K=!0),F&&F?.column===A)F.subHeaders.push(T);else{const Z=ah(i,A,{id:[l,x,A.id,T?.id].filter(Boolean).join("_"),isPlaceholder:K,placeholderId:K?`${P.filter(j=>j.column===A).length}`:void 0,depth:x,index:P.length});Z.subHeaders.push(T),P.push(Z)}C.headers.push(T),T.headerGroup=C}),p.push(C),x>0&&m(P,x-1)},v=r.map((y,x)=>ah(i,y,{depth:f,index:x}));m(v,f-1),p.reverse();const S=y=>y.filter(C=>C.column.getIsVisible()).map(C=>{let P=0,T=0,F=[0];C.subHeaders&&C.subHeaders.length?(F=[],S(C.subHeaders).forEach(A=>{let{colSpan:K,rowSpan:Z}=A;P+=K,F.push(Z)})):P=1;const B=Math.min(...F);return T=T+B,C.colSpan=P,C.rowSpan=T,{colSpan:P,rowSpan:T}});return S((a=(c=p[0])==null?void 0:c.headers)!=null?a:[]),p}const iw=(t,r,i,l,a,c,f)=>{let h={id:r,index:l,original:i,depth:a,parentId:f,_valuesCache:{},_uniqueValuesCache:{},getValue:p=>{if(h._valuesCache.hasOwnProperty(p))return h._valuesCache[p];const m=t.getColumn(p);if(m!=null&&m.accessorFn)return h._valuesCache[p]=m.accessorFn(h.original,l),h._valuesCache[p]},getUniqueValues:p=>{if(h._uniqueValuesCache.hasOwnProperty(p))return h._uniqueValuesCache[p];const m=t.getColumn(p);if(m!=null&&m.accessorFn)return m.columnDef.getUniqueValues?(h._uniqueValuesCache[p]=m.columnDef.getUniqueValues(h.original,l),h._uniqueValuesCache[p]):(h._uniqueValuesCache[p]=[h.getValue(p)],h._uniqueValuesCache[p])},renderValue:p=>{var m;return(m=h.getValue(p))!=null?m:t.options.renderFallbackValue},subRows:[],getLeafRows:()=>tw(h.subRows,p=>p.subRows),getParentRow:()=>h.parentId?t.getRow(h.parentId,!0):void 0,getParentRows:()=>{let p=[],m=h;for(;;){const v=m.getParentRow();if(!v)break;p.push(v),m=v}return p.reverse()},getAllCells:me(()=>[t.getAllLeafColumns()],p=>p.map(m=>nw(t,h,m,m.id)),ve(t.options,"debugRows")),_getAllCellsByColumnId:me(()=>[h.getAllCells()],p=>p.reduce((m,v)=>(m[v.column.id]=v,m),{}),ve(t.options,"debugRows"))};for(let p=0;p<t._features.length;p++){const m=t._features[p];m==null||m.createRow==null||m.createRow(h,t)}return h},lw={createColumn:(t,r)=>{t._getFacetedRowModel=r.options.getFacetedRowModel&&r.options.getFacetedRowModel(r,t.id),t.getFacetedRowModel=()=>t._getFacetedRowModel?t._getFacetedRowModel():r.getPreFilteredRowModel(),t._getFacetedUniqueValues=r.options.getFacetedUniqueValues&&r.options.getFacetedUniqueValues(r,t.id),t.getFacetedUniqueValues=()=>t._getFacetedUniqueValues?t._getFacetedUniqueValues():new Map,t._getFacetedMinMaxValues=r.options.getFacetedMinMaxValues&&r.options.getFacetedMinMaxValues(r,t.id),t.getFacetedMinMaxValues=()=>{if(t._getFacetedMinMaxValues)return t._getFacetedMinMaxValues()}}},ap=(t,r,i)=>{var l,a;const c=i==null||(l=i.toString())==null?void 0:l.toLowerCase();return!!(!((a=t.getValue(r))==null||(a=a.toString())==null||(a=a.toLowerCase())==null)&&a.includes(c))};ap.autoRemove=t=>fn(t);const up=(t,r,i)=>{var l;return!!(!((l=t.getValue(r))==null||(l=l.toString())==null)&&l.includes(i))};up.autoRemove=t=>fn(t);const cp=(t,r,i)=>{var l;return((l=t.getValue(r))==null||(l=l.toString())==null?void 0:l.toLowerCase())===i?.toLowerCase()};cp.autoRemove=t=>fn(t);const dp=(t,r,i)=>{var l;return(l=t.getValue(r))==null?void 0:l.includes(i)};dp.autoRemove=t=>fn(t);const fp=(t,r,i)=>!i.some(l=>{var a;return!((a=t.getValue(r))!=null&&a.includes(l))});fp.autoRemove=t=>fn(t)||!(t!=null&&t.length);const hp=(t,r,i)=>i.some(l=>{var a;return(a=t.getValue(r))==null?void 0:a.includes(l)});hp.autoRemove=t=>fn(t)||!(t!=null&&t.length);const pp=(t,r,i)=>t.getValue(r)===i;pp.autoRemove=t=>fn(t);const gp=(t,r,i)=>t.getValue(r)==i;gp.autoRemove=t=>fn(t);const Hu=(t,r,i)=>{let[l,a]=i;const c=t.getValue(r);return c>=l&&c<=a};Hu.resolveFilterValue=t=>{let[r,i]=t,l=typeof r!="number"?parseFloat(r):r,a=typeof i!="number"?parseFloat(i):i,c=r===null||Number.isNaN(l)?-1/0:l,f=i===null||Number.isNaN(a)?1/0:a;if(c>f){const h=c;c=f,f=h}return[c,f]};Hu.autoRemove=t=>fn(t)||fn(t[0])&&fn(t[1]);const jn={includesString:ap,includesStringSensitive:up,equalsString:cp,arrIncludes:dp,arrIncludesAll:fp,arrIncludesSome:hp,equals:pp,weakEquals:gp,inNumberRange:Hu};function fn(t){return t==null||t===""}const sw={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:t=>({columnFilters:[],...t}),getDefaultOptions:t=>({onColumnFiltersChange:Ut("columnFilters",t),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(t,r)=>{t.getAutoFilterFn=()=>{const i=r.getCoreRowModel().flatRows[0],l=i?.getValue(t.id);return typeof l=="string"?jn.includesString:typeof l=="number"?jn.inNumberRange:typeof l=="boolean"||l!==null&&typeof l=="object"?jn.equals:Array.isArray(l)?jn.arrIncludes:jn.weakEquals},t.getFilterFn=()=>{var i,l;return ls(t.columnDef.filterFn)?t.columnDef.filterFn:t.columnDef.filterFn==="auto"?t.getAutoFilterFn():(i=(l=r.options.filterFns)==null?void 0:l[t.columnDef.filterFn])!=null?i:jn[t.columnDef.filterFn]},t.getCanFilter=()=>{var i,l,a;return((i=t.columnDef.enableColumnFilter)!=null?i:!0)&&((l=r.options.enableColumnFilters)!=null?l:!0)&&((a=r.options.enableFilters)!=null?a:!0)&&!!t.accessorFn},t.getIsFiltered=()=>t.getFilterIndex()>-1,t.getFilterValue=()=>{var i;return(i=r.getState().columnFilters)==null||(i=i.find(l=>l.id===t.id))==null?void 0:i.value},t.getFilterIndex=()=>{var i,l;return(i=(l=r.getState().columnFilters)==null?void 0:l.findIndex(a=>a.id===t.id))!=null?i:-1},t.setFilterValue=i=>{r.setColumnFilters(l=>{const a=t.getFilterFn(),c=l?.find(v=>v.id===t.id),f=dr(i,c?c.value:void 0);if(uh(a,f,t)){var h;return(h=l?.filter(v=>v.id!==t.id))!=null?h:[]}const p={id:t.id,value:f};if(c){var m;return(m=l?.map(v=>v.id===t.id?p:v))!=null?m:[]}return l!=null&&l.length?[...l,p]:[p]})}},createRow:(t,r)=>{t.columnFilters={},t.columnFiltersMeta={}},createTable:t=>{t.setColumnFilters=r=>{const i=t.getAllLeafColumns(),l=a=>{var c;return(c=dr(r,a))==null?void 0:c.filter(f=>{const h=i.find(p=>p.id===f.id);if(h){const p=h.getFilterFn();if(uh(p,f.value,h))return!1}return!0})};t.options.onColumnFiltersChange==null||t.options.onColumnFiltersChange(l)},t.resetColumnFilters=r=>{var i,l;t.setColumnFilters(r?[]:(i=(l=t.initialState)==null?void 0:l.columnFilters)!=null?i:[])},t.getPreFilteredRowModel=()=>t.getCoreRowModel(),t.getFilteredRowModel=()=>(!t._getFilteredRowModel&&t.options.getFilteredRowModel&&(t._getFilteredRowModel=t.options.getFilteredRowModel(t)),t.options.manualFiltering||!t._getFilteredRowModel?t.getPreFilteredRowModel():t._getFilteredRowModel())}};function uh(t,r,i){return(t&&t.autoRemove?t.autoRemove(r,i):!1)||typeof r>"u"||typeof r=="string"&&!r}const aw=(t,r,i)=>i.reduce((l,a)=>{const c=a.getValue(t);return l+(typeof c=="number"?c:0)},0),uw=(t,r,i)=>{let l;return i.forEach(a=>{const c=a.getValue(t);c!=null&&(l>c||l===void 0&&c>=c)&&(l=c)}),l},cw=(t,r,i)=>{let l;return i.forEach(a=>{const c=a.getValue(t);c!=null&&(l<c||l===void 0&&c>=c)&&(l=c)}),l},dw=(t,r,i)=>{let l,a;return i.forEach(c=>{const f=c.getValue(t);f!=null&&(l===void 0?f>=f&&(l=a=f):(l>f&&(l=f),a<f&&(a=f)))}),[l,a]},fw=(t,r)=>{let i=0,l=0;if(r.forEach(a=>{let c=a.getValue(t);c!=null&&(c=+c)>=c&&(++i,l+=c)}),i)return l/i},hw=(t,r)=>{if(!r.length)return;const i=r.map(c=>c.getValue(t));if(!ew(i))return;if(i.length===1)return i[0];const l=Math.floor(i.length/2),a=i.sort((c,f)=>c-f);return i.length%2!==0?a[l]:(a[l-1]+a[l])/2},pw=(t,r)=>Array.from(new Set(r.map(i=>i.getValue(t))).values()),gw=(t,r)=>new Set(r.map(i=>i.getValue(t))).size,mw=(t,r)=>r.length,eu={sum:aw,min:uw,max:cw,extent:dw,mean:fw,median:hw,unique:pw,uniqueCount:gw,count:mw},vw={getDefaultColumnDef:()=>({aggregatedCell:t=>{var r,i;return(r=(i=t.getValue())==null||i.toString==null?void 0:i.toString())!=null?r:null},aggregationFn:"auto"}),getInitialState:t=>({grouping:[],...t}),getDefaultOptions:t=>({onGroupingChange:Ut("grouping",t),groupedColumnMode:"reorder"}),createColumn:(t,r)=>{t.toggleGrouping=()=>{r.setGrouping(i=>i!=null&&i.includes(t.id)?i.filter(l=>l!==t.id):[...i??[],t.id])},t.getCanGroup=()=>{var i,l;return((i=t.columnDef.enableGrouping)!=null?i:!0)&&((l=r.options.enableGrouping)!=null?l:!0)&&(!!t.accessorFn||!!t.columnDef.getGroupingValue)},t.getIsGrouped=()=>{var i;return(i=r.getState().grouping)==null?void 0:i.includes(t.id)},t.getGroupedIndex=()=>{var i;return(i=r.getState().grouping)==null?void 0:i.indexOf(t.id)},t.getToggleGroupingHandler=()=>{const i=t.getCanGroup();return()=>{i&&t.toggleGrouping()}},t.getAutoAggregationFn=()=>{const i=r.getCoreRowModel().flatRows[0],l=i?.getValue(t.id);if(typeof l=="number")return eu.sum;if(Object.prototype.toString.call(l)==="[object Date]")return eu.extent},t.getAggregationFn=()=>{var i,l;if(!t)throw new Error;return ls(t.columnDef.aggregationFn)?t.columnDef.aggregationFn:t.columnDef.aggregationFn==="auto"?t.getAutoAggregationFn():(i=(l=r.options.aggregationFns)==null?void 0:l[t.columnDef.aggregationFn])!=null?i:eu[t.columnDef.aggregationFn]}},createTable:t=>{t.setGrouping=r=>t.options.onGroupingChange==null?void 0:t.options.onGroupingChange(r),t.resetGrouping=r=>{var i,l;t.setGrouping(r?[]:(i=(l=t.initialState)==null?void 0:l.grouping)!=null?i:[])},t.getPreGroupedRowModel=()=>t.getFilteredRowModel(),t.getGroupedRowModel=()=>(!t._getGroupedRowModel&&t.options.getGroupedRowModel&&(t._getGroupedRowModel=t.options.getGroupedRowModel(t)),t.options.manualGrouping||!t._getGroupedRowModel?t.getPreGroupedRowModel():t._getGroupedRowModel())},createRow:(t,r)=>{t.getIsGrouped=()=>!!t.groupingColumnId,t.getGroupingValue=i=>{if(t._groupingValuesCache.hasOwnProperty(i))return t._groupingValuesCache[i];const l=r.getColumn(i);return l!=null&&l.columnDef.getGroupingValue?(t._groupingValuesCache[i]=l.columnDef.getGroupingValue(t.original),t._groupingValuesCache[i]):t.getValue(i)},t._groupingValuesCache={}},createCell:(t,r,i,l)=>{t.getIsGrouped=()=>r.getIsGrouped()&&r.id===i.groupingColumnId,t.getIsPlaceholder=()=>!t.getIsGrouped()&&r.getIsGrouped(),t.getIsAggregated=()=>{var a;return!t.getIsGrouped()&&!t.getIsPlaceholder()&&!!((a=i.subRows)!=null&&a.length)}}};function yw(t,r,i){if(!(r!=null&&r.length)||!i)return t;const l=t.filter(c=>!r.includes(c.id));return i==="remove"?l:[...r.map(c=>t.find(f=>f.id===c)).filter(Boolean),...l]}const ww={getInitialState:t=>({columnOrder:[],...t}),getDefaultOptions:t=>({onColumnOrderChange:Ut("columnOrder",t)}),createColumn:(t,r)=>{t.getIndex=me(i=>[Ci(r,i)],i=>i.findIndex(l=>l.id===t.id),ve(r.options,"debugColumns")),t.getIsFirstColumn=i=>{var l;return((l=Ci(r,i)[0])==null?void 0:l.id)===t.id},t.getIsLastColumn=i=>{var l;const a=Ci(r,i);return((l=a[a.length-1])==null?void 0:l.id)===t.id}},createTable:t=>{t.setColumnOrder=r=>t.options.onColumnOrderChange==null?void 0:t.options.onColumnOrderChange(r),t.resetColumnOrder=r=>{var i;t.setColumnOrder(r?[]:(i=t.initialState.columnOrder)!=null?i:[])},t._getOrderColumnsFn=me(()=>[t.getState().columnOrder,t.getState().grouping,t.options.groupedColumnMode],(r,i,l)=>a=>{let c=[];if(!(r!=null&&r.length))c=a;else{const f=[...r],h=[...a];for(;h.length&&f.length;){const p=f.shift(),m=h.findIndex(v=>v.id===p);m>-1&&c.push(h.splice(m,1)[0])}c=[...c,...h]}return yw(c,i,l)},ve(t.options,"debugTable"))}},tu=()=>({left:[],right:[]}),Sw={getInitialState:t=>({columnPinning:tu(),...t}),getDefaultOptions:t=>({onColumnPinningChange:Ut("columnPinning",t)}),createColumn:(t,r)=>{t.pin=i=>{const l=t.getLeafColumns().map(a=>a.id).filter(Boolean);r.setColumnPinning(a=>{var c,f;if(i==="right"){var h,p;return{left:((h=a?.left)!=null?h:[]).filter(S=>!(l!=null&&l.includes(S))),right:[...((p=a?.right)!=null?p:[]).filter(S=>!(l!=null&&l.includes(S))),...l]}}if(i==="left"){var m,v;return{left:[...((m=a?.left)!=null?m:[]).filter(S=>!(l!=null&&l.includes(S))),...l],right:((v=a?.right)!=null?v:[]).filter(S=>!(l!=null&&l.includes(S)))}}return{left:((c=a?.left)!=null?c:[]).filter(S=>!(l!=null&&l.includes(S))),right:((f=a?.right)!=null?f:[]).filter(S=>!(l!=null&&l.includes(S)))}})},t.getCanPin=()=>t.getLeafColumns().some(l=>{var a,c,f;return((a=l.columnDef.enablePinning)!=null?a:!0)&&((c=(f=r.options.enableColumnPinning)!=null?f:r.options.enablePinning)!=null?c:!0)}),t.getIsPinned=()=>{const i=t.getLeafColumns().map(h=>h.id),{left:l,right:a}=r.getState().columnPinning,c=i.some(h=>l?.includes(h)),f=i.some(h=>a?.includes(h));return c?"left":f?"right":!1},t.getPinnedIndex=()=>{var i,l;const a=t.getIsPinned();return a?(i=(l=r.getState().columnPinning)==null||(l=l[a])==null?void 0:l.indexOf(t.id))!=null?i:-1:0}},createRow:(t,r)=>{t.getCenterVisibleCells=me(()=>[t._getAllVisibleCells(),r.getState().columnPinning.left,r.getState().columnPinning.right],(i,l,a)=>{const c=[...l??[],...a??[]];return i.filter(f=>!c.includes(f.column.id))},ve(r.options,"debugRows")),t.getLeftVisibleCells=me(()=>[t._getAllVisibleCells(),r.getState().columnPinning.left],(i,l)=>(l??[]).map(c=>i.find(f=>f.column.id===c)).filter(Boolean).map(c=>({...c,position:"left"})),ve(r.options,"debugRows")),t.getRightVisibleCells=me(()=>[t._getAllVisibleCells(),r.getState().columnPinning.right],(i,l)=>(l??[]).map(c=>i.find(f=>f.column.id===c)).filter(Boolean).map(c=>({...c,position:"right"})),ve(r.options,"debugRows"))},createTable:t=>{t.setColumnPinning=r=>t.options.onColumnPinningChange==null?void 0:t.options.onColumnPinningChange(r),t.resetColumnPinning=r=>{var i,l;return t.setColumnPinning(r?tu():(i=(l=t.initialState)==null?void 0:l.columnPinning)!=null?i:tu())},t.getIsSomeColumnsPinned=r=>{var i;const l=t.getState().columnPinning;if(!r){var a,c;return!!((a=l.left)!=null&&a.length||(c=l.right)!=null&&c.length)}return!!((i=l[r])!=null&&i.length)},t.getLeftLeafColumns=me(()=>[t.getAllLeafColumns(),t.getState().columnPinning.left],(r,i)=>(i??[]).map(l=>r.find(a=>a.id===l)).filter(Boolean),ve(t.options,"debugColumns")),t.getRightLeafColumns=me(()=>[t.getAllLeafColumns(),t.getState().columnPinning.right],(r,i)=>(i??[]).map(l=>r.find(a=>a.id===l)).filter(Boolean),ve(t.options,"debugColumns")),t.getCenterLeafColumns=me(()=>[t.getAllLeafColumns(),t.getState().columnPinning.left,t.getState().columnPinning.right],(r,i,l)=>{const a=[...i??[],...l??[]];return r.filter(c=>!a.includes(c.id))},ve(t.options,"debugColumns"))}};function xw(t){return t||(typeof document<"u"?document:null)}const Ql={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},nu=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Cw={getDefaultColumnDef:()=>Ql,getInitialState:t=>({columnSizing:{},columnSizingInfo:nu(),...t}),getDefaultOptions:t=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:Ut("columnSizing",t),onColumnSizingInfoChange:Ut("columnSizingInfo",t)}),createColumn:(t,r)=>{t.getSize=()=>{var i,l,a;const c=r.getState().columnSizing[t.id];return Math.min(Math.max((i=t.columnDef.minSize)!=null?i:Ql.minSize,(l=c??t.columnDef.size)!=null?l:Ql.size),(a=t.columnDef.maxSize)!=null?a:Ql.maxSize)},t.getStart=me(i=>[i,Ci(r,i),r.getState().columnSizing],(i,l)=>l.slice(0,t.getIndex(i)).reduce((a,c)=>a+c.getSize(),0),ve(r.options,"debugColumns")),t.getAfter=me(i=>[i,Ci(r,i),r.getState().columnSizing],(i,l)=>l.slice(t.getIndex(i)+1).reduce((a,c)=>a+c.getSize(),0),ve(r.options,"debugColumns")),t.resetSize=()=>{r.setColumnSizing(i=>{let{[t.id]:l,...a}=i;return a})},t.getCanResize=()=>{var i,l;return((i=t.columnDef.enableResizing)!=null?i:!0)&&((l=r.options.enableColumnResizing)!=null?l:!0)},t.getIsResizing=()=>r.getState().columnSizingInfo.isResizingColumn===t.id},createHeader:(t,r)=>{t.getSize=()=>{let i=0;const l=a=>{if(a.subHeaders.length)a.subHeaders.forEach(l);else{var c;i+=(c=a.column.getSize())!=null?c:0}};return l(t),i},t.getStart=()=>{if(t.index>0){const i=t.headerGroup.headers[t.index-1];return i.getStart()+i.getSize()}return 0},t.getResizeHandler=i=>{const l=r.getColumn(t.column.id),a=l?.getCanResize();return c=>{if(!l||!a||(c.persist==null||c.persist(),ru(c)&&c.touches&&c.touches.length>1))return;const f=t.getSize(),h=t?t.getLeafHeaders().map(F=>[F.column.id,F.column.getSize()]):[[l.id,l.getSize()]],p=ru(c)?Math.round(c.touches[0].clientX):c.clientX,m={},v=(F,B)=>{typeof B=="number"&&(r.setColumnSizingInfo(A=>{var K,Z;const j=r.options.columnResizeDirection==="rtl"?-1:1,ie=(B-((K=A?.startOffset)!=null?K:0))*j,se=Math.max(ie/((Z=A?.startSize)!=null?Z:0),-.999999);return A.columnSizingStart.forEach(ue=>{let[Ce,Ie]=ue;m[Ce]=Math.round(Math.max(Ie+Ie*se,0)*100)/100}),{...A,deltaOffset:ie,deltaPercentage:se}}),(r.options.columnResizeMode==="onChange"||F==="end")&&r.setColumnSizing(A=>({...A,...m})))},S=F=>v("move",F),y=F=>{v("end",F),r.setColumnSizingInfo(B=>({...B,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},x=xw(i),C={moveHandler:F=>S(F.clientX),upHandler:F=>{x?.removeEventListener("mousemove",C.moveHandler),x?.removeEventListener("mouseup",C.upHandler),y(F.clientX)}},P={moveHandler:F=>(F.cancelable&&(F.preventDefault(),F.stopPropagation()),S(F.touches[0].clientX),!1),upHandler:F=>{var B;x?.removeEventListener("touchmove",P.moveHandler),x?.removeEventListener("touchend",P.upHandler),F.cancelable&&(F.preventDefault(),F.stopPropagation()),y((B=F.touches[0])==null?void 0:B.clientX)}},T=Rw()?{passive:!1}:!1;ru(c)?(x?.addEventListener("touchmove",P.moveHandler,T),x?.addEventListener("touchend",P.upHandler,T)):(x?.addEventListener("mousemove",C.moveHandler,T),x?.addEventListener("mouseup",C.upHandler,T)),r.setColumnSizingInfo(F=>({...F,startOffset:p,startSize:f,deltaOffset:0,deltaPercentage:0,columnSizingStart:h,isResizingColumn:l.id}))}}},createTable:t=>{t.setColumnSizing=r=>t.options.onColumnSizingChange==null?void 0:t.options.onColumnSizingChange(r),t.setColumnSizingInfo=r=>t.options.onColumnSizingInfoChange==null?void 0:t.options.onColumnSizingInfoChange(r),t.resetColumnSizing=r=>{var i;t.setColumnSizing(r?{}:(i=t.initialState.columnSizing)!=null?i:{})},t.resetHeaderSizeInfo=r=>{var i;t.setColumnSizingInfo(r?nu():(i=t.initialState.columnSizingInfo)!=null?i:nu())},t.getTotalSize=()=>{var r,i;return(r=(i=t.getHeaderGroups()[0])==null?void 0:i.headers.reduce((l,a)=>l+a.getSize(),0))!=null?r:0},t.getLeftTotalSize=()=>{var r,i;return(r=(i=t.getLeftHeaderGroups()[0])==null?void 0:i.headers.reduce((l,a)=>l+a.getSize(),0))!=null?r:0},t.getCenterTotalSize=()=>{var r,i;return(r=(i=t.getCenterHeaderGroups()[0])==null?void 0:i.headers.reduce((l,a)=>l+a.getSize(),0))!=null?r:0},t.getRightTotalSize=()=>{var r,i;return(r=(i=t.getRightHeaderGroups()[0])==null?void 0:i.headers.reduce((l,a)=>l+a.getSize(),0))!=null?r:0}}};let Wl=null;function Rw(){if(typeof Wl=="boolean")return Wl;let t=!1;try{const r={get passive(){return t=!0,!1}},i=()=>{};window.addEventListener("test",i,r),window.removeEventListener("test",i)}catch{t=!1}return Wl=t,Wl}function ru(t){return t.type==="touchstart"}const Ew={getInitialState:t=>({columnVisibility:{},...t}),getDefaultOptions:t=>({onColumnVisibilityChange:Ut("columnVisibility",t)}),createColumn:(t,r)=>{t.toggleVisibility=i=>{t.getCanHide()&&r.setColumnVisibility(l=>({...l,[t.id]:i??!t.getIsVisible()}))},t.getIsVisible=()=>{var i,l;const a=t.columns;return(i=a.length?a.some(c=>c.getIsVisible()):(l=r.getState().columnVisibility)==null?void 0:l[t.id])!=null?i:!0},t.getCanHide=()=>{var i,l;return((i=t.columnDef.enableHiding)!=null?i:!0)&&((l=r.options.enableHiding)!=null?l:!0)},t.getToggleVisibilityHandler=()=>i=>{t.toggleVisibility==null||t.toggleVisibility(i.target.checked)}},createRow:(t,r)=>{t._getAllVisibleCells=me(()=>[t.getAllCells(),r.getState().columnVisibility],i=>i.filter(l=>l.column.getIsVisible()),ve(r.options,"debugRows")),t.getVisibleCells=me(()=>[t.getLeftVisibleCells(),t.getCenterVisibleCells(),t.getRightVisibleCells()],(i,l,a)=>[...i,...l,...a],ve(r.options,"debugRows"))},createTable:t=>{const r=(i,l)=>me(()=>[l(),l().filter(a=>a.getIsVisible()).map(a=>a.id).join("_")],a=>a.filter(c=>c.getIsVisible==null?void 0:c.getIsVisible()),ve(t.options,"debugColumns"));t.getVisibleFlatColumns=r("getVisibleFlatColumns",()=>t.getAllFlatColumns()),t.getVisibleLeafColumns=r("getVisibleLeafColumns",()=>t.getAllLeafColumns()),t.getLeftVisibleLeafColumns=r("getLeftVisibleLeafColumns",()=>t.getLeftLeafColumns()),t.getRightVisibleLeafColumns=r("getRightVisibleLeafColumns",()=>t.getRightLeafColumns()),t.getCenterVisibleLeafColumns=r("getCenterVisibleLeafColumns",()=>t.getCenterLeafColumns()),t.setColumnVisibility=i=>t.options.onColumnVisibilityChange==null?void 0:t.options.onColumnVisibilityChange(i),t.resetColumnVisibility=i=>{var l;t.setColumnVisibility(i?{}:(l=t.initialState.columnVisibility)!=null?l:{})},t.toggleAllColumnsVisible=i=>{var l;i=(l=i)!=null?l:!t.getIsAllColumnsVisible(),t.setColumnVisibility(t.getAllLeafColumns().reduce((a,c)=>({...a,[c.id]:i||!(c.getCanHide!=null&&c.getCanHide())}),{}))},t.getIsAllColumnsVisible=()=>!t.getAllLeafColumns().some(i=>!(i.getIsVisible!=null&&i.getIsVisible())),t.getIsSomeColumnsVisible=()=>t.getAllLeafColumns().some(i=>i.getIsVisible==null?void 0:i.getIsVisible()),t.getToggleAllColumnsVisibilityHandler=()=>i=>{var l;t.toggleAllColumnsVisible((l=i.target)==null?void 0:l.checked)}}};function Ci(t,r){return r?r==="center"?t.getCenterVisibleLeafColumns():r==="left"?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns()}const Pw={createTable:t=>{t._getGlobalFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,"__global__"),t.getGlobalFacetedRowModel=()=>t.options.manualFiltering||!t._getGlobalFacetedRowModel?t.getPreFilteredRowModel():t._getGlobalFacetedRowModel(),t._getGlobalFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,"__global__"),t.getGlobalFacetedUniqueValues=()=>t._getGlobalFacetedUniqueValues?t._getGlobalFacetedUniqueValues():new Map,t._getGlobalFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,"__global__"),t.getGlobalFacetedMinMaxValues=()=>{if(t._getGlobalFacetedMinMaxValues)return t._getGlobalFacetedMinMaxValues()}}},kw={getInitialState:t=>({globalFilter:void 0,...t}),getDefaultOptions:t=>({onGlobalFilterChange:Ut("globalFilter",t),globalFilterFn:"auto",getColumnCanGlobalFilter:r=>{var i;const l=(i=t.getCoreRowModel().flatRows[0])==null||(i=i._getAllCellsByColumnId()[r.id])==null?void 0:i.getValue();return typeof l=="string"||typeof l=="number"}}),createColumn:(t,r)=>{t.getCanGlobalFilter=()=>{var i,l,a,c;return((i=t.columnDef.enableGlobalFilter)!=null?i:!0)&&((l=r.options.enableGlobalFilter)!=null?l:!0)&&((a=r.options.enableFilters)!=null?a:!0)&&((c=r.options.getColumnCanGlobalFilter==null?void 0:r.options.getColumnCanGlobalFilter(t))!=null?c:!0)&&!!t.accessorFn}},createTable:t=>{t.getGlobalAutoFilterFn=()=>jn.includesString,t.getGlobalFilterFn=()=>{var r,i;const{globalFilterFn:l}=t.options;return ls(l)?l:l==="auto"?t.getGlobalAutoFilterFn():(r=(i=t.options.filterFns)==null?void 0:i[l])!=null?r:jn[l]},t.setGlobalFilter=r=>{t.options.onGlobalFilterChange==null||t.options.onGlobalFilterChange(r)},t.resetGlobalFilter=r=>{t.setGlobalFilter(r?void 0:t.initialState.globalFilter)}}},_w={getInitialState:t=>({expanded:{},...t}),getDefaultOptions:t=>({onExpandedChange:Ut("expanded",t),paginateExpandedRows:!0}),createTable:t=>{let r=!1,i=!1;t._autoResetExpanded=()=>{var l,a;if(!r){t._queue(()=>{r=!0});return}if((l=(a=t.options.autoResetAll)!=null?a:t.options.autoResetExpanded)!=null?l:!t.options.manualExpanding){if(i)return;i=!0,t._queue(()=>{t.resetExpanded(),i=!1})}},t.setExpanded=l=>t.options.onExpandedChange==null?void 0:t.options.onExpandedChange(l),t.toggleAllRowsExpanded=l=>{l??!t.getIsAllRowsExpanded()?t.setExpanded(!0):t.setExpanded({})},t.resetExpanded=l=>{var a,c;t.setExpanded(l?{}:(a=(c=t.initialState)==null?void 0:c.expanded)!=null?a:{})},t.getCanSomeRowsExpand=()=>t.getPrePaginationRowModel().flatRows.some(l=>l.getCanExpand()),t.getToggleAllRowsExpandedHandler=()=>l=>{l.persist==null||l.persist(),t.toggleAllRowsExpanded()},t.getIsSomeRowsExpanded=()=>{const l=t.getState().expanded;return l===!0||Object.values(l).some(Boolean)},t.getIsAllRowsExpanded=()=>{const l=t.getState().expanded;return typeof l=="boolean"?l===!0:!(!Object.keys(l).length||t.getRowModel().flatRows.some(a=>!a.getIsExpanded()))},t.getExpandedDepth=()=>{let l=0;return(t.getState().expanded===!0?Object.keys(t.getRowModel().rowsById):Object.keys(t.getState().expanded)).forEach(c=>{const f=c.split(".");l=Math.max(l,f.length)}),l},t.getPreExpandedRowModel=()=>t.getSortedRowModel(),t.getExpandedRowModel=()=>(!t._getExpandedRowModel&&t.options.getExpandedRowModel&&(t._getExpandedRowModel=t.options.getExpandedRowModel(t)),t.options.manualExpanding||!t._getExpandedRowModel?t.getPreExpandedRowModel():t._getExpandedRowModel())},createRow:(t,r)=>{t.toggleExpanded=i=>{r.setExpanded(l=>{var a;const c=l===!0?!0:!!(l!=null&&l[t.id]);let f={};if(l===!0?Object.keys(r.getRowModel().rowsById).forEach(h=>{f[h]=!0}):f=l,i=(a=i)!=null?a:!c,!c&&i)return{...f,[t.id]:!0};if(c&&!i){const{[t.id]:h,...p}=f;return p}return l})},t.getIsExpanded=()=>{var i;const l=r.getState().expanded;return!!((i=r.options.getIsRowExpanded==null?void 0:r.options.getIsRowExpanded(t))!=null?i:l===!0||l?.[t.id])},t.getCanExpand=()=>{var i,l,a;return(i=r.options.getRowCanExpand==null?void 0:r.options.getRowCanExpand(t))!=null?i:((l=r.options.enableExpanding)!=null?l:!0)&&!!((a=t.subRows)!=null&&a.length)},t.getIsAllParentsExpanded=()=>{let i=!0,l=t;for(;i&&l.parentId;)l=r.getRow(l.parentId,!0),i=l.getIsExpanded();return i},t.getToggleExpandedHandler=()=>{const i=t.getCanExpand();return()=>{i&&t.toggleExpanded()}}}},wu=0,Su=10,ou=()=>({pageIndex:wu,pageSize:Su}),Mw={getInitialState:t=>({...t,pagination:{...ou(),...t?.pagination}}),getDefaultOptions:t=>({onPaginationChange:Ut("pagination",t)}),createTable:t=>{let r=!1,i=!1;t._autoResetPageIndex=()=>{var l,a;if(!r){t._queue(()=>{r=!0});return}if((l=(a=t.options.autoResetAll)!=null?a:t.options.autoResetPageIndex)!=null?l:!t.options.manualPagination){if(i)return;i=!0,t._queue(()=>{t.resetPageIndex(),i=!1})}},t.setPagination=l=>{const a=c=>dr(l,c);return t.options.onPaginationChange==null?void 0:t.options.onPaginationChange(a)},t.resetPagination=l=>{var a;t.setPagination(l?ou():(a=t.initialState.pagination)!=null?a:ou())},t.setPageIndex=l=>{t.setPagination(a=>{let c=dr(l,a.pageIndex);const f=typeof t.options.pageCount>"u"||t.options.pageCount===-1?Number.MAX_SAFE_INTEGER:t.options.pageCount-1;return c=Math.max(0,Math.min(c,f)),{...a,pageIndex:c}})},t.resetPageIndex=l=>{var a,c;t.setPageIndex(l?wu:(a=(c=t.initialState)==null||(c=c.pagination)==null?void 0:c.pageIndex)!=null?a:wu)},t.resetPageSize=l=>{var a,c;t.setPageSize(l?Su:(a=(c=t.initialState)==null||(c=c.pagination)==null?void 0:c.pageSize)!=null?a:Su)},t.setPageSize=l=>{t.setPagination(a=>{const c=Math.max(1,dr(l,a.pageSize)),f=a.pageSize*a.pageIndex,h=Math.floor(f/c);return{...a,pageIndex:h,pageSize:c}})},t.setPageCount=l=>t.setPagination(a=>{var c;let f=dr(l,(c=t.options.pageCount)!=null?c:-1);return typeof f=="number"&&(f=Math.max(-1,f)),{...a,pageCount:f}}),t.getPageOptions=me(()=>[t.getPageCount()],l=>{let a=[];return l&&l>0&&(a=[...new Array(l)].fill(null).map((c,f)=>f)),a},ve(t.options,"debugTable")),t.getCanPreviousPage=()=>t.getState().pagination.pageIndex>0,t.getCanNextPage=()=>{const{pageIndex:l}=t.getState().pagination,a=t.getPageCount();return a===-1?!0:a===0?!1:l<a-1},t.previousPage=()=>t.setPageIndex(l=>l-1),t.nextPage=()=>t.setPageIndex(l=>l+1),t.firstPage=()=>t.setPageIndex(0),t.lastPage=()=>t.setPageIndex(t.getPageCount()-1),t.getPrePaginationRowModel=()=>t.getExpandedRowModel(),t.getPaginationRowModel=()=>(!t._getPaginationRowModel&&t.options.getPaginationRowModel&&(t._getPaginationRowModel=t.options.getPaginationRowModel(t)),t.options.manualPagination||!t._getPaginationRowModel?t.getPrePaginationRowModel():t._getPaginationRowModel()),t.getPageCount=()=>{var l;return(l=t.options.pageCount)!=null?l:Math.ceil(t.getRowCount()/t.getState().pagination.pageSize)},t.getRowCount=()=>{var l;return(l=t.options.rowCount)!=null?l:t.getPrePaginationRowModel().rows.length}}},iu=()=>({top:[],bottom:[]}),Fw={getInitialState:t=>({rowPinning:iu(),...t}),getDefaultOptions:t=>({onRowPinningChange:Ut("rowPinning",t)}),createRow:(t,r)=>{t.pin=(i,l,a)=>{const c=l?t.getLeafRows().map(p=>{let{id:m}=p;return m}):[],f=a?t.getParentRows().map(p=>{let{id:m}=p;return m}):[],h=new Set([...f,t.id,...c]);r.setRowPinning(p=>{var m,v;if(i==="bottom"){var S,y;return{top:((S=p?.top)!=null?S:[]).filter(P=>!(h!=null&&h.has(P))),bottom:[...((y=p?.bottom)!=null?y:[]).filter(P=>!(h!=null&&h.has(P))),...Array.from(h)]}}if(i==="top"){var x,C;return{top:[...((x=p?.top)!=null?x:[]).filter(P=>!(h!=null&&h.has(P))),...Array.from(h)],bottom:((C=p?.bottom)!=null?C:[]).filter(P=>!(h!=null&&h.has(P)))}}return{top:((m=p?.top)!=null?m:[]).filter(P=>!(h!=null&&h.has(P))),bottom:((v=p?.bottom)!=null?v:[]).filter(P=>!(h!=null&&h.has(P)))}})},t.getCanPin=()=>{var i;const{enableRowPinning:l,enablePinning:a}=r.options;return typeof l=="function"?l(t):(i=l??a)!=null?i:!0},t.getIsPinned=()=>{const i=[t.id],{top:l,bottom:a}=r.getState().rowPinning,c=i.some(h=>l?.includes(h)),f=i.some(h=>a?.includes(h));return c?"top":f?"bottom":!1},t.getPinnedIndex=()=>{var i,l;const a=t.getIsPinned();if(!a)return-1;const c=(i=a==="top"?r.getTopRows():r.getBottomRows())==null?void 0:i.map(f=>{let{id:h}=f;return h});return(l=c?.indexOf(t.id))!=null?l:-1}},createTable:t=>{t.setRowPinning=r=>t.options.onRowPinningChange==null?void 0:t.options.onRowPinningChange(r),t.resetRowPinning=r=>{var i,l;return t.setRowPinning(r?iu():(i=(l=t.initialState)==null?void 0:l.rowPinning)!=null?i:iu())},t.getIsSomeRowsPinned=r=>{var i;const l=t.getState().rowPinning;if(!r){var a,c;return!!((a=l.top)!=null&&a.length||(c=l.bottom)!=null&&c.length)}return!!((i=l[r])!=null&&i.length)},t._getPinnedRows=(r,i,l)=>{var a;return((a=t.options.keepPinnedRows)==null||a?(i??[]).map(f=>{const h=t.getRow(f,!0);return h.getIsAllParentsExpanded()?h:null}):(i??[]).map(f=>r.find(h=>h.id===f))).filter(Boolean).map(f=>({...f,position:l}))},t.getTopRows=me(()=>[t.getRowModel().rows,t.getState().rowPinning.top],(r,i)=>t._getPinnedRows(r,i,"top"),ve(t.options,"debugRows")),t.getBottomRows=me(()=>[t.getRowModel().rows,t.getState().rowPinning.bottom],(r,i)=>t._getPinnedRows(r,i,"bottom"),ve(t.options,"debugRows")),t.getCenterRows=me(()=>[t.getRowModel().rows,t.getState().rowPinning.top,t.getState().rowPinning.bottom],(r,i,l)=>{const a=new Set([...i??[],...l??[]]);return r.filter(c=>!a.has(c.id))},ve(t.options,"debugRows"))}},Lw={getInitialState:t=>({rowSelection:{},...t}),getDefaultOptions:t=>({onRowSelectionChange:Ut("rowSelection",t),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:t=>{t.setRowSelection=r=>t.options.onRowSelectionChange==null?void 0:t.options.onRowSelectionChange(r),t.resetRowSelection=r=>{var i;return t.setRowSelection(r?{}:(i=t.initialState.rowSelection)!=null?i:{})},t.toggleAllRowsSelected=r=>{t.setRowSelection(i=>{r=typeof r<"u"?r:!t.getIsAllRowsSelected();const l={...i},a=t.getPreGroupedRowModel().flatRows;return r?a.forEach(c=>{c.getCanSelect()&&(l[c.id]=!0)}):a.forEach(c=>{delete l[c.id]}),l})},t.toggleAllPageRowsSelected=r=>t.setRowSelection(i=>{const l=typeof r<"u"?r:!t.getIsAllPageRowsSelected(),a={...i};return t.getRowModel().rows.forEach(c=>{xu(a,c.id,l,!0,t)}),a}),t.getPreSelectedRowModel=()=>t.getCoreRowModel(),t.getSelectedRowModel=me(()=>[t.getState().rowSelection,t.getCoreRowModel()],(r,i)=>Object.keys(r).length?lu(t,i):{rows:[],flatRows:[],rowsById:{}},ve(t.options,"debugTable")),t.getFilteredSelectedRowModel=me(()=>[t.getState().rowSelection,t.getFilteredRowModel()],(r,i)=>Object.keys(r).length?lu(t,i):{rows:[],flatRows:[],rowsById:{}},ve(t.options,"debugTable")),t.getGroupedSelectedRowModel=me(()=>[t.getState().rowSelection,t.getSortedRowModel()],(r,i)=>Object.keys(r).length?lu(t,i):{rows:[],flatRows:[],rowsById:{}},ve(t.options,"debugTable")),t.getIsAllRowsSelected=()=>{const r=t.getFilteredRowModel().flatRows,{rowSelection:i}=t.getState();let l=!!(r.length&&Object.keys(i).length);return l&&r.some(a=>a.getCanSelect()&&!i[a.id])&&(l=!1),l},t.getIsAllPageRowsSelected=()=>{const r=t.getPaginationRowModel().flatRows.filter(a=>a.getCanSelect()),{rowSelection:i}=t.getState();let l=!!r.length;return l&&r.some(a=>!i[a.id])&&(l=!1),l},t.getIsSomeRowsSelected=()=>{var r;const i=Object.keys((r=t.getState().rowSelection)!=null?r:{}).length;return i>0&&i<t.getFilteredRowModel().flatRows.length},t.getIsSomePageRowsSelected=()=>{const r=t.getPaginationRowModel().flatRows;return t.getIsAllPageRowsSelected()?!1:r.filter(i=>i.getCanSelect()).some(i=>i.getIsSelected()||i.getIsSomeSelected())},t.getToggleAllRowsSelectedHandler=()=>r=>{t.toggleAllRowsSelected(r.target.checked)},t.getToggleAllPageRowsSelectedHandler=()=>r=>{t.toggleAllPageRowsSelected(r.target.checked)}},createRow:(t,r)=>{t.toggleSelected=(i,l)=>{const a=t.getIsSelected();r.setRowSelection(c=>{var f;if(i=typeof i<"u"?i:!a,t.getCanSelect()&&a===i)return c;const h={...c};return xu(h,t.id,i,(f=l?.selectChildren)!=null?f:!0,r),h})},t.getIsSelected=()=>{const{rowSelection:i}=r.getState();return Bu(t,i)},t.getIsSomeSelected=()=>{const{rowSelection:i}=r.getState();return Cu(t,i)==="some"},t.getIsAllSubRowsSelected=()=>{const{rowSelection:i}=r.getState();return Cu(t,i)==="all"},t.getCanSelect=()=>{var i;return typeof r.options.enableRowSelection=="function"?r.options.enableRowSelection(t):(i=r.options.enableRowSelection)!=null?i:!0},t.getCanSelectSubRows=()=>{var i;return typeof r.options.enableSubRowSelection=="function"?r.options.enableSubRowSelection(t):(i=r.options.enableSubRowSelection)!=null?i:!0},t.getCanMultiSelect=()=>{var i;return typeof r.options.enableMultiRowSelection=="function"?r.options.enableMultiRowSelection(t):(i=r.options.enableMultiRowSelection)!=null?i:!0},t.getToggleSelectedHandler=()=>{const i=t.getCanSelect();return l=>{var a;i&&t.toggleSelected((a=l.target)==null?void 0:a.checked)}}}},xu=(t,r,i,l,a)=>{var c;const f=a.getRow(r,!0);i?(f.getCanMultiSelect()||Object.keys(t).forEach(h=>delete t[h]),f.getCanSelect()&&(t[r]=!0)):delete t[r],l&&(c=f.subRows)!=null&&c.length&&f.getCanSelectSubRows()&&f.subRows.forEach(h=>xu(t,h.id,i,l,a))};function lu(t,r){const i=t.getState().rowSelection,l=[],a={},c=function(f,h){return f.map(p=>{var m;const v=Bu(p,i);if(v&&(l.push(p),a[p.id]=p),(m=p.subRows)!=null&&m.length&&(p={...p,subRows:c(p.subRows)}),v)return p}).filter(Boolean)};return{rows:c(r.rows),flatRows:l,rowsById:a}}function Bu(t,r){var i;return(i=r[t.id])!=null?i:!1}function Cu(t,r,i){var l;if(!((l=t.subRows)!=null&&l.length))return!1;let a=!0,c=!1;return t.subRows.forEach(f=>{if(!(c&&!a)&&(f.getCanSelect()&&(Bu(f,r)?c=!0:a=!1),f.subRows&&f.subRows.length)){const h=Cu(f,r);h==="all"?c=!0:(h==="some"&&(c=!0),a=!1)}}),a?"all":c?"some":!1}const Ru=/([0-9]+)/gm,Nw=(t,r,i)=>mp(pr(t.getValue(i)).toLowerCase(),pr(r.getValue(i)).toLowerCase()),Dw=(t,r,i)=>mp(pr(t.getValue(i)),pr(r.getValue(i))),Tw=(t,r,i)=>Gu(pr(t.getValue(i)).toLowerCase(),pr(r.getValue(i)).toLowerCase()),jw=(t,r,i)=>Gu(pr(t.getValue(i)),pr(r.getValue(i))),zw=(t,r,i)=>{const l=t.getValue(i),a=r.getValue(i);return l>a?1:l<a?-1:0},Ow=(t,r,i)=>Gu(t.getValue(i),r.getValue(i));function Gu(t,r){return t===r?0:t>r?1:-1}function pr(t){return typeof t=="number"?isNaN(t)||t===1/0||t===-1/0?"":String(t):typeof t=="string"?t:""}function mp(t,r){const i=t.split(Ru).filter(Boolean),l=r.split(Ru).filter(Boolean);for(;i.length&&l.length;){const a=i.shift(),c=l.shift(),f=parseInt(a,10),h=parseInt(c,10),p=[f,h].sort();if(isNaN(p[0])){if(a>c)return 1;if(c>a)return-1;continue}if(isNaN(p[1]))return isNaN(f)?-1:1;if(f>h)return 1;if(h>f)return-1}return i.length-l.length}const yi={alphanumeric:Nw,alphanumericCaseSensitive:Dw,text:Tw,textCaseSensitive:jw,datetime:zw,basic:Ow},Iw={getInitialState:t=>({sorting:[],...t}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:t=>({onSortingChange:Ut("sorting",t),isMultiSortEvent:r=>r.shiftKey}),createColumn:(t,r)=>{t.getAutoSortingFn=()=>{const i=r.getFilteredRowModel().flatRows.slice(10);let l=!1;for(const a of i){const c=a?.getValue(t.id);if(Object.prototype.toString.call(c)==="[object Date]")return yi.datetime;if(typeof c=="string"&&(l=!0,c.split(Ru).length>1))return yi.alphanumeric}return l?yi.text:yi.basic},t.getAutoSortDir=()=>{const i=r.getFilteredRowModel().flatRows[0];return typeof i?.getValue(t.id)=="string"?"asc":"desc"},t.getSortingFn=()=>{var i,l;if(!t)throw new Error;return ls(t.columnDef.sortingFn)?t.columnDef.sortingFn:t.columnDef.sortingFn==="auto"?t.getAutoSortingFn():(i=(l=r.options.sortingFns)==null?void 0:l[t.columnDef.sortingFn])!=null?i:yi[t.columnDef.sortingFn]},t.toggleSorting=(i,l)=>{const a=t.getNextSortingOrder(),c=typeof i<"u"&&i!==null;r.setSorting(f=>{const h=f?.find(x=>x.id===t.id),p=f?.findIndex(x=>x.id===t.id);let m=[],v,S=c?i:a==="desc";if(f!=null&&f.length&&t.getCanMultiSort()&&l?h?v="toggle":v="add":f!=null&&f.length&&p!==f.length-1?v="replace":h?v="toggle":v="replace",v==="toggle"&&(c||a||(v="remove")),v==="add"){var y;m=[...f,{id:t.id,desc:S}],m.splice(0,m.length-((y=r.options.maxMultiSortColCount)!=null?y:Number.MAX_SAFE_INTEGER))}else v==="toggle"?m=f.map(x=>x.id===t.id?{...x,desc:S}:x):v==="remove"?m=f.filter(x=>x.id!==t.id):m=[{id:t.id,desc:S}];return m})},t.getFirstSortDir=()=>{var i,l;return((i=(l=t.columnDef.sortDescFirst)!=null?l:r.options.sortDescFirst)!=null?i:t.getAutoSortDir()==="desc")?"desc":"asc"},t.getNextSortingOrder=i=>{var l,a;const c=t.getFirstSortDir(),f=t.getIsSorted();return f?f!==c&&((l=r.options.enableSortingRemoval)==null||l)&&(!(i&&(a=r.options.enableMultiRemove)!=null)||a)?!1:f==="desc"?"asc":"desc":c},t.getCanSort=()=>{var i,l;return((i=t.columnDef.enableSorting)!=null?i:!0)&&((l=r.options.enableSorting)!=null?l:!0)&&!!t.accessorFn},t.getCanMultiSort=()=>{var i,l;return(i=(l=t.columnDef.enableMultiSort)!=null?l:r.options.enableMultiSort)!=null?i:!!t.accessorFn},t.getIsSorted=()=>{var i;const l=(i=r.getState().sorting)==null?void 0:i.find(a=>a.id===t.id);return l?l.desc?"desc":"asc":!1},t.getSortIndex=()=>{var i,l;return(i=(l=r.getState().sorting)==null?void 0:l.findIndex(a=>a.id===t.id))!=null?i:-1},t.clearSorting=()=>{r.setSorting(i=>i!=null&&i.length?i.filter(l=>l.id!==t.id):[])},t.getToggleSortingHandler=()=>{const i=t.getCanSort();return l=>{i&&(l.persist==null||l.persist(),t.toggleSorting==null||t.toggleSorting(void 0,t.getCanMultiSort()?r.options.isMultiSortEvent==null?void 0:r.options.isMultiSortEvent(l):!1))}}},createTable:t=>{t.setSorting=r=>t.options.onSortingChange==null?void 0:t.options.onSortingChange(r),t.resetSorting=r=>{var i,l;t.setSorting(r?[]:(i=(l=t.initialState)==null?void 0:l.sorting)!=null?i:[])},t.getPreSortedRowModel=()=>t.getGroupedRowModel(),t.getSortedRowModel=()=>(!t._getSortedRowModel&&t.options.getSortedRowModel&&(t._getSortedRowModel=t.options.getSortedRowModel(t)),t.options.manualSorting||!t._getSortedRowModel?t.getPreSortedRowModel():t._getSortedRowModel())}},$w=[ow,Ew,ww,Sw,lw,sw,Pw,kw,Iw,vw,_w,Mw,Fw,Lw,Cw];function Aw(t){var r,i;const l=[...$w,...(r=t._features)!=null?r:[]];let a={_features:l};const c=a._features.reduce((y,x)=>Object.assign(y,x.getDefaultOptions==null?void 0:x.getDefaultOptions(a)),{}),f=y=>a.options.mergeOptions?a.options.mergeOptions(c,y):{...c,...y};let p={...{},...(i=t.initialState)!=null?i:{}};a._features.forEach(y=>{var x;p=(x=y.getInitialState==null?void 0:y.getInitialState(p))!=null?x:p});const m=[];let v=!1;const S={_features:l,options:{...c,...t},initialState:p,_queue:y=>{m.push(y),v||(v=!0,Promise.resolve().then(()=>{for(;m.length;)m.shift()();v=!1}).catch(x=>setTimeout(()=>{throw x})))},reset:()=>{a.setState(a.initialState)},setOptions:y=>{const x=dr(y,a.options);a.options=f(x)},getState:()=>a.options.state,setState:y=>{a.options.onStateChange==null||a.options.onStateChange(y)},_getRowId:(y,x,C)=>{var P;return(P=a.options.getRowId==null?void 0:a.options.getRowId(y,x,C))!=null?P:`${C?[C.id,x].join("."):x}`},getCoreRowModel:()=>(a._getCoreRowModel||(a._getCoreRowModel=a.options.getCoreRowModel(a)),a._getCoreRowModel()),getRowModel:()=>a.getPaginationRowModel(),getRow:(y,x)=>{let C=(x?a.getPrePaginationRowModel():a.getRowModel()).rowsById[y];if(!C&&(C=a.getCoreRowModel().rowsById[y],!C))throw new Error;return C},_getDefaultColumnDef:me(()=>[a.options.defaultColumn],y=>{var x;return y=(x=y)!=null?x:{},{header:C=>{const P=C.header.column.columnDef;return P.accessorKey?P.accessorKey:P.accessorFn?P.id:null},cell:C=>{var P,T;return(P=(T=C.renderValue())==null||T.toString==null?void 0:T.toString())!=null?P:null},...a._features.reduce((C,P)=>Object.assign(C,P.getDefaultColumnDef==null?void 0:P.getDefaultColumnDef()),{}),...y}},ve(t,"debugColumns")),_getColumnDefs:()=>a.options.columns,getAllColumns:me(()=>[a._getColumnDefs()],y=>{const x=function(C,P,T){return T===void 0&&(T=0),C.map(F=>{const B=rw(a,F,T,P),A=F;return B.columns=A.columns?x(A.columns,B,T+1):[],B})};return x(y)},ve(t,"debugColumns")),getAllFlatColumns:me(()=>[a.getAllColumns()],y=>y.flatMap(x=>x.getFlatColumns()),ve(t,"debugColumns")),_getAllFlatColumnsById:me(()=>[a.getAllFlatColumns()],y=>y.reduce((x,C)=>(x[C.id]=C,x),{}),ve(t,"debugColumns")),getAllLeafColumns:me(()=>[a.getAllColumns(),a._getOrderColumnsFn()],(y,x)=>{let C=y.flatMap(P=>P.getLeafColumns());return x(C)},ve(t,"debugColumns")),getColumn:y=>a._getAllFlatColumnsById()[y]};Object.assign(a,S);for(let y=0;y<a._features.length;y++){const x=a._features[y];x==null||x.createTable==null||x.createTable(a)}return a}function Qu(){return t=>me(()=>[t.options.data],r=>{const i={rows:[],flatRows:[],rowsById:{}},l=function(a,c,f){c===void 0&&(c=0);const h=[];for(let m=0;m<a.length;m++){const v=iw(t,t._getRowId(a[m],m,f),a[m],m,c,void 0,f?.id);if(i.flatRows.push(v),i.rowsById[v.id]=v,h.push(v),t.options.getSubRows){var p;v.originalSubRows=t.options.getSubRows(a[m],m),(p=v.originalSubRows)!=null&&p.length&&(v.subRows=l(v.originalSubRows,c+1,v))}}return h};return i.rows=l(r),i},ve(t.options,"debugTable","getRowModel",()=>t._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function ch(t,r){return t?bw(t)?L.createElement(t,r):t:null}function bw(t){return Vw(t)||typeof t=="function"||Uw(t)}function Vw(t){return typeof t=="function"&&(()=>{const r=Object.getPrototypeOf(t);return r.prototype&&r.prototype.isReactComponent})()}function Uw(t){return typeof t=="object"&&typeof t.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(t.$$typeof.description)}function Wu(t){const r={state:{},onStateChange:()=>{},renderFallbackValue:null,...t},[i]=L.useState(()=>({current:Aw(r)})),[l,a]=L.useState(()=>i.current.initialState);return i.current.setOptions(c=>({...c,...t,state:{...l,...t.state},onStateChange:f=>{a(f),t.onStateChange==null||t.onStateChange(f)}})),i.current}function dh(t,r){if(typeof t=="function")return t(r);t!=null&&(t.current=r)}function Hw(...t){return r=>{let i=!1;const l=t.map(a=>{const c=dh(a,r);return!i&&typeof c=="function"&&(i=!0),c});if(i)return()=>{for(let a=0;a<l.length;a++){const c=l[a];typeof c=="function"?c():dh(t[a],null)}}}}function Bw(t){const r=Qw(t),i=L.forwardRef((l,a)=>{const{children:c,...f}=l,h=L.Children.toArray(c),p=h.find(qw);if(p){const m=p.props.children,v=h.map(S=>S===p?L.Children.count(m)>1?L.Children.only(null):L.isValidElement(m)?m.props.children:null:S);return M.jsx(r,{...f,ref:a,children:L.isValidElement(m)?L.cloneElement(m,void 0,v):null})}return M.jsx(r,{...f,ref:a,children:c})});return i.displayName=`${t}.Slot`,i}var Gw=Bw("Slot");function Qw(t){const r=L.forwardRef((i,l)=>{const{children:a,...c}=i;if(L.isValidElement(a)){const f=Yw(a),h=Kw(c,a.props);return a.type!==L.Fragment&&(h.ref=l?Hw(l,f):f),L.cloneElement(a,h)}return L.Children.count(a)>1?L.Children.only(null):null});return r.displayName=`${t}.SlotClone`,r}var Ww=Symbol("radix.slottable");function qw(t){return L.isValidElement(t)&&typeof t.type=="function"&&"__radixId"in t.type&&t.type.__radixId===Ww}function Kw(t,r){const i={...r};for(const l in r){const a=t[l],c=r[l];/^on[A-Z]/.test(l)?a&&c?i[l]=(...h)=>{const p=c(...h);return a(...h),p}:a&&(i[l]=a):l==="style"?i[l]={...a,...c}:l==="className"&&(i[l]=[a,c].filter(Boolean).join(" "))}return{...t,...i}}function Yw(t){let r=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?t.ref:(r=Object.getOwnPropertyDescriptor(t,"ref")?.get,i=r&&"isReactWarning"in r&&r.isReactWarning,i?t.props.ref:t.props.ref||t.ref)}const fh=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,hh=Qh,Xw=(t,r)=>i=>{var l;if(r?.variants==null)return hh(t,i?.class,i?.className);const{variants:a,defaultVariants:c}=r,f=Object.keys(a).map(m=>{const v=i?.[m],S=c?.[m];if(v===null)return null;const y=fh(v)||fh(S);return a[m][y]}),h=i&&Object.entries(i).reduce((m,v)=>{let[S,y]=v;return y===void 0||(m[S]=y),m},{}),p=r==null||(l=r.compoundVariants)===null||l===void 0?void 0:l.reduce((m,v)=>{let{class:S,className:y,...x}=v;return Object.entries(x).every(C=>{let[P,T]=C;return Array.isArray(T)?T.includes({...c,...h}[P]):{...c,...h}[P]===T})?[...m,S,y]:m},[]);return hh(t,f,p,i?.class,i?.className)},Zw=Xw("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),gr=L.forwardRef(({className:t,variant:r,size:i,asChild:l=!1,...a},c)=>{const f=l?Gw:"button";return M.jsx(f,{className:Ct(Zw({variant:r,size:i,className:t})),ref:c,...a})});gr.displayName="Button";const vp=L.forwardRef(({className:t,...r},i)=>M.jsx("div",{className:"relative w-full overflow-auto",children:M.jsx("table",{ref:i,className:Ct("w-full caption-bottom text-sm",t),...r})}));vp.displayName="Table";const yp=L.forwardRef(({className:t,...r},i)=>M.jsx("thead",{ref:i,className:Ct("[&_tr]:border-b",t),...r}));yp.displayName="TableHeader";const wp=L.forwardRef(({className:t,...r},i)=>M.jsx("tbody",{ref:i,className:Ct("[&_tr:last-child]:border-0",t),...r}));wp.displayName="TableBody";const Jw=L.forwardRef(({className:t,...r},i)=>M.jsx("tfoot",{ref:i,className:Ct("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...r}));Jw.displayName="TableFooter";const Zl=L.forwardRef(({className:t,...r},i)=>M.jsx("tr",{ref:i,className:Ct("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...r}));Zl.displayName="TableRow";const Sp=L.forwardRef(({className:t,...r},i)=>M.jsx("th",{ref:i,className:Ct("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...r}));Sp.displayName="TableHead";const Eu=L.forwardRef(({className:t,...r},i)=>M.jsx("td",{ref:i,className:Ct("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...r}));Eu.displayName="TableCell";const eS=L.forwardRef(({className:t,...r},i)=>M.jsx("caption",{ref:i,className:Ct("mt-4 text-sm text-muted-foreground",t),...r}));eS.displayName="TableCaption";function qu({table:t,isLoading:r,isError:i}){return r?M.jsx("div",{className:"flex items-center justify-center h-32",children:"加载中..."}):i?M.jsx("div",{className:"flex items-center justify-center h-32 text-red-500",children:"加载数据失败"}):t?M.jsxs("div",{className:"rounded-md border",children:[M.jsxs(vp,{children:[M.jsx(yp,{children:t.getHeaderGroups().map(l=>M.jsx(Zl,{children:l.headers.map(a=>M.jsx(Sp,{children:a.isPlaceholder?null:ch(a.column.columnDef.header,a.getContext())},a.id))},l.id))}),M.jsx(wp,{children:t.getRowModel().rows?.length?t.getRowModel().rows.map(l=>M.jsx(Zl,{"data-state":l.getIsSelected()&&"selected",children:l.getVisibleCells().map(a=>M.jsx(Eu,{children:ch(a.column.columnDef.cell,a.getContext())},a.id))},l.id)):M.jsx(Zl,{children:M.jsx(Eu,{colSpan:t.getAllColumns().length,className:"h-24 text-center",children:"暂无数据"})})})]}),M.jsxs("div",{className:"flex items-center justify-end space-x-2 py-4 px-4",children:[M.jsx(gr,{variant:"outline",size:"sm",onClick:()=>t.previousPage(),disabled:!t.getCanPreviousPage(),children:"上一页"}),M.jsx(gr,{variant:"outline",size:"sm",onClick:()=>t.nextPage(),disabled:!t.getCanNextPage(),children:"下一页"})]})]}):M.jsx("div",{className:"flex items-center justify-center h-32",children:"表格数据未就绪"})}const tS="http://localhost:3001";class nS{baseURL;constructor(r){this.baseURL=r}async request(r,i={}){const l=`${this.baseURL}${r}`,a={headers:{"Content-Type":"application/json",...i.headers},...i};try{const c=await fetch(l,a);if(!c.ok)throw new Error(`HTTP error! status: ${c.status}`);return await c.json()}catch(c){throw console.error("API request failed:",c),c}}async get(r){return this.request(r,{method:"GET"})}async post(r,i){return this.request(r,{method:"POST",body:JSON.stringify(i)})}async put(r,i){return this.request(r,{method:"PUT",body:JSON.stringify(i)})}async delete(r){return this.request(r,{method:"DELETE"})}}const dn=new nS(tS),rS={getBuilds:t=>{const r=new URLSearchParams;t?.page&&r.append("page",t.page.toString()),t?.limit&&r.append("limit",t.limit.toString());const i=`/api/builds${r.toString()?`?${r.toString()}`:""}`;return dn.get(i)},submitAnalysisResult:t=>dn.post("/api/builds/analysis-result",t),receiveBuildResult:t=>dn.post("/api/webhook/build_result",t)},oS={getUsers:()=>dn.get("/api/users"),createUser:t=>dn.post("/api/users",t),updateUser:(t,r)=>dn.put(`/api/users/${t}`,r),deleteUser:t=>dn.delete(`/api/users/${t}`)},iS={getApps:()=>dn.get("/api/apps"),createApp:t=>dn.post("/api/apps",t),updateApp:(t,r)=>dn.put(`/api/apps/${t}`,r),deleteApp:t=>dn.delete(`/api/apps/${t}`)},wi=Uu(),lS=[wi.accessor("id",{header:"ID",cell:t=>t.getValue()}),wi.accessor("name",{header:"应用名称",cell:t=>t.getValue()}),wi.accessor("description",{header:"应用描述",cell:t=>t.getValue()}),wi.accessor("status",{header:"状态",cell:t=>M.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${t.getValue()==="active"?"bg-green-100 text-green-800":t.getValue()==="inactive"?"bg-gray-100 text-gray-800":"bg-yellow-100 text-yellow-800"}`,children:t.getValue()==="active"?"活跃":t.getValue()==="inactive"?"停用":"测试中"})}),wi.accessor("createdAt",{header:"创建时间",cell:t=>new Date(t.getValue()).toLocaleString("zh-CN")})],sS=async()=>{try{return await iS.getApps()}catch(t){return console.error("获取应用列表失败:",t),[{id:1,name:"运维管理系统",description:"用于管理服务器和应用部署的系统",status:"active",createdAt:"2025-06-15T08:30:00.000Z"},{id:2,name:"监控平台",description:"服务器和应用性能监控",status:"active",createdAt:"2025-06-10T14:20:00.000Z"},{id:3,name:"日志分析系统",description:"集中式日志收集和分析",status:"testing",createdAt:"2025-06-20T09:45:00.000Z"}]}},aS=()=>{const{data:t,isLoading:r,isError:i,error:l}=Vu({queryKey:["apps"],queryFn:sS}),a=Wu({data:t||[],columns:lS,getCoreRowModel:Qu()});return i&&console.warn("应用列表加载失败:",l),M.jsx("div",{className:"container mx-auto px-4 py-6",children:M.jsxs(yn,{children:[M.jsxs(xo,{className:"flex flex-row items-center justify-between",children:[M.jsx(Co,{children:"应用管理"}),i&&M.jsx("span",{className:"text-amber-600 text-sm",children:"⚠️ API连接失败，显示模拟数据"})]}),M.jsx(wn,{children:M.jsx(qu,{table:a,isLoading:r,isError:!1})})]})})},uS=t=>({id:t.BuildRecordID,repository:t.Repository,branch:t.Branch.trim(),status:t.Status,email:t.Email,buildTime:new Date(t.EndTime).toISOString(),analysisAvailable:t.analysisAvailable||!1}),$r=Uu(),cS=t=>{switch(t){case 1:return"进行中";case 2:return"成功";case 3:return"失败";case 4:return"构建错误";default:return"未知"}},dS=t=>{switch(t){case 1:return"bg-blue-100 text-blue-800";case 2:return"bg-green-100 text-green-800";case 3:return"bg-red-100 text-red-800";case 4:return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},fS=[$r.accessor("id",{header:"构建ID",cell:t=>t.getValue()}),$r.accessor("repository",{header:"代码仓库",cell:t=>{const r=t.getValue(),i=r.split("/").pop()?.replace(".git","")||r;return M.jsx("span",{title:r,children:i})}}),$r.accessor("branch",{header:"分支",cell:t=>t.getValue()}),$r.accessor("status",{header:"状态",cell:t=>M.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${dS(t.getValue())}`,children:cS(t.getValue())})}),$r.accessor("email",{header:"提交者",cell:t=>t.getValue()}),$r.accessor("buildTime",{header:"构建时间",cell:t=>new Date(t.getValue()).toLocaleString("zh-CN")}),$r.accessor(t=>t,{id:"actions",header:"操作",cell:t=>{const r=t.getValue();return M.jsxs("div",{className:"flex space-x-2",children:[M.jsxs(gr,{variant:"outline",size:"sm",children:[M.jsx(du,{className:"h-4 w-4 mr-1"}),"日志"]}),r.status>=3&&M.jsxs(gr,{variant:"outline",size:"sm",className:r.analysisAvailable?"text-green-600":"",children:[M.jsx(My,{className:"h-4 w-4 mr-1"}),r.analysisAvailable?"AI分析":"分析构建"]})]})}})],hS=async()=>{try{return(await rS.getBuilds({page:1,limit:50})).data.map(uS)}catch(t){return console.error("获取构建记录失败:",t),[{id:94233,repository:"https://github.com/example/repo1.git",branch:"main",status:2,email:"<EMAIL>",buildTime:"2025-06-27T08:30:00.000Z",analysisAvailable:!1},{id:94232,repository:"https://github.com/example/repo2.git",branch:"develop",status:3,email:"<EMAIL>",buildTime:"2025-06-26T14:20:00.000Z",analysisAvailable:!0}]}},pS=()=>{const{data:t,isLoading:r,isError:i,error:l}=Vu({queryKey:["builds"],queryFn:hS,refetchInterval:3e4,retry:2}),a=Wu({data:t||[],columns:fS,getCoreRowModel:Qu()});return i&&console.warn("构建记录加载失败:",l),M.jsx("div",{className:"container mx-auto px-4 py-6",children:M.jsxs(yn,{children:[M.jsxs(xo,{className:"flex flex-row items-center justify-between",children:[M.jsx(Co,{children:"构建记录"}),M.jsx("div",{className:"text-sm text-gray-500",children:i&&M.jsx("span",{className:"text-amber-600",children:"⚠️ API连接失败，显示模拟数据"})})]}),M.jsx(wn,{children:M.jsx(qu,{table:a,isLoading:r,isError:!1})})]})})},ar=Uu(),gS=[ar.accessor("id",{header:"ID",cell:t=>t.getValue()}),ar.accessor("name",{header:"姓名",cell:t=>t.getValue()}),ar.accessor("email",{header:"邮箱",cell:t=>t.getValue()}),ar.accessor("role",{header:"角色",cell:t=>t.getValue()}),ar.accessor("department",{header:"部门",cell:t=>t.getValue()}),ar.accessor("active",{header:"状态",cell:t=>M.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${t.getValue()?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.getValue()?"活跃":"停用"})}),ar.accessor("lastLogin",{header:"最后登录",cell:t=>new Date(t.getValue()).toLocaleString("zh-CN")}),ar.accessor(t=>t,{id:"actions",header:"操作",cell:t=>M.jsxs("div",{className:"flex space-x-2",children:[M.jsx(gr,{variant:"ghost",size:"sm",children:M.jsx(Ny,{className:"h-4 w-4"})}),M.jsx(gr,{variant:"ghost",size:"sm",className:"text-red-600",children:M.jsx(Dy,{className:"h-4 w-4"})})]})})],mS=async()=>{try{return await oS.getUsers()}catch(t){return console.error("获取用户列表失败:",t),[{id:1,name:"张三",email:"<EMAIL>",role:"管理员",department:"研发部",active:!0,lastLogin:"2025-06-27T08:30:00.000Z"},{id:2,name:"李四",email:"<EMAIL>",role:"开发者",department:"研发部",active:!0,lastLogin:"2025-06-26T14:20:00.000Z"},{id:3,name:"王五",email:"<EMAIL>",role:"运维",department:"运维部",active:!1,lastLogin:"2025-05-15T09:45:00.000Z"},{id:4,name:"赵六",email:"<EMAIL>",role:"开发者",department:"测试部",active:!0,lastLogin:"2025-06-24T16:10:00.000Z"}]}},vS=()=>{const{data:t,isLoading:r,isError:i,error:l}=Vu({queryKey:["users"],queryFn:mS}),a=Wu({data:t||[],columns:gS,getCoreRowModel:Qu()});return i&&console.warn("用户列表加载失败:",l),M.jsx("div",{className:"container mx-auto px-4 py-6",children:M.jsxs(yn,{children:[M.jsxs(xo,{className:"flex flex-row justify-between items-center",children:[M.jsx(Co,{children:"用户管理"}),M.jsxs("div",{className:"flex items-center gap-4",children:[i&&M.jsx("span",{className:"text-amber-600 text-sm",children:"⚠️ API连接失败，显示模拟数据"}),M.jsx(gr,{children:"添加用户"})]})]}),M.jsx(wn,{children:M.jsx(qu,{table:a,isLoading:r,isError:!1})})]})})};function yS({children:t}){const[r]=L.useState(()=>new I0({defaultOptions:{queries:{staleTime:6e4}}}));return M.jsx(U0,{client:r,children:t})}const wS={theme:"system",setTheme:()=>null},SS=L.createContext(wS);function xS({children:t,defaultTheme:r="system",storageKey:i="vite-ui-theme",...l}){const[a,c]=L.useState(()=>localStorage.getItem(i)||r);L.useEffect(()=>{const h=window.document.documentElement;if(h.classList.remove("light","dark"),a==="system"){const p=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";h.classList.add(p);return}h.classList.add(a)},[a]);const f={theme:a,setTheme:h=>{localStorage.setItem(i,h),c(h)}};return M.jsx(SS.Provider,{...l,value:f,children:t})}const CS=ay([{path:"/",element:M.jsx($y,{}),children:[{index:!0,element:M.jsx(w0,{})},{path:"system/apps",element:M.jsx(aS,{})},{path:"system/builds",element:M.jsx(pS,{})},{path:"system/users",element:M.jsx(vS,{})}]}]);fm.createRoot(document.getElementById("root")).render(M.jsx(lm.StrictMode,{children:M.jsx(xS,{defaultTheme:"light",storageKey:"vite-ui-theme",children:M.jsx(yS,{children:M.jsx(xy,{router:CS})})})}));
