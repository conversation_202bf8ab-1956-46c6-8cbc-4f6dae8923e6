# Azure 管理后台 - Next.js 版本

这是一个使用 Next.js + React + Tailwind CSS 构建的后台管理系统。

## 技术栈

- **框架**: Next.js 14 (App Router)
- **前端**: React 18 + TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Radix UI + Shadcn/ui
- **状态管理**: React Query (TanStack Query)
- **图标**: Lucide React

## 功能特性

- 🎨 现代化的深蓝色侧边栏设计
- 📱 响应式布局，支持移动端
- 🔧 可配置的二级菜单系统
- 📊 数据表格展示和管理
- 🔍 搜索和筛选功能
- 📋 发布记录管理
- 👥 用户和权限管理

## 项目结构

```
├── app/                  # Next.js App Router 页面
│   ├── globals.css      # 全局样式
│   ├── layout.tsx       # 根布局
│   ├── page.tsx         # 首页
│   └── system/          # 系统管理页面
├── components/          # 可复用组件
│   ├── AdminLayout.tsx  # 管理后台布局
│   ├── Sidebar.tsx      # 侧边栏组件
│   ├── Header.tsx       # 头部组件
│   ├── DataTable.tsx    # 数据表格组件
│   └── ui/              # UI基础组件
├── lib/                 # 工具函数
├── hooks/               # 自定义Hooks
└── providers/           # 全局状态提供者
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式运行

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm run start
```

## 菜单配置

菜单配置在 `components/Sidebar.tsx` 中，支持多级菜单：

```typescript
const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: '仪表盘',
    icon: BarChart3,
    path: '/'
  },
  {
    id: 'system',
    title: '系统设置',
    icon: Settings,
    children: [
      { id: 'user-management', title: '用户管理', icon: Users, path: '/system/users' },
      // ... 更多子菜单
    ]
  }
];
```

## 页面路由

- `/` - 首页/仪表盘
- `/system/users` - 用户管理
- `/system/apps` - 应用管理
- `/system/services` - 服务管理
- `/publish/*` - 发布管理相关页面
- `/tools/*` - 运维工具相关页面

## 开发指南

### 添加新页面

1. 在 `app/` 目录下创建对应的文件夹和 `page.tsx`
2. 使用 `AdminLayout` 组件包装页面内容
3. 在 `components/Sidebar.tsx` 中添加菜单项

### 样式定制

项目使用 Tailwind CSS，主要颜色配置在 `app/globals.css` 中。

## 部署

项目支持多种部署方式：

- **Vercel**: 推荐，一键部署
- **Docker**: 容器化部署
- **传统服务器**: 构建后部署到 Node.js 服务器

## 许可证

MIT License
