from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc
import httpx
import json

from db.database import get_db
from db.models import BuildRecord, BuildAnalysis
from schemas import (
    BuildAnalysisCreate,
    BuildAnalysisResponse,
    AnalysisListResponse,
    AnalysisCreateResponse,
)

router = APIRouter(prefix="/api/builds", tags=["AI分析"])


@router.post("/analysis-result", response_model=AnalysisCreateResponse)
async def create_analysis_result(
    analysis_data: BuildAnalysisCreate, db: Session = Depends(get_db)
):
    """上报分析结果"""
    try:
        # 验证构建记录是否存在
        build_record = (
            db.query(BuildRecord)
            .filter(BuildRecord.BuildRecordID == analysis_data.buildRecordId)
            .first()
        )

        if not build_record:
            raise HTTPException(
                status_code=404,
                detail=f"构建记录不存在: BuildRecordID {analysis_data.buildRecordId}",
            )

        # 创建分析记录
        new_analysis = BuildAnalysis(**analysis_data.model_dump())
        db.add(new_analysis)
        db.commit()
        db.refresh(new_analysis)

        return AnalysisCreateResponse(
            success=True, message="分析结果保存成功", data=new_analysis
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"保存分析结果失败: {str(e)}")


@router.get("/analysis-result", response_model=AnalysisListResponse)
async def get_analysis_results(
    buildRecordId: int = Query(..., description="构建记录ID"),
    db: Session = Depends(get_db),
):
    """查询分析记录"""
    try:
        # 查询指定构建记录的所有分析结果
        analysis_results = (
            db.query(BuildAnalysis)
            .options(joinedload(BuildAnalysis.build_record))
            .filter(BuildAnalysis.buildRecordId == buildRecordId)
            .order_by(desc(BuildAnalysis.createdAt))
            .all()
        )

        # 为每个分析结果添加构建记录信息
        for analysis in analysis_results:
            if analysis.build_record:
                analysis.buildResult = analysis.build_record

        total = len(analysis_results)

        return AnalysisListResponse(success=True, data=analysis_results, total=total)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询分析记录失败: {str(e)}")


@router.post("/analysis")
async def forward_to_ai_analysis(request_data: dict):
    """
    转发构建数据到AI分析服务器
    将请求转发到 http://10.0.0.51:7000/webhook/build_result
    """
    try:
        # AI服务器地址
        ai_server_url = "http://10.0.0.51:7000/webhook/build_result"

        # 使用 httpx 发送异步请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                ai_server_url,
                json=request_data,
                headers={"Content-Type": "application/json"},
            )

            # 检查响应状态
            if not response.is_success:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"AI 服务器响应错误: {response.status_code}",
                )

            # 返回AI服务器的响应
            try:
                result = response.json()
                return result
            except json.JSONDecodeError:
                # 如果响应不是JSON格式，返回文本内容
                return {"message": response.text}

    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="AI 服务器请求超时")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="无法连接到AI服务器")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AI 分析请求失败: {str(e)}")
