from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc

from db.database import get_db
from db.models import BuildRecord, BuildAnalysis
from schemas import (
    BuildAnalysisCreate, BuildAnalysisResponse,
    AnalysisListResponse, AnalysisCreateResponse
)

router = APIRouter(prefix="/api/builds", tags=["AI分析"])

@router.post("/analysis-result", response_model=AnalysisCreateResponse)
async def create_analysis_result(
    analysis_data: BuildAnalysisCreate,
    db: Session = Depends(get_db)
):
    """上报分析结果"""
    try:
        # 验证构建记录是否存在
        build_record = db.query(BuildRecord)\
                        .filter(BuildRecord.BuildRecordID == analysis_data.buildRecordId)\
                        .first()
        
        if not build_record:
            raise HTTPException(
                status_code=404, 
                detail=f"构建记录不存在: BuildRecordID {analysis_data.buildRecordId}"
            )
        
        # 创建分析记录
        new_analysis = BuildAnalysis(**analysis_data.dict())
        db.add(new_analysis)
        db.commit()
        db.refresh(new_analysis)
        
        return AnalysisCreateResponse(
            success=True,
            message="分析结果保存成功",
            data=new_analysis
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"保存分析结果失败: {str(e)}")

@router.get("/analysis-result", response_model=AnalysisListResponse)
async def get_analysis_results(
    buildRecordId: int = Query(..., description="构建记录ID"),
    db: Session = Depends(get_db)
):
    """查询分析记录"""
    try:
        # 查询指定构建记录的所有分析结果
        analysis_results = db.query(BuildAnalysis)\
                            .options(joinedload(BuildAnalysis.build_record))\
                            .filter(BuildAnalysis.buildRecordId == buildRecordId)\
                            .order_by(desc(BuildAnalysis.createdAt))\
                            .all()
        
        # 为每个分析结果添加构建记录信息
        for analysis in analysis_results:
            if analysis.build_record:
                analysis.buildResult = analysis.build_record
        
        total = len(analysis_results)
        
        return AnalysisListResponse(
            success=True,
            data=analysis_results,
            total=total
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询分析记录失败: {str(e)}") 