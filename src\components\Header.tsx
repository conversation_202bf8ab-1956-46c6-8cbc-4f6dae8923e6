
import { Bell, User, Menu } from 'lucide-react';

interface HeaderProps {
  onMenuToggle: () => void;
  sidebarCollapsed: boolean;
}

export const Header = ({ onMenuToggle, sidebarCollapsed: _sidebarCollapsed }: HeaderProps) => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onMenuToggle}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <Menu className="w-5 h-5 text-gray-600" />
          </button>
          <div className="text-sm text-gray-500">
            <span className="font-medium">版本信息:</span> ver 1.6.0.1600
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <button className="p-2 rounded-lg hover:bg-gray-100 transition-colors relative">
            <Bell className="w-5 h-5 text-gray-600" />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </button>
          
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <div className="text-sm">
              <div className="font-medium text-gray-900">管理员</div>
              <div className="text-gray-500">administrator</div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}; 