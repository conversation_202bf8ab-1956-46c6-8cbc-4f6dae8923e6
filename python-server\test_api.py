#!/usr/bin/env python3
"""
API测试脚本
用于测试构建记录和AI分析结果相关的接口
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:3001"

# 测试数据
test_build_data = {
    "BuildRecordID": 94233,
    "ItemId": 2001,
    "BuildTrigger": 1,
    "BuildType": 2,
    "BuildLang": "nodejs",
    "BuildVars": "NODE_ENV=production",
    "AppID": 3001,
    "ServiceID": 4001,
    "Status": 3,  # 构建错误状态
    "Repository": "http://yw-gitlab.miniworldplus.com/miniw/soc-game.git",
    "Branch": "develop",
    "Commit": "fcd96b111e579247caa082ae20012edf6ba6a0ec",
    "StartTime": int(time.time() * 1000) - 300000,  # 5分钟前开始
    "EndTime": int(time.time() * 1000),  # 现在结束
    "Email": "<EMAIL>",
    "ArtifactName": "app-v1.0.0.tar.gz",
    "ArtifactMd5": "d41d8cd98f00b204e9800998ecf8427e",
    "ArtifactUrl": "https://artifacts.example.com/app-v1.0.0.tar.gz",
    "PackageID": 5001,
    "BuildLogUrl": "buildlogs/2025-06-27/socgame_prod_1729495265_473.log",
}

test_analysis_data = {
    "buildRecordId": 94233,
    "provider": "claude",
    "model": "claude-3-5-sonnet-20241022",
    "context": "构建失败，错误信息：npm install 失败，依赖包不存在",
    "analysisResult": """## Claude AI 分析结果

### 问题分析
构建失败的主要原因是npm install步骤出现错误。

### 可能原因
1. package.json中指定的依赖版本不存在
2. npm registry访问问题
3. 网络连接问题

### 解决建议
1. 检查package.json中的依赖版本
2. 尝试使用npm cache clean清理缓存
3. 检查网络连接和npm registry配置

### 预计修复时间
约10-30分钟""",
    "analysisStatus": 2,  # 分析完成
}


def test_health_check():
    """测试健康检查接口"""
    print("🧪 测试健康检查接口...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        result = response.json()
        print(f"✅ 健康检查成功: {result}")
        return True
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False


def test_webhook_build_result():
    """测试构建结果Webhook接口"""
    print("\n🧪 测试构建结果Webhook...")
    try:
        response = requests.post(
            f"{BASE_URL}/api/webhook/build_result",
            json=test_build_data,
            headers={"Content-Type": "application/json"},
        )
        result = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        if response.status_code == 200 and result.get("Ret") == 0:
            print("✅ 构建结果Webhook测试成功!")
            return True
        else:
            print(f"❌ 构建结果Webhook测试失败: {result}")
            return False
    except Exception as e:
        print(f"❌ 构建结果Webhook测试错误: {e}")
        return False


def test_get_builds():
    """测试获取构建记录列表"""
    print("\n🧪 测试获取构建记录列表...")
    try:
        response = requests.get(f"{BASE_URL}/api/builds?page=1&limit=10")
        result = response.json()
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print(f"✅ 获取构建记录成功!")
            print(f"📊 总记录数: {result.get('total', 0)}")
            print(f"📄 当前页数据: {len(result.get('data', []))} 条")

            # 显示前3条记录的简要信息
            for i, build in enumerate(result.get("data", [])[:3]):
                print(
                    f"  {i+1}. BuildRecordID: {build.get('BuildRecordID')}, "
                    f"Status: {build.get('Status')}, "
                    f"Repository: {build.get('Repository', '')[:50]}..."
                )
            return True
        else:
            print(f"❌ 获取构建记录失败: {result}")
            return False
    except Exception as e:
        print(f"❌ 获取构建记录错误: {e}")
        return False


def test_analysis_result():
    """测试AI分析结果接口"""
    print("\n🧪 测试AI分析结果上报...")
    try:
        # 先确保有构建记录
        test_webhook_build_result()

        # 上报分析结果
        response = requests.post(
            f"{BASE_URL}/api/builds/analysis-result",
            json=test_analysis_data,
            headers={"Content-Type": "application/json"},
        )
        result = response.json()
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        if response.status_code == 200 and result.get("success"):
            print("✅ AI分析结果上报成功!")
            return True
        else:
            print(f"❌ AI分析结果上报失败: {result}")
            return False
    except Exception as e:
        print(f"❌ AI分析结果上报错误: {e}")
        return False


def test_get_analysis_results():
    """测试查询AI分析记录"""
    print("\n🧪 测试查询AI分析记录...")
    try:
        build_id = test_analysis_data["buildRecordId"]
        response = requests.get(
            f"{BASE_URL}/api/builds/analysis-result?buildRecordId={build_id}"
        )
        result = response.json()
        print(f"状态码: {response.status_code}")

        if response.status_code == 200 and result.get("success"):
            print(f"✅ 查询AI分析记录成功!")
            print(f"📊 分析记录数: {result.get('total', 0)}")

            # 显示分析记录简要信息
            for i, analysis in enumerate(result.get("data", [])):
                print(
                    f"  {i+1}. ID: {analysis.get('id')}, "
                    f"Provider: {analysis.get('provider')}, "
                    f"Model: {analysis.get('model')}, "
                    f"Status: {analysis.get('analysisStatus')}"
                )
            return True
        else:
            print(f"❌ 查询AI分析记录失败: {result}")
            return False
    except Exception as e:
        print(f"❌ 查询AI分析记录错误: {e}")
        return False


def test_api_docs():
    """测试API文档访问"""
    print("\n🧪 测试API文档访问...")
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✅ API文档访问成功!")
            print(f"📖 文档地址: {BASE_URL}/docs")
            return True
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API文档访问错误: {e}")
        return False


def main():
    """运行所有测试"""
    print("🚀 开始API测试...\n")
    print(f"🎯 目标服务器: {BASE_URL}")
    print("⏳ 等待服务器启动...")
    time.sleep(2)  # 等待服务器启动

    tests = [
        ("健康检查", test_health_check),
        ("构建结果Webhook", test_webhook_build_result),
        ("获取构建记录", test_get_builds),
        # ("上报AI分析结果", test_analysis_result),
        # ("查询AI分析记录", test_get_analysis_results),
        # ("API文档访问", test_api_docs),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))

    # 输出测试结果总结
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print("=" * 50)

    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name:<15} {status}")

    success_count = sum(1 for _, success in results if success)
    total_count = len(results)

    print(f"\n📊 总计: {success_count}/{total_count} 测试通过")

    if success_count == total_count:
        print("\n🎉 所有测试通过! API功能正常!")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息")
        print(f"💡 提示: 确保MySQL数据库已启动并正确配置")


if __name__ == "__main__":
    main()
