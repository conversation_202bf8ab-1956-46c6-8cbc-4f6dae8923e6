from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship
from db.database import Base
from datetime import datetime


class BuildRecord(Base):
    """构建记录表"""

    __tablename__ = "build_records"

    # 主键
    BuildRecordID = Column(Integer, primary_key=True, index=True)

    # 构建相关信息
    ItemId = Column(Integer, nullable=False)
    BuildTrigger = Column(Integer, nullable=False, comment="构建触发方式")
    BuildType = Column(Integer, nullable=False, comment="构建类型")
    BuildLang = Column(String(50), nullable=False, comment="构建语言")
    BuildVars = Column(Text, comment="构建变量")

    # 关联ID
    AppID = Column(Integer, nullable=False)
    ServiceID = Column(Integer, nullable=False)
    PackageID = Column(Integer, nullable=True)

    # 构建状态和结果
    Status = Column(
        Integer,
        nullable=False,
        comment="构建状态: 0-未开始, 1-开始调度, 2-进行中, 3-构建成功, 4-构建失败 5-构建取消 6-构建超时",
    )

    # 代码仓库信息
    Repository = Column(String(500), nullable=False, comment="代码仓库地址")
    Branch = Column(String(100), nullable=False, comment="分支名称")
    Commit = Column(String(100), nullable=False, comment="提交哈希")

    # 时间信息
    StartTime = Column(BigInteger, nullable=False, comment="开始时间(毫秒时间戳)")
    EndTime = Column(BigInteger, nullable=True, comment="结束时间(毫秒时间戳)")

    # 用户信息
    Email = Column(String(100), nullable=False, comment="提交者邮箱")

    # 构建产物
    ArtifactName = Column(String(200), comment="构建产物名称")
    ArtifactMd5 = Column(String(100), comment="构建产物MD5")
    ArtifactUrl = Column(String(500), comment="构建产物下载地址")
    BuildLogUrl = Column(String(500), comment="构建日志下载地址")
    CreatedAt = Column(DateTime, default=datetime.utcnow, nullable=False)

    # 关联的分析记录
    analysis_records = relationship("BuildAnalysis", back_populates="build_record")


class BuildAnalysis(Base):
    """构建分析结果表"""

    __tablename__ = "build_analysis"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True)

    # 关联的构建记录ID
    buildRecordId = Column(
        Integer, ForeignKey("build_records.BuildRecordID"), nullable=False, index=True
    )

    # 分析状态：1-正在分析，2-分析结束
    analysisStatus = Column(Integer, nullable=False, default=1)

    # AI提供商和模型
    provider = Column(
        String(50), nullable=False, comment="AI提供商: claude, gemini, openai等"
    )
    model = Column(String(100), nullable=False, comment="AI模型")

    # 分析内容
    context = Column(Text, nullable=False, comment="分析上下文")
    analysisResult = Column(Text, nullable=False, comment="分析结果")

    # 时间戳
    createdAt = Column(DateTime, default=datetime.utcnow, nullable=False)
    updatedAt = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # 关联的构建记录
    build_record = relationship("BuildRecord", back_populates="analysis_records")
