
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { BarChart3, Users, Database, Shield } from 'lucide-react';

const HomePage = () => {
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Azure 管理后台</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="flex items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">构建数量</p>
              <p className="text-2xl font-bold">128</p>
            </div>
            <div className="p-2 bg-primary/10 rounded-full">
              <BarChart3 className="h-6 w-6 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">活跃用户</p>
              <p className="text-2xl font-bold">28</p>
            </div>
            <div className="p-2 bg-blue-500/10 rounded-full">
              <Users className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">应用数量</p>
              <p className="text-2xl font-bold">12</p>
            </div>
            <div className="p-2 bg-green-500/10 rounded-full">
              <Database className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center justify-between p-6">
            <div>
              <p className="text-sm font-medium text-muted-foreground">安全状态</p>
              <p className="text-2xl font-bold">良好</p>
            </div>
            <div className="p-2 bg-yellow-500/10 rounded-full">
              <Shield className="h-6 w-6 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>最近构建</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center justify-between border-b pb-2">
                  <div>
                    <p className="font-medium">构建 #{94233 - i}</p>
                    <p className="text-sm text-muted-foreground">repository-{i}.git (main)</p>
                  </div>
                  <div className="text-right">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      i === 1 ? 'bg-green-100 text-green-800' : 
                      i === 2 ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-red-100 text-red-800'
                    }`}>
                      {i === 1 ? '成功' : i === 2 ? '构建错误' : '失败'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card className="col-span-1">
          <CardHeader>
            <CardTitle>最近活动</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="mr-4 mt-1">
                  <span className="flex h-2 w-2 rounded-full bg-blue-500"></span>
                </div>
                <div>
                  <p className="font-medium">系统更新</p>
                  <p className="text-sm text-muted-foreground">系统已更新至最新版本</p>
                  <p className="text-xs text-muted-foreground">10分钟前</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="mr-4 mt-1">
                  <span className="flex h-2 w-2 rounded-full bg-green-500"></span>
                </div>
                <div>
                  <p className="font-medium">新用户注册</p>
                  <p className="text-sm text-muted-foreground">用户"张三"已成功注册</p>
                  <p className="text-xs text-muted-foreground">2小时前</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="mr-4 mt-1">
                  <span className="flex h-2 w-2 rounded-full bg-yellow-500"></span>
                </div>
                <div>
                  <p className="font-medium">应用发布</p>
                  <p className="text-sm text-muted-foreground">应用"监控平台"已发布新版本</p>
                  <p className="text-xs text-muted-foreground">1天前</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default HomePage;