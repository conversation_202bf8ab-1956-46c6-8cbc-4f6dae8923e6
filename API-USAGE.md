# BuildAnalysis API 使用说明

## 概述
BuildAnalysis API 用于保存和查询AI分析结果，支持多种AI提供商（<PERSON>、Gemini、OpenAI等）的分析结果存储。

## 数据表结构

### BuildAnalysis 表字段
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Int | 自动 | 自增长主键 |
| buildRecordId | Int | 是 | 所属构建ID |
| analysisStatus | Int | 是 | 分析状态：1-正在分析，2-分析结束 |
| provider | String | 是 | AI提供商：claude, gemini, openai等 |
| model | String | 是 | AI模型：claude-3-5-sonnet, gemini-pro等 |
| context | String | 是 | 分析上下文 |
| analysisResult | String | 是 | 分析结果 |
| createdAt | DateTime | 自动 | 创建时间 |
| updatedAt | DateTime | 自动 | 更新时间 |

## API 接口

### 1. 上报分析结果

**POST** `/api/builds/analysis-result`

#### 请求体示例：
```json
{
  "buildRecordId": 94233,
  "provider": "claude",
  "model": "claude-3-5-sonnet-20241022",
  "context": "构建失败，需要分析错误日志",
  "analysisResult": "## Claude AI 分析结果\n\n### 错误分析\n构建过程中发现以下问题...",
  "analysisStatus": 2
}
```

#### 响应示例：
```json
{
  "success": true,
  "message": "分析结果保存成功",
  "data": {
    "id": "4",
    "buildRecordId": 94233,
    "analysisStatus": 2,
    "provider": "claude",
    "model": "claude-3-5-sonnet-20241022",
    "context": "构建失败，需要分析错误日志",
    "analysisResult": "## Claude AI 分析结果...",
    "createdAt": "2025-06-27T13:18:40.237Z",
    "updatedAt": "2025-06-27T13:18:40.237Z"
  }
}
```

### 2. 查询分析记录

**GET** `/api/builds/analysis-result?buildRecordId={buildRecordId}`

#### 响应示例：
```json
{
  "success": true,
  "data": [
    {
      "id": "6",
      "buildRecordId": 94233,
      "analysisStatus": 2,
      "provider": "openai",
      "model": "gpt-4o-mini",
      "context": "编译错误分析",
      "analysisResult": "## OpenAI GPT-4 分析结果...",
      "createdAt": "2025-06-27T13:18:40.302Z",
      "updatedAt": "2025-06-27T13:18:40.302Z",
      "buildResult": {
        "Repository": "http://example.com/repo.git",
        "Branch": "develop",
        "Status": 4,
        "Email": "<EMAIL>"
      }
    }
  ],
  "total": 6
}
```

## 字段说明

### analysisStatus 状态值
- `1`: 正在分析
- `2`: 分析结束

### 支持的 AI 提供商
- `claude`: Claude AI
- `gemini`: Google Gemini
- `openai`: OpenAI GPT
- `其他`: 可扩展支持更多AI提供商

### 支持的模型示例
- Claude: `claude-3-5-sonnet-20241022`, `claude-3-haiku`
- Gemini: `gemini-1.5-pro`, `gemini-pro`
- OpenAI: `gpt-4o-mini`, `gpt-4`, `gpt-3.5-turbo`

## 错误处理

### 常见错误响应
```json
{
  "error": "参数错误",
  "message": "buildRecordId, provider, model, context, analysisResult 字段不能为空"
}
```

```json
{
  "error": "构建记录不存在",
  "message": "构建ID 12345 不存在"
}
```

## 使用建议

1. **字符限制**: context 和 analysisResult 字段支持大文本存储（SQLite TEXT类型）
2. **数据完整性**: 确保 buildRecordId 对应的构建记录存在
3. **并发处理**: 同一构建可以有多个分析记录（不同AI提供商）
4. **状态管理**: 建议先创建状态为"正在分析"的记录，完成后更新为"分析结束"

## 示例代码

### JavaScript/Node.js
```javascript
// 上报分析结果
async function saveAnalysisResult(buildRecordId, provider, model, context, result) {
  const response = await fetch('/api/builds/analysis-result', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      buildRecordId,
      provider,
      model,
      context,
      analysisResult: result,
      analysisStatus: 2
    })
  });
  return response.json();
}

// 查询分析记录
async function getAnalysisResults(buildRecordId) {
  const response = await fetch(`/api/builds/analysis-result?buildRecordId=${buildRecordId}`);
  return response.json();
}
``` 