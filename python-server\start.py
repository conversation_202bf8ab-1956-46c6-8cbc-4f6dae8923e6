#!/usr/bin/env python3
"""
服务器启动脚本
提供更可靠的进程管理和信号处理
"""

import uvicorn
import signal
import sys
import os
import logging
import time
import atexit
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# PID文件路径
PID_FILE = Path("server.pid")

class ServerManager:
    def __init__(self):
        self.server = None
        self.running = False
    
    def write_pid(self):
        """写入PID文件"""
        try:
            with open(PID_FILE, 'w') as f:
                f.write(str(os.getpid()))
            logger.info(f"📝 PID文件已创建: {PID_FILE}")
        except Exception as e:
            logger.error(f"❌ 创建PID文件失败: {e}")
    
    def remove_pid(self):
        """删除PID文件"""
        try:
            if PID_FILE.exists():
                PID_FILE.unlink()
                logger.info("🗑️ PID文件已删除")
        except Exception as e:
            logger.error(f"❌ 删除PID文件失败: {e}")
    
    def signal_handler(self, sig, frame):
        """信号处理器"""
        logger.info(f"📡 接收到信号 {sig}，正在关闭服务器...")
        self.shutdown()
    
    def shutdown(self):
        """关闭服务器"""
        if self.running:
            self.running = False
            logger.info("🔄 正在关闭服务器...")
            
            # 关闭数据库连接
            try:
                from db.database import engine
                engine.dispose()
                logger.info("✅ 数据库连接已关闭")
            except Exception as e:
                logger.error(f"❌ 关闭数据库连接时出错: {e}")
            
            # 删除PID文件
            self.remove_pid()
            
            logger.info("👋 服务器已停止")
            sys.exit(0)
    
    def start(self):
        """启动服务器"""
        try:
            # 检查是否已有实例运行
            if PID_FILE.exists():
                with open(PID_FILE, 'r') as f:
                    old_pid = int(f.read().strip())
                
                # 检查进程是否还在运行
                try:
                    os.kill(old_pid, 0)  # 发送信号0检查进程是否存在
                    logger.error(f"❌ 服务器已在运行 (PID: {old_pid})")
                    logger.info("💡 使用 'python stop.py' 停止现有服务器")
                    sys.exit(1)
                except OSError:
                    # 进程不存在，删除旧的PID文件
                    logger.info("🔄 清理旧的PID文件")
                    self.remove_pid()
            
            # 写入当前PID
            self.write_pid()
            
            # 注册退出处理
            atexit.register(self.remove_pid)
            
            # 注册信号处理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            
            # Windows系统特殊处理
            if os.name == 'nt':
                signal.signal(signal.SIGBREAK, self.signal_handler)
            
            self.running = True
            
            logger.info("🚀 启动服务器...")
            logger.info("💡 使用 Ctrl+C 或 'python stop.py' 停止服务器")
            
            # 启动uvicorn服务器
            config = uvicorn.Config(
                "main:app",
                host="127.0.0.1",
                port=3001,
                reload=True,
                log_level="info",
                access_log=False,
                use_colors=True,
                reload_dirs=["./"],
                reload_excludes=["*.pid", "*.log", "__pycache__", "*.db"]
            )
            
            server = uvicorn.Server(config)
            server.run()
            
        except KeyboardInterrupt:
            logger.info("📡 接收到键盘中断信号")
            self.shutdown()
        except Exception as e:
            logger.error(f"❌ 服务器启动失败: {e}")
            self.remove_pid()
            sys.exit(1)

def main():
    """主函数"""
    print("🚀 Build Analysis API Server")
    print("=" * 50)
    
    server_manager = ServerManager()
    server_manager.start()

if __name__ == "__main__":
    main() 