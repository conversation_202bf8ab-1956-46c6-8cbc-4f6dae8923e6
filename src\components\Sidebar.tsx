import { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronDown, ChevronRight, Menu, Settings, BarChart3, FileText, Database } from 'lucide-react';

interface MenuItem {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  path?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: 'dashboard',
    title: '仪表盘',
    icon: BarChart3,
    path: '/'
  },
  // {
  //   id: 'system',
  //   title: '系统设置',
  //   icon: Settings,
  //   children: [
  //     { id: 'user-management', title: '用户管理', icon: Users, path: '/system/users' },
  //     { id: 'app-management', title: '应用管理', icon: Database, path: '/system/apps' },
  //     { id: 'service-management', title: '服务管理', icon: Shield, path: '/system/services' }
  //   ]
  // },
  {
    id: 'publish',
    title: '项目管理',
    icon: FileText,
    children: [
      { id: 'publish-service', title: '构建记录', icon: FileText, path: '/system/builds' },
      { id: 'task-service', title: '崩溃记录', icon: BarChart3, path: '' },
     ]
  },
  {
    id: 'tools',
    title: 'Agent管理',
    icon: Settings,
    children: [
      { id: 'package-management', title: 'LLM管理', icon: Database, path: '/tools/packages' },
      { id: 'publish-stats', title: 'Agents', icon: BarChart3, path: '/tools/stats' }
    ]
  }
];

interface SidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

export const Sidebar = ({ collapsed, onToggle }: SidebarProps) => {
  const [expandedItems, setExpandedItems] = useState<string[]>(['publish', 'tools']);
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.children && item.children.length > 0) {
      toggleExpanded(item.id);
    } else if (item.path) {
      navigate(item.path);
    }
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const isActive = pathname === item.path;
    const Icon = item.icon;

    return (
      <div key={item.id} className="mb-1">
        <div
          className={`
            flex items-center px-3 py-2 text-sm rounded-lg cursor-pointer
            transition-all duration-200 hover:bg-blue-700/50
            ${level > 0 ? 'ml-4 text-blue-200' : 'text-blue-100'}
            ${collapsed && level === 0 ? 'justify-center' : ''}
            ${isActive ? 'bg-blue-700 text-white' : ''}
          `}
          onClick={() => handleItemClick(item)}
        >
          <Icon className={`${collapsed && level === 0 ? 'w-5 h-5' : 'w-4 h-4 mr-3'} flex-shrink-0`} />
          {(!collapsed || level > 0) && (
            <>
              <span className="flex-1">{item.title}</span>
              {hasChildren && (
                isExpanded ? 
                <ChevronDown className="w-4 h-4" /> : 
                <ChevronRight className="w-4 h-4" />
              )}
            </>
          )}
        </div>
        
        {hasChildren && isExpanded && (!collapsed || level > 0) && (
          <div className="mt-1">
            {item.children?.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`
      bg-slate-800 text-white transition-all duration-300 flex-shrink-0
      ${collapsed ? 'w-16' : 'w-64'}
    `}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-6">
          {!collapsed && (
            <h1 className="text-lg font-semibold text-blue-100">管理后台</h1>
          )}
          <button
            onClick={onToggle}
            className="p-1 rounded-lg hover:bg-blue-700/50 transition-colors"
          >
            <Menu className="w-5 h-5" />
          </button>
        </div>
        
        <nav className="space-y-2">
          {menuItems.map(item => renderMenuItem(item))}
        </nav>
      </div>
    </div>
  );
}; 