// 获取构建记录
const BASE_URL = 'http://localhost:3001';

async function getBuilds() {
  try {
    console.log('🔍 正在获取构建记录...');
    const response = await fetch(`${BASE_URL}/api/builds`);
    const result = await response.json();
    
    if (response.ok && result.data && result.data.length > 0) {
      console.log(`✅ 找到 ${result.total} 条构建记录`);
      console.log('前3条记录的BuildRecordID:', result.data.slice(0, 3).map(r => r.BuildRecordID));
      return result.data[0].BuildRecordID; // 返回第一个构建ID
    } else {
      console.log('❌ 没有找到构建记录');
      return null;
    }
  } catch (error) {
    console.error('❌ 获取构建记录失败:', error.message);
    return null;
  }
}

async function testWithRealBuildId() {
  const buildId = await getBuilds();
  
  if (!buildId) {
    console.log('⚠️ 无法获取有效的构建ID，无法继续测试');
    return;
  }

  // 使用真实的构建ID进行测试
  const testData = {
    buildRecordId: buildId,
    context: '构建失败，错误信息：npm install 失败，依赖包不存在',
    analysisResult: `## AI分析结果

### 问题分析
构建失败的主要原因是npm install步骤出现错误。

### 可能原因
1. package.json中指定的依赖版本不存在
2. npm registry访问问题
3. 网络连接问题

### 解决建议
1. 检查package.json中的依赖版本
2. 尝试使用npm cache clean清理缓存
3. 检查网络连接和npm registry配置

### 预计修复时间
约10-30分钟`,
    analysisStatus: 2 // 分析完成
  };

  try {
    console.log(`\n📤 使用构建ID ${buildId} 测试上报分析结果...`);
    const response = await fetch(`${BASE_URL}/api/builds/analysis-result`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();
    console.log('响应状态:', response.status);
    console.log('响应内容:', JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log('✅ 分析结果上报成功!');
    } else {
      console.log('❌ 分析结果上报失败');
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  }
}

// 运行测试
testWithRealBuildId(); 