// 测试查询分析记录
const BASE_URL = 'http://localhost:3001';

async function testQueryAnalysis() {
  const buildRecordId = 94233; // 使用刚才测试的构建ID
  
  try {
    console.log(`🔍 查询构建ID ${buildRecordId} 的分析记录...`);
    
    const response = await fetch(`${BASE_URL}/api/builds/analysis-result?buildRecordId=${buildRecordId}`);
    const result = await response.json();
    
    console.log('查询响应状态:', response.status);
    console.log('查询响应内容:', JSON.stringify(result, null, 2));
    
    if (response.ok && result.success) {
      console.log(`✅ 查询成功！找到 ${result.total} 条分析记录`);
      
      if (result.data && result.data.length > 0) {
        const record = result.data[0];
        console.log(`📋 最新分析记录概要:`);
        console.log(`   - ID: ${record.id}`);
        console.log(`   - 状态: ${record.analysisStatus === 1 ? '正在分析' : '分析完成'}`);
        console.log(`   - 创建时间: ${new Date(record.createdAt).toLocaleString()}`);
        console.log(`   - 上下文长度: ${record.context.length} 字符`);
        console.log(`   - 分析结果长度: ${record.analysisResult.length} 字符`);
      }
    } else {
      console.log('❌ 查询失败');
    }
    
  } catch (error) {
    console.error('❌ 查询过程中出错:', error.message);
  }
}

// 运行测试
testQueryAnalysis(); 