export interface BuildResult {
  id: number;
  BuildRecordID: number;
  ItemId: number;
  BuildTrigger: number;
  BuildType: number;
  BuildLang: string;
  BuildVars: string;
  AppID: number;
  ServiceID: number;
  Status: number;
  Repository: string;
  Branch: string;
  Commit: string;
  StartTime: string;
  EndTime: string;
  Email: string;
  ArtifactName: string;
  ArtifactMd5: string;
  ArtifactUrl: string;
  BuildLogUrl: string | null;
  PackageID: number | null;
  createdAt: Date;
} 

export interface BuildAnalysis {
  id: number;
  buildRecordId: number; // 所属构建ID
  analysisStatus: number; // 当前分析状态: 1-正在分析, 2-分析结束
  provider: string; // AI提供商: claude, gemini, openai等
  model: string; // AI模型: claude-3-5-sonnet, gemini-pro等
  context: string; // 上下文
  analysisResult: string; // 分析结果
  createdAt: Date;
  updatedAt: Date;
}

// 分析状态枚举
export enum AnalysisStatus {
  ANALYZING = 1, // 正在分析
  COMPLETED = 2  // 分析结束
} 