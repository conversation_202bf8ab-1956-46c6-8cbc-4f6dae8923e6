/**
 * 测试前端 AI 分析 API 调用
 * 这个文件用于测试 buildsApi.requestAnalysis 方法
 */

import { buildsApi } from '@/lib/api';

// 测试函数
export async function testAnalysisAPI() {
  console.log('🧪 开始测试 AI 分析 API...');
  
  try {
    // 测试 Claude 分析
    console.log('📡 测试 Claude 分析...');
    const claudeResult = await buildsApi.requestAnalysis(94233, 'claude');
    console.log('✅ Claude 分析结果:', claudeResult);
    
    // 测试 Gemini 分析
    console.log('📡 测试 Gemini 分析...');
    const geminiResult = await buildsApi.requestAnalysis(94233, 'gemini');
    console.log('✅ Gemini 分析结果:', geminiResult);
    
    return {
      claude: claudeResult,
      gemini: geminiResult
    };
    
  } catch (error) {
    console.error('❌ API 测试失败:', error);
    throw error;
  }
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  (window as any).testAnalysisAPI = testAnalysisAPI;
  console.log('💡 在控制台中运行 testAnalysisAPI() 来测试 API');
}
