@echo off
echo ==========================================
echo      Build Analysis API 启动脚本
echo ==========================================
echo.

echo 检查 Python 环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到 Python 环境，请先安装 Python 3.8+
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 检查配置文件...
if not exist .env (
    echo 警告: 未找到 .env 配置文件
    echo 请复制 config.example 为 .env 并配置数据库连接
    echo.
    echo 使用默认配置启动...
)

echo.
echo 启动 API 服务器...
echo 服务地址: http://localhost:3001
echo API 文档: http://localhost:3001/docs
echo 按 Ctrl+C 停止服务
echo.

python main.py 