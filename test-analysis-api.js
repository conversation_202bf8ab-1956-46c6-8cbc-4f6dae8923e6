// 测试分析结果上报API
const BASE_URL = 'http://localhost:3001';

async function testAnalysisResultAPI() {
  console.log('🧪 开始测试分析结果上报API...\n');

  // 测试数据
  const testData = {
    buildRecordId: 1, // 假设存在构建ID为1的记录
    context: '构建失败，错误信息：npm install 失败，依赖包不存在',
    analysisResult: `## AI分析结果

### 问题分析
构建失败的主要原因是npm install步骤出现错误。

### 可能原因
1. package.json中指定的依赖版本不存在
2. npm registry访问问题
3. 网络连接问题

### 解决建议
1. 检查package.json中的依赖版本
2. 尝试使用npm cache clean清理缓存
3. 检查网络连接和npm registry配置

### 预计修复时间
约10-30分钟`,
    analysisStatus: 2 // 分析完成
  };

  try {
    // 1. 测试POST - 上报分析结果
    console.log('📤 测试上报分析结果...');
    const postResponse = await fetch(`${BASE_URL}/api/builds/analysis-result`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const postResult = await postResponse.json();
    console.log('POST 响应状态:', postResponse.status);
    console.log('POST 响应内容:', JSON.stringify(postResult, null, 2));

    if (postResponse.ok) {
      console.log('✅ 分析结果上报成功!\n');
      
      // 2. 测试GET - 查询分析记录
      console.log('📥 测试查询分析记录...');
      const getResponse = await fetch(`${BASE_URL}/api/builds/analysis-result?buildRecordId=${testData.buildRecordId}`);
      const getResult = await getResponse.json();
      
      console.log('GET 响应状态:', getResponse.status);
      console.log('GET 响应内容:', JSON.stringify(getResult, null, 2));
      
      if (getResponse.ok) {
        console.log('✅ 分析记录查询成功!');
        console.log(`📊 找到 ${getResult.total} 条分析记录`);
      } else {
        console.log('❌ 分析记录查询失败');
      }
    } else {
      console.log('❌ 分析结果上报失败');
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  }

  // 3. 测试错误情况
  console.log('\n🚨 测试错误情况...');
  
  try {
    // 测试缺少必需字段
    const invalidData = { buildRecordId: 1 }; // 缺少context和analysisResult
    const errorResponse = await fetch(`${BASE_URL}/api/builds/analysis-result`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidData),
    });
    
    const errorResult = await errorResponse.json();
    console.log('错误测试响应状态:', errorResponse.status);
    console.log('错误测试响应内容:', JSON.stringify(errorResult, null, 2));
    
    if (errorResponse.status === 400) {
      console.log('✅ 参数验证正常工作');
    } else {
      console.log('⚠️ 参数验证可能有问题');
    }
    
  } catch (error) {
    console.error('❌ 错误测试过程中出错:', error.message);
  }

  console.log('\n🎯 API测试完成!');
}

// 运行测试
testAnalysisResultAPI(); 