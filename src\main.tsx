import React from 'react';
import ReactDOM from 'react-dom/client';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import './styles/globals.css';
import AdminLayout from './components/AdminLayout';
import HomePage from './pages/home';
import AppsPage from './pages/system/apps';
import BuildsPage from './pages/system/builds';
import UsersPage from './pages/system/users';
import { ReactQueryProvider } from './providers/react-query-provider';
import { ThemeProvider } from './providers/theme-provider';

const router = createBrowserRouter([
  {
    path: '/',
    element: <AdminLayout />,
    children: [
      {
        index: true,
        element: <HomePage />,
      },
      {
        path: 'system/apps',
        element: <AppsPage />,
      },
      {
        path: 'system/builds',
        element: <BuildsPage />,
      },
      {
        path: 'system/users',
        element: <UsersPage />,
      },
    ],
  },
]);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
      <ReactQueryProvider>
        <RouterProvider router={router} />
      </ReactQueryProvider>
    </ThemeProvider>
  </React.StrictMode>
);
