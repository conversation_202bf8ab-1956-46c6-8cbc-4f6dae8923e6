from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from typing import Optional
import httpx
import asyncio
import logging

from db.database import get_db
from db.models import BuildRecord
from schemas import BuildsListResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/builds", tags=["构建记录"])

@router.get("", response_model=BuildsListResponse)
async def get_builds(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(10, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db)
):
    """获取构建记录列表"""
    try:
        # 计算偏移量
        offset = (page - 1) * limit
        
        # 查询总数
        total = db.query(func.count(BuildRecord.BuildRecordID)).scalar()
        
        # 查询分页数据，按构建ID降序排列
        builds = db.query(BuildRecord)\
                   .order_by(desc(BuildRecord.BuildRecordID))\
                   .offset(offset)\
                   .limit(limit)\
                   .all()
        
        return BuildsListResponse(
            data=builds,
            total=total,
            page=page,
            limit=limit
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询构建记录失败: {str(e)}")

@router.get("/logs")
async def get_build_logs(
    buildRecordId: int = Query(..., description="构建记录ID"),
    path: str = Query(..., description="日志文件路径"),
):
    """获取构建日志代理接口"""
    try:
        logger.info(f"🔍 获取构建日志: buildRecordId={buildRecordId}, path={path}")
        
        # 构建完整的日志URL
        log_url = f"http://freyja.baymax.mini1.cn/{path}"
        logger.info(f"📡 请求日志URL: {log_url}")
        
        # 使用httpx异步请求外部日志服务
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(log_url)
            
            if response.status_code != 200:
                logger.error(f"❌ 日志服务响应错误: {response.status_code} {response.reason_phrase}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"日志服务响应错误: {response.status_code} {response.reason_phrase}"
                )
            
            log_content = response.text
            logger.info(f"✅ 成功获取日志，长度: {len(log_content)} 字符")
        
        # 处理日志内容，按行分析
        log_lines = log_content.split('\n')
        processed_lines = []
        line_num = 1
        
        # 定义错误关键词
        error_keywords = [
            'error', 'failed', 'failure', 'exception', 'fatal', 'panic',
            'abort', 'crash', 'timeout', 'denied', 'forbidden', 'rejected',
            'invalid', 'missing', 'corrupt', 'broken', '错误', '失败', '异常',
            'npm ERR!', 'yarn error', 'build failed', 'compilation error',
            'permission denied', 'no such file', 'command not found'
        ]
        
        for line in log_lines:
            # 检测错误行
            is_error_line = any(
                keyword.lower() in line.lower() 
                for keyword in error_keywords
            )
            
            processed_lines.append({
                "lineNumber": line_num,
                "content": line,
                "isError": is_error_line,
            })
            
            line_num += 1
        
        # 统计错误行数
        error_lines_count = sum(1 for line in processed_lines if line["isError"])
        
        logger.info(f"📊 日志处理完成: 总行数={len(processed_lines)}, 错误行数={error_lines_count}")
        
        return {
            "success": True,
            "buildRecordId": buildRecordId,
            "logUrl": log_url,
            "totalLines": len(processed_lines),
            "errorLinesCount": error_lines_count,
            "content": log_content,
            "processedLines": processed_lines,
        }
        
    except httpx.TimeoutException:
        logger.error("⏰ 请求日志服务超时")
        raise HTTPException(status_code=504, detail="请求日志服务超时")
    
    except httpx.RequestError as e:
        logger.error(f"🌐 网络请求错误: {str(e)}")
        raise HTTPException(status_code=502, detail=f"网络请求错误: {str(e)}")
    
    except Exception as e:
        logger.error(f"❌ 获取构建日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取构建日志失败: {str(e)}") 