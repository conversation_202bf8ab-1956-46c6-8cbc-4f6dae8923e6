# Build Analysis API

基于 Python + FastAPI + MySQL 的构建记录和AI分析结果管理API。

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- MySQL 5.7+
- pip

### 2. 安装依赖

```bash
cd python-server
pip install -r requirements.txt
```

### 3. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE soc_agnets_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 复制配置文件：
```bash
cp config.example .env
```

3. 修改 `.env` 文件中的数据库连接信息：
```env
DATABASE_URL=mysql+pymysql://root:password@localhost:13306/soc_agnets_db
```

### 4. 启动服务

```bash
python main.py
```

或使用 uvicorn：
```bash
uvicorn main:app --host 0.0.0.0 --port 3001 --reload
```

API将在 http://localhost:3001 启动

## 📡 API接口

### 接口列表

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/builds` | 获取构建记录列表 |
| POST | `/api/webhook/build_result` | 接收构建结果Webhook |
| POST | `/api/builds/analysis-result` | 上报AI分析结果 |
| GET | `/api/builds/analysis-result` | 查询AI分析记录 |
| GET | `/health` | 健康检查 |
| GET | `/docs` | API文档 (Swagger UI) |

### 接口详情

#### 1. 获取构建记录列表
```http
GET /api/builds?page=1&limit=10
```

响应：
```json
{
  "data": [
    {
      "BuildRecordID": 94233,
      "Repository": "https://github.com/example/repo.git",
      "Branch": "main",
      "Status": 2,
      "Email": "<EMAIL>",
      ...
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 10
}
```

#### 2. 接收构建结果
```http
POST /api/webhook/build_result
Content-Type: application/json

{
  "BuildRecordID": 94233,
  "ItemId": 2001,
  "BuildTrigger": 1,
  "BuildType": 2,
  "BuildLang": "nodejs",
  "Status": 4,
  "Repository": "https://github.com/example/repo.git",
  "Branch": "main",
  "Commit": "abc123",
  "StartTime": 1640995200000,
  "EndTime": 1640995500000,
  "Email": "<EMAIL>",
  ...
}
```

#### 3. 上报AI分析结果
```http
POST /api/builds/analysis-result
Content-Type: application/json

{
  "buildRecordId": 94233,
  "provider": "claude",
  "model": "claude-3-5-sonnet-20241022",
  "context": "构建失败，需要分析错误日志",
  "analysisResult": "## Claude AI 分析结果\n\n### 错误分析\n构建过程中发现以下问题...",
  "analysisStatus": 2
}
```

#### 4. 查询AI分析记录
```http
GET /api/builds/analysis-result?buildRecordId=94233
```

## 🗄️ 数据库结构

### build_records 表（构建记录）

| 字段 | 类型 | 说明 |
|------|------|------|
| BuildRecordID | INT | 主键，构建记录ID |
| ItemId | INT | 项目ID |
| BuildTrigger | INT | 构建触发方式 |
| BuildType | INT | 构建类型 |
| BuildLang | VARCHAR(50) | 构建语言 |
| Status | INT | 构建状态：1-进行中, 2-成功, 3-失败, 4-构建错误 |
| Repository | VARCHAR(500) | 代码仓库地址 |
| Branch | VARCHAR(100) | 分支名称 |
| Email | VARCHAR(100) | 提交者邮箱 |
| ... | ... | 其他字段 |

### build_analysis 表（AI分析结果）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 自增主键 |
| buildRecordId | INT | 构建记录ID（外键） |
| analysisStatus | INT | 分析状态：1-正在分析, 2-分析结束 |
| provider | VARCHAR(50) | AI提供商 |
| model | VARCHAR(100) | AI模型 |
| context | TEXT | 分析上下文 |
| analysisResult | TEXT | 分析结果 |
| createdAt | DATETIME | 创建时间 |
| updatedAt | DATETIME | 更新时间 |

## 🔧 开发说明

### 项目结构
```
python-server/
├── main.py              # FastAPI应用入口
├── schemas.py           # Pydantic数据验证模型
├── requirements.txt     # Python依赖
├── config.example       # 配置示例
├── test_api.py         # API测试脚本
├── start.bat           # Windows启动脚本
├── alembic.ini         # 数据库迁移配置
├── db/                 # 数据库模块
│   ├── __init__.py     # 数据库包初始化
│   ├── database.py     # 数据库配置和连接
│   └── models.py       # SQLAlchemy数据库模型
├── route/              # 路由模块
│   ├── __init__.py     # 路由包初始化
│   ├── build_routes.py # 构建记录路由
│   ├── analysis_routes.py # AI分析路由
│   └── webhook_routes.py  # Webhook路由
└── README.md           # 项目说明
```

### 开发环境
1. 启用开发模式（自动重载）：
```bash
python main.py
```

2. 查看API文档：
访问 http://localhost:3001/docs

3. 数据库调试：
在 `db/database.py` 中设置 `echo=True` 可查看SQL执行日志

### 模块说明

#### db/ 数据库模块
- `database.py`: 数据库引擎、会话管理、依赖注入
- `models.py`: SQLAlchemy模型定义

#### route/ 路由模块
- `build_routes.py`: 构建记录相关API
- `analysis_routes.py`: AI分析结果相关API  
- `webhook_routes.py`: Webhook接收API

#### 核心文件
- `main.py`: FastAPI应用配置、中间件、路由注册
- `schemas.py`: Pydantic数据验证和序列化模型

## 🧪 测试

### 健康检查
```bash
curl http://localhost:3001/health
```

### 构建记录API测试
```bash
# 获取构建列表
curl "http://localhost:3001/api/builds?page=1&limit=5"

# 上报构建结果
curl -X POST "http://localhost:3001/api/webhook/build_result" \
  -H "Content-Type: application/json" \
  -d @test-build-data.json
```

## 🔒 生产部署

### 环境变量配置
```env
DATABASE_URL=mysql+pymysql://user:password@prod-db:3306/build_analysis
```

### Docker 部署（可选）
```dockerfile
# Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 3001
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "3001"]
```

## 📝 API兼容性

此API完全兼容现有前端的调用格式：
- 支持原有的字段命名（如 BuildRecordID）
- 保持响应格式与前端期望一致
- 支持分页查询和错误处理

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接字符串
   - 确认数据库用户权限

2. **端口占用**
   - 修改 `main.py` 中的端口号
   - 或杀掉占用3001端口的进程

3. **依赖安装失败**
   - 升级pip: `pip install --upgrade pip`
   - 使用国内镜像: `pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt` 