import asyncio
from mcp_weixin_spider.client import MCPWeixinClient

async def main():
    # 创建客户端
    client = MCPWeixinClient()
    
    try:
        # 连接到MCP服务器
        await client.connect()
        
        # 爬取文章
        result = await client.crawl_article(
            "https://mp.weixin.qq.com/s/example"
        )
        
        print(f"✅ 爬取成功: {result['article']['title']}")
        print(f"📄 内容长度: {result['article']['content_length']}")
        print(f"🖼️ 图片数量: {result['article']['images_count']}")
        
        # 获取最近文章
        recent = await client.get_recent_articles(limit=5)
        print(f"📚 最近文章数量: {len(recent['articles'])}")
        
        # 获取爬虫配置
        config = await client.get_spider_config()
        print(f"⚙️ 当前配置: {config}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())