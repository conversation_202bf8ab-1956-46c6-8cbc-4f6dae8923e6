from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import logging
from dotenv import load_dotenv

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

# 数据库配置
DATABASE_URL = os.getenv("DATABASE_URL")

if not DATABASE_URL:
    logger.warning("⚠️ DATABASE_URL 环境变量未设置")
    # 使用默认的SQLite数据库
    DATABASE_URL = "sqlite:///./build_analysis.db"
    logger.info(f"🔄 使用默认SQLite数据库: {DATABASE_URL}")
else:
    logger.info(f"🔗 使用配置的数据库: {DATABASE_URL[:50]}...")

# 创建数据库引擎
try:
    logger.info("正在创建数据库引擎...")
    
    if DATABASE_URL.startswith("sqlite"):
        # SQLite 配置
        engine = create_engine(
            DATABASE_URL,
            echo=False,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}  # SQLite 需要这个配置
        )
    else:
        # 其他数据库配置
        engine = create_engine(
            DATABASE_URL,
            echo=False,
            pool_pre_ping=True,
            pool_recycle=3600,
        )
    
    logger.info("✅ 数据库连接成功!")
    
except Exception as e:
    logger.error(f"❌ 数据库连接失败: {e}")
    logger.error(f"当前DATABASE_URL: {DATABASE_URL}")
    
    # 如果连接失败，使用本地SQLite作为回退
    logger.warning("🔄 使用本地SQLite数据库作为回退...")
    try:
        DATABASE_URL = "sqlite:///./build_analysis_fallback.db"
        engine = create_engine(
            DATABASE_URL,
            echo=False,
            connect_args={"check_same_thread": False}
        )
        
        # 测试回退数据库连接
        engine.connect()
        
        logger.info(f"✅ 回退数据库连接成功: {DATABASE_URL}")
    except Exception as fallback_error:
        logger.error(f"❌ 回退数据库也连接失败: {fallback_error}")
        raise Exception("所有数据库连接都失败了")

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 基类
Base = declarative_base()

# 依赖注入：获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 数据库信息
def get_database_info():
    """获取数据库信息"""
    return {
        "database_url": DATABASE_URL,
        "engine": str(engine.url),
        "dialect": engine.dialect.name
    } 