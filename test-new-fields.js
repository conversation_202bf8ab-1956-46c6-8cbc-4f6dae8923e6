// 测试新增的 provider 和 model 字段
const BASE_URL = 'http://localhost:3000';

async function testNewFields() {
  console.log('🧪 测试新增的 provider 和 model 字段...\n');

  // 获取一个构建ID用于测试
  try {
    const buildsResponse = await fetch(`${BASE_URL}/api/builds`);
    const buildsResult = await buildsResponse.json();
    
    if (!buildsResult.data || buildsResult.data.length === 0) {
      console.log('❌ 没有找到构建记录，无法测试');
      return;
    }

    const buildId = buildsResult.data[0].BuildRecordID;
    console.log(`📋 使用构建ID: ${buildId}\n`);

    // 测试不同的AI提供商和模型
    const testCases = [
      {
        provider: 'claude',
        model: 'claude-3-5-sonnet-20241022',
        context: 'Claude分析上下文：构建失败，需要分析错误日志',
        analysisResult: '## Claude AI 分析结果\n\n### 错误分析\n构建过程中发现以下问题...\n\n### 解决方案\n建议采取以下措施修复问题...'
      },
      {
        provider: 'gemini',
        model: 'gemini-1.5-pro',
        context: 'Gemini分析上下文：依赖包安装失败',
        analysisResult: '## Gemini AI 分析结果\n\n### 问题诊断\n通过分析构建日志，发现依赖包版本冲突...\n\n### 修复建议\n1. 更新package.json\n2. 清理npm缓存'
      },
      {
        provider: 'openai',
        model: 'gpt-4o-mini',
        context: 'OpenAI分析上下文：编译错误分析',
        analysisResult: '## OpenAI GPT-4 分析结果\n\n### 编译错误分析\n检测到TypeScript类型错误...\n\n### 解决步骤\n1. 修复类型定义\n2. 更新接口声明'
      }
    ];

    console.log('📤 测试不同AI提供商的分析结果上报...\n');

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`${i + 1}. 测试 ${testCase.provider} (${testCase.model})...`);

      try {
        const response = await fetch(`${BASE_URL}/api/builds/analysis-result`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            buildRecordId: buildId,
            provider: testCase.provider,
            model: testCase.model,
            context: testCase.context,
            analysisResult: testCase.analysisResult,
            analysisStatus: 2 // 分析完成
          }),
        });

        const result = await response.json();
        
        if (response.ok) {
          console.log(`   ✅ ${testCase.provider} 分析结果保存成功！ID: ${result.data.id}`);
        } else {
          console.log(`   ❌ ${testCase.provider} 分析结果保存失败:`, result.message);
        }
      } catch (error) {
        console.log(`   ❌ ${testCase.provider} 请求失败:`, error.message);
      }
    }

    // 查询分析记录验证新字段
    console.log(`\n🔍 查询构建ID ${buildId} 的所有分析记录...`);
    
    try {
      const queryResponse = await fetch(`${BASE_URL}/api/builds/analysis-result?buildRecordId=${buildId}`);
      const queryResult = await queryResponse.json();
      
      if (queryResponse.ok && queryResult.success) {
        console.log(`📊 找到 ${queryResult.total} 条分析记录：\n`);
        
        queryResult.data.forEach((record, index) => {
          console.log(`记录 ${index + 1}:`);
          console.log(`  - ID: ${record.id}`);
          console.log(`  - AI提供商: ${record.provider}`);
          console.log(`  - AI模型: ${record.model}`);
          console.log(`  - 状态: ${record.analysisStatus === 1 ? '正在分析' : '分析完成'}`);
          console.log(`  - 创建时间: ${new Date(record.createdAt).toLocaleString()}`);
          console.log(`  - 上下文: ${record.context.substring(0, 50)}...`);
          console.log('');
        });
        
        // 验证字段完整性
        const hasAllFields = queryResult.data.every(record => 
          record.provider && record.model && 
          record.context && record.analysisResult
        );
        
        if (hasAllFields) {
          console.log('✅ 所有记录都包含完整的字段信息！');
        } else {
          console.log('⚠️ 部分记录缺少字段信息');
        }
        
      } else {
        console.log('❌ 查询分析记录失败');
      }
    } catch (error) {
      console.error('❌ 查询过程中出错:', error.message);
    }

  } catch (error) {
    console.error('❌ 测试过程中出错:', error.message);
  }

  console.log('\n🎯 新字段测试完成！');
}

// 运行测试
testNewFields(); 