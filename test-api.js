// 测试数据
const testBuildData = {
  BuildRecordID: 94233,
  ItemId: 2001,
  BuildTrigger: 1,
  BuildType: 2,
  BuildLang: "nodejs",
  BuildVars: "NODE_ENV=production",
  AppID: 3001,
  ServiceID: 4001,
  Status: 4, // 成功状态
  Repository: "http://yw-gitlab.miniworldplus.com/miniw/soc-game.git",
  Branch: "develop ",
  Commit: "fcd96b111e579247caa082ae20012edf6ba6a0ec",
  StartTime: Date.now() - 300000, // 5分钟前开始
  EndTime: Date.now(), // 现在结束
  Email: "<EMAIL>",
  ArtifactName: "app-v1.0.0.tar.gz",
  ArtifactMd5: "d41d8cd98f00b204e9800998ecf8427e",
  ArtifactUrl: "https://artifacts.example.com/app-v1.0.0.tar.gz",
  PackageID: 5001
};

async function testWebhook() {
  console.log('🧪 测试Webhook接口...');
  try {
    const response = await fetch('http://localhost:3000/api/webhook/build_result', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBuildData)
    });

    const result = await response.json();
    console.log('Webhook响应:', result);
    
    if (response.ok && result.Ret === 0) {
      console.log('✅ Webhook测试成功!');
      return true;
    } else {
      console.log('❌ Webhook测试失败:', result);
      return false;
    }
  } catch (error) {
    console.error('❌ Webhook测试错误:', error.message);
    return false;
  }
}

async function testBuildsAPI() {
  console.log('\n🧪 测试构建记录API...');
  try {
    const response = await fetch('http://localhost:3000/api/builds?page=1&limit=10');
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ 构建记录API测试成功!');
      console.log(`📊 总记录数: ${result.total}`);
      console.log(`📄 当前页数据: ${result.data.length} 条`);
      return true;
    } else {
      console.log('❌ 构建记录API测试失败:', result);
      return false;
    }
  } catch (error) {
    console.error('❌ 构建记录API测试错误:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 开始API测试...\n');
  
  // 等待服务器启动
  console.log('⏳ 等待服务器启动...');
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  const webhookSuccess = await testWebhook();
  const apiSuccess = await testBuildsAPI();
  
  console.log('\n📋 测试结果总结:');
  console.log(`Webhook接口: ${webhookSuccess ? '✅ 通过' : '❌ 失败'}`);
  console.log(`构建记录API: ${apiSuccess ? '✅ 通过' : '❌ 失败'}`);
  
  if (webhookSuccess && apiSuccess) {
    console.log('\n🎉 所有测试通过! 功能正常工作!');
  } else {
    console.log('\n⚠️  部分测试失败，请检查错误信息');
  }
}

runTests(); 