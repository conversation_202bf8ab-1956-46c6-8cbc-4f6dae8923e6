import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/DataTable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import {
  createColumnHelper,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { RefreshCw, Settings, Trash2, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Send, AlertTriangle } from 'lucide-react';
import { buildsApi, type BuildData } from '@/lib/api';
import { useState, useEffect } from 'react';

// 将API数据转换为页面显示格式
const transformBuildData = (apiData: BuildData) => ({
  id: apiData.BuildRecordID,
  repository: apiData.Repository,
  branch: apiData.Branch.trim(),
  commit: apiData.Commit || 'abc123def456',
  status: apiData.Status,
  email: apiData.Email,
  startTime: apiData.StartTime ? new Date(apiData.StartTime).toISOString() : new Date().toISOString(),
  endTime: new Date(apiData.EndTime).toISOString(),
  analysisAvailable: apiData.analysisAvailable || false,
  buildLang: apiData.BuildLang || 'JavaScript',
  buildVars: apiData.BuildVars || '',
  artifactName: apiData.ArtifactName || '',
  artifactMd5: apiData.ArtifactMd5 || '',
  artifactUrl: apiData.ArtifactUrl || '',
  buildLogUrl: apiData.BuildLogUrl || '',
});

type TransformedBuildData = ReturnType<typeof transformBuildData>;

// 构建详情弹窗组件
const BuildDetailDialog = ({ 
  build, 
  isOpen, 
  onClose 
}: { 
  build: TransformedBuildData; 
  isOpen: boolean; 
  onClose: () => void; 
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState({ claude: false, gemini: false });
  const [analysisResults, setAnalysisResults] = useState<{
    claude: string | null;
    gemini: string | null;
  }>({ claude: null, gemini: null });
  const [activeTab, setActiveTab] = useState('logs');
  const [logContent, setLogContent] = useState<string>('');
  const [processedLogLines, setProcessedLogLines] = useState<Array<{
    lineNumber: number;
    content: string;
    isError: boolean;
  }>>([]);
  const [logStats, setLogStats] = useState<{
    totalLines: number;
    errorLinesCount: number;
  }>({ totalLines: 0, errorLinesCount: 0 });
  const [isLoadingLog, setIsLoadingLog] = useState(false);
  const [logError, setLogError] = useState<string | null>(null);

  // 模拟构建日志
  const mockBuildLog = `
[2024-01-15 10:30:00] 开始构建任务 #${build.id}
[2024-01-15 10:30:01] 仓库: ${build.repository}
[2024-01-15 10:30:01] 分支: ${build.branch}
[2024-01-15 10:30:01] 提交: ${build.commit}
[2024-01-15 10:30:02] 构建语言: ${build.buildLang}
[2024-01-15 10:30:02] 构建变量: ${build.buildVars}
[2024-01-15 10:30:03] 
[2024-01-15 10:30:03] > 正在拉取代码...
[2024-01-15 10:30:05] > 代码拉取完成
[2024-01-15 10:30:05] > 开始安装依赖...
[2024-01-15 10:30:15] > npm install 完成
[2024-01-15 10:30:15] > 开始构建...
[2024-01-15 10:30:45] > 构建完成
[2024-01-15 10:30:46] > 正在打包制品...
[2024-01-15 10:30:50] > 制品上传完成: ${build.artifactName}
[2024-01-15 10:30:50] > MD5: ${build.artifactMd5}
[2024-01-15 10:30:50] 构建任务完成！
  `.trim();

  // 获取构建日志
  const fetchBuildLog = async () => {
    setIsLoadingLog(true);
    setLogError(null);
    setProcessedLogLines([]);
    setLogStats({ totalLines: 0, errorLinesCount: 0 });

    try {
      // 如果有BuildLogUrl，使用真实API
      if (build.buildLogUrl) {
        const apiUrl = `http://localhost:3001/api/builds/logs?buildRecordId=${build.id}&path=${encodeURIComponent(build.buildLogUrl)}`;
        const response = await fetch(apiUrl);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.success) {
          setLogContent(result.content || '');
          setProcessedLogLines(result.processedLines || []);
          setLogStats({
            totalLines: result.totalLines || 0,
            errorLinesCount: result.errorLinesCount || 0,
          });
        } else {
          throw new Error(result.error || '获取日志失败');
        }
      } else {
        // 使用模拟日志作为回退
        console.warn('没有BuildLogUrl，使用模拟日志');
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLogContent(mockBuildLog);
        
        // 处理模拟日志
        const lines = mockBuildLog.split('\n');
        const processed = lines.map((line, index) => ({
          lineNumber: index + 1,
          content: line,
          isError: line.toLowerCase().includes('error') || line.toLowerCase().includes('failed')
        }));
        
        setProcessedLogLines(processed);
        setLogStats({
          totalLines: processed.length,
          errorLinesCount: processed.filter(line => line.isError).length,
        });
      }
    } catch (error) {
      console.error('获取构建日志失败:', error);
      setLogError(error instanceof Error ? error.message : '获取日志失败');
      setLogContent('');
      setProcessedLogLines([]);
      setLogStats({ totalLines: 0, errorLinesCount: 0 });
    } finally {
      setIsLoadingLog(false);
    }
  };

  // 当弹窗打开且在日志页面时自动获取日志
  useEffect(() => {
    if (isOpen && activeTab === 'logs') {
      fetchBuildLog();
    }
  }, [isOpen, activeTab]);

  const handleSendToFeishu = async () => {
    const analysisContent = [];
    
    if (analysisResults.claude) {
      analysisContent.push(`## Claude AI 分析结果\n\n${analysisResults.claude}`);
    }
    
    if (analysisResults.gemini) {
      analysisContent.push(`## Gemini AI 分析结果\n\n${analysisResults.gemini}`);
    }
    
    const fullContent = `# 构建失败分析报告\n\n**构建ID**: ${build.id}\n**仓库**: ${build.repository}\n**分支**: ${build.branch}\n**触发者**: ${build.email}\n**失败时间**: ${new Date(build.endTime).toLocaleString()}\n\n---\n\n${analysisContent.join('\n\n---\n\n')}`;
    
    try {
      console.log('发送到飞书的内容:', fullContent);
      
      // 模拟发送过程
      const response = await fetch('/api/send-to-feishu', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          buildId: build.id,
          content: fullContent,
          title: `构建失败分析 - ${build.repository}#${build.branch}`,
        }),
      });
      
      if (response.ok) {
        alert('分析结果已成功发送到飞书！');
      } else {
        alert('发送到飞书失败，请稍后重试');
      }
    } catch (error) {
      console.error('发送到飞书出错:', error);
      alert('发送到飞书出错，请稍后重试');
    }
  };

  const handleAIAnalysis = async (provider: 'claude' | 'gemini') => {
    setIsAnalyzing(prev => ({ ...prev, [provider]: true }));
    
    try {
      if (provider === 'claude') {
        // 构造 BuildData 数据
        const buildData = {
          BuildRecordID: build.id,
          ItemId: 0,
          BuildTrigger: 0,
          BuildType: 0,
          BuildLang: build.buildLang,
          BuildVars: build.buildVars,
          AppID: 0,
          ServiceID: 0,
          Status: build.status,
          Repository: build.repository,
          Branch: build.branch,
          Commit: build.commit,
          StartTime: new Date(build.startTime).getTime(),
          EndTime: new Date(build.endTime).getTime(),
          Email: build.email,
          ArtifactName: build.artifactName,
          ArtifactMd5: build.artifactMd5,
          ArtifactUrl: build.artifactUrl,
          PackageID: null,
        };

        // 通过本地 API 路由发送请求到 AI 服务器
        const response = await fetch('/api/builds/analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(buildData),
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        const analysisText = result.analysis || result.message || '分析完成，但没有返回具体内容';
        
        setAnalysisResults(prev => ({ 
          ...prev, 
          [provider]: analysisText 
        }));
      } else {
        // Gemini 分析模拟
        await new Promise(resolve => setTimeout(resolve, 2000));
        const mockGeminiAnalysis = `## Gemini AI 构建分析报告

### 构建概况
- **构建ID**: ${build.id}
- **状态**: ${build.status === 3 ? '失败' : '成功'}
- **仓库**: ${build.repository}
- **分支**: ${build.branch}

### 问题分析
基于构建日志和配置信息，以下是可能的问题原因：

1. **依赖问题**
   - 检查 package.json 中的依赖版本
   - 可能存在版本冲突

2. **构建环境**
   - Node.js 版本兼容性
   - 构建工具配置

3. **代码质量**
   - 语法检查
   - 类型检查

### 建议解决方案
1. 更新依赖版本
2. 检查构建配置
3. 运行本地测试

这是 Gemini AI 的模拟分析结果。`;
        
        setAnalysisResults(prev => ({ 
          ...prev, 
          [provider]: mockGeminiAnalysis 
        }));
      }
    } catch (error) {
      console.error(`${provider} AI 分析失败:`, error);
      
      const errorMessage = `## ${provider === 'claude' ? 'Claude' : 'Gemini'} AI 分析失败

### 错误信息
${error instanceof Error ? error.message : '未知错误'}

### 可能的原因
- 网络连接问题
- AI 服务器暂时不可用
- 请求数据格式错误

### 建议
- 请稍后重试
- 检查网络连接
- 联系管理员确认 AI 服务状态`;

      setAnalysisResults(prev => ({ 
        ...prev, 
        [provider]: errorMessage 
      }));
    } finally {
      setIsAnalyzing(prev => ({ ...prev, [provider]: false }));
    }
  };

  const getStatusInfo = (status: number) => {
    const statusMap: { [key: number]: { text: string; color: string; bgColor: string } } = {
      1: { text: '进行中', color: 'text-blue-700', bgColor: 'bg-blue-100' },
      2: { text: '构建成功', color: 'text-green-700', bgColor: 'bg-green-100' },
      3: { text: '构建失败', color: 'text-red-700', bgColor: 'bg-red-100' },
      4: { text: '构建错误', color: 'text-yellow-700', bgColor: 'bg-yellow-100' },
    };
    return statusMap[status] || { text: '未知状态', color: 'text-gray-700', bgColor: 'bg-gray-100' };
  };

  const statusInfo = getStatusInfo(build.status);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[1800px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <Bot className="h-6 w-6 text-purple-500" />
            构建详情 & AI 分析
          </DialogTitle>
          <DialogDescription>
            构建ID: {build.id} | 触发者: {build.email}
          </DialogDescription>
        </DialogHeader>

        {/* 构建详情信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          <div>
            <p className="text-sm text-gray-500">状态</p>
            <Badge className={`${statusInfo.bgColor} ${statusInfo.color} border-0`}>
              {statusInfo.text}
            </Badge>
          </div>
          <div>
            <p className="text-sm text-gray-500">仓库</p>
            <p className="text-sm font-medium truncate">{build.repository}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">分支</p>
            <p className="text-sm font-medium">{build.branch}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">语言</p>
            <p className="text-sm font-medium">{build.buildLang}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">开始时间</p>
            <p className="text-sm font-medium">
              {new Date(build.startTime).toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">结束时间</p>
            <p className="text-sm font-medium">
              {new Date(build.endTime).toLocaleString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">构建时长</p>
            <p className="text-sm font-medium">
              {Math.round((new Date(build.endTime).getTime() - new Date(build.startTime).getTime()) / 1000)}秒
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">制品</p>
            <p className="text-sm font-medium truncate">{build.artifactName || '无'}</p>
          </div>
        </div>

        <Separator />

        {/* Tab 组件 */}
        <Tabs defaultValue="logs" className="flex-1 flex flex-col min-h-0" onValueChange={setActiveTab}>
          <TabsList className={`grid w-full ${build.status >= 3 ? 'grid-cols-3' : 'grid-cols-1'}`}>
            <TabsTrigger value="logs">构建日志</TabsTrigger>
            {build.status >= 3 && (
              <>
                <TabsTrigger value="claude">Claude AI 分析</TabsTrigger>
                <TabsTrigger value="gemini">Gemini AI 分析</TabsTrigger>
              </>
            )}
          </TabsList>

          {/* 构建日志 */}
          <TabsContent value="logs" className="flex-1 min-h-0">
            <div className="space-y-4 h-96">
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-2">
                  <h3 className="text-lg font-semibold">构建日志详情</h3>
                  {build.buildLogUrl && (
                    <div className="text-sm text-gray-600">
                      <span className="font-medium">路径: </span>
                      <span className="font-mono">{build.buildLogUrl}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-4 text-sm">
                    <span className="text-gray-600">
                      总行数: <span className="font-medium">{logStats.totalLines}</span>
                    </span>
                    {logStats.errorLinesCount > 0 && (
                      <div className="flex items-center gap-1 text-orange-600">
                        <AlertTriangle className="h-4 w-4" />
                        <span>错误行数: <span className="font-medium">{logStats.errorLinesCount}</span></span>
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchBuildLog}
                    disabled={isLoadingLog}
                  >
                    {isLoadingLog ? '刷新中...' : '刷新日志'}
                  </Button>
                  <Badge variant="outline">实时日志</Badge>
                </div>
              </div>
              <ScrollArea className="h-80 w-full border rounded-md">
                <div className="p-4">
                  {isLoadingLog ? (
                    <div className="flex items-center justify-center h-32 space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                      <span className="text-sm text-gray-500">正在加载日志...</span>
                    </div>
                  ) : logError ? (
                    <div className="flex items-center justify-center h-32 text-red-500">
                      <div className="text-center">
                        <p className="text-sm">❌ {logError}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={fetchBuildLog}
                          className="mt-2"
                        >
                          重试
                        </Button>
                      </div>
                    </div>
                  ) : processedLogLines.length > 0 ? (
                    <div className="text-sm font-mono bg-gray-50 rounded border h-full overflow-y-auto">
                      {processedLogLines.map((line) => (
                        <div
                          key={line.lineNumber}
                          className={`flex hover:bg-gray-100 ${line.isError ? 'bg-red-50 border-l-2 border-l-red-400' : ''}`}
                        >
                          <span className="text-gray-400 select-none w-12 flex-shrink-0 text-right pr-3 py-1 bg-gray-100 border-r">
                            {line.lineNumber}
                          </span>
                          <span className={`flex-1 px-3 py-1 whitespace-pre-wrap ${line.isError ? 'text-red-700' : 'text-gray-800'}`}>
                            {line.content}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : logContent ? (
                    <pre className="text-sm font-mono whitespace-pre-wrap bg-gray-50 p-3 rounded border h-full overflow-y-auto">
                      {logContent}
                    </pre>
                  ) : (
                    <div className="flex items-center justify-center h-32 text-gray-400">
                      <p className="text-sm">暂无日志内容</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>

          {/* Claude AI 分析 */}
          {build.status >= 3 && (
            <TabsContent value="claude" className="flex-1 min-h-0">
              <div className="space-y-4 h-96">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-500" />
                    Claude AI 分析
                  </h3>
                  <Button
                    onClick={() => handleAIAnalysis('claude')}
                    disabled={isAnalyzing.claude}
                    size="sm"
                    variant="outline"
                  >
                    {isAnalyzing.claude ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-500 mr-2"></div>
                        分析中...
                      </>
                    ) : (
                      '开始分析'
                    )}
                  </Button>
                </div>
                
                <ScrollArea className="h-80 w-full border rounded-md">
                  <div className="p-4">
                    {analysisResults.claude ? (
                      <div className="prose prose-sm max-w-none">
                        <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                          {analysisResults.claude}
                        </pre>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-32 text-gray-500">
                        点击"开始分析"获取 Claude AI 的专业建议
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          )}

          {/* Gemini AI 分析 */}
          {build.status >= 3 && (
            <TabsContent value="gemini" className="flex-1 min-h-0">
              <div className="space-y-4 h-96">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-blue-500" />
                    Gemini AI 分析
                  </h3>
                  <Button
                    onClick={() => handleAIAnalysis('gemini')}
                    disabled={isAnalyzing.gemini}
                    size="sm"
                    variant="outline"
                  >
                    {isAnalyzing.gemini ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                        分析中...
                      </>
                    ) : (
                      '开始分析'
                    )}
                  </Button>
                </div>
                
                <ScrollArea className="h-80 w-full border rounded-md">
                  <div className="p-4">
                    {analysisResults.gemini ? (
                      <div className="prose prose-sm max-w-none">
                        <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                          {analysisResults.gemini}
                        </pre>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-32 text-gray-500">
                        点击"开始分析"获取 Gemini AI 的智能洞察
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </div>
            </TabsContent>
          )}
        </Tabs>

        {/* 发送到飞书按钮 - 为所有页面保留空间避免抖动 */}
        <div className="flex justify-end pt-4 border-t min-h-[60px]">
          {build.status >= 3 && (activeTab === 'claude' || activeTab === 'gemini') && (
            <Button
              onClick={handleSendToFeishu}
              className="flex items-center gap-2"
            >
              <Send className="h-4 w-4" />
              发送分析结果到飞书
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

const columnHelper = createColumnHelper<TransformedBuildData>();

const getBuildStatusText = (status: number) => {
  switch (status) {
    case 1: return '进行中';
    case 2: return '成功';
    case 3: return '失败';
    case 4: return '构建错误';
    default: return '未知';
  }
};

const getBuildStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'bg-blue-100 text-blue-800 border-blue-200';
    case 2: return 'bg-green-100 text-green-800 border-green-200';
    case 3: return 'bg-red-100 text-red-800 border-red-200';
    case 4: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    default: return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const columns = [
  columnHelper.accessor('id', {
    header: '构建ID',
    cell: (info) => (
      <span className="font-mono text-sm">{info.getValue()}</span>
    ),
  }),
  columnHelper.accessor('branch', {
    header: '分支',
    cell: (info) => (
      <span className="font-medium text-blue-600">{info.getValue()}</span>
    ),
  }),
  columnHelper.accessor('commit', {
    header: 'Commit',
    cell: (info) => (
      <span className="font-mono text-xs text-gray-600">
        {info.getValue().substring(0, 12)}
      </span>
    ),
  }),
  columnHelper.accessor('status', {
    header: '状态',
    cell: (info) => (
      <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getBuildStatusClass(info.getValue())}`}>
        {getBuildStatusText(info.getValue())}
      </span>
    ),
  }),
  columnHelper.accessor('startTime', {
    header: '开始时间',
    cell: (info) => (
      <span className="text-sm text-gray-600">
        {new Date(info.getValue()).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}
      </span>
    ),
  }),
  columnHelper.accessor('endTime', {
    header: '结束时间',
    cell: (info) => (
      <span className="text-sm text-gray-600">
        {new Date(info.getValue()).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })}
      </span>
    ),
  }),
  columnHelper.accessor('email', {
    header: '触发者',
    cell: (info) => (
      <span className="text-sm text-gray-700">{info.getValue()}</span>
    ),
  }),
  columnHelper.accessor((row) => row, {
    id: 'actions',
    header: '操作',
    cell: (info) => {
      const build = info.getValue();
      const [dialogOpen, setDialogOpen] = useState(false);
      
      const handleAIAnalysis = () => {
        setDialogOpen(true);
      };

      const handleDelete = () => {
        if (confirm('确定要删除这条构建记录吗？')) {
          console.log('删除构建记录:', build.id);
          // TODO: 实现删除功能
        }
      };

      const handleSettings = () => {
        console.log('构建设置:', build.id);
        // TODO: 实现设置功能
      };

      return (
        <div className="flex items-center space-x-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAIAnalysis}
                className="h-8 w-8 p-0 hover:bg-purple-50"
                title="AI分析"
              >
                <Sparkles className="h-4 w-4 text-purple-600" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>AI分析</p>
            </TooltipContent>
          </Tooltip>

          <BuildDetailDialog 
            build={build}
            isOpen={dialogOpen}
            onClose={() => setDialogOpen(false)}
          />

          <Button
            variant="ghost"
            size="sm"
            onClick={handleSettings}
            className="h-8 w-8 p-0 hover:bg-gray-50"
            title="设置"
          >
            <Settings className="h-4 w-4 text-gray-600" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            className="h-8 w-8 p-0 hover:bg-red-50"
            title="删除"
          >
            <Trash2 className="h-4 w-4 text-red-600" />
          </Button>
        </div>
      );
    },
  }),
];

// 使用真实API调用
const fetchBuilds = async () => {
  try {
    const response = await buildsApi.getBuilds({ page: 1, limit: 50 });
    return response.data.map(transformBuildData);
  } catch (error) {
    console.error('获取构建记录失败:', error);
    // 如果API调用失败，返回模拟数据作为备选
    return [
      { 
        id: 94233, 
        repository: 'https://github.com/example/repo1.git', 
        branch: 'develop',
        commit: 'fcd96b11e579247caa082ae20012edf6ba6a0ec',
        status: 3,
        email: '<EMAIL>',
        startTime: '2025-06-27T08:41:53.000Z',
        endTime: '2025-06-27T08:46:53.000Z',
        analysisAvailable: false,
        buildLang: 'JavaScript',
        buildVars: 'NODE_ENV=production',
        artifactName: 'app-v1.2.3.tar.gz',
        artifactMd5: 'abc123def456',
        artifactUrl: '/artifacts/app-v1.2.3.tar.gz',
        buildLogUrl: 'buildlogs/2025-06-27/socgame_prod_1729495265_473.log'
      },
      { 
        id: 1005, 
        repository: 'https://github.com/example/repo2.git', 
        branch: 'main',
        commit: 'abc123def456',
        status: 3,
        email: '<EMAIL>',
        startTime: '2025-06-27T07:46:12.000Z',
        endTime: '2025-06-27T07:51:12.000Z',
        analysisAvailable: true,
        buildLang: 'TypeScript',
        buildVars: 'NODE_ENV=development',
        artifactName: 'webapp-v2.1.0.zip',
        artifactMd5: 'def456ghi789',
        artifactUrl: '/artifacts/webapp-v2.1.0.zip',
        buildLogUrl: 'buildlogs/2025-06-27/webapp_dev_1729495123_456.log'
      },
      { 
        id: 1004, 
        repository: 'https://github.com/example/repo3.git', 
        branch: 'main',
        commit: 'abc123def456',
        status: 3,
        email: '<EMAIL>',
        startTime: '2025-06-27T07:17:37.000Z',
        endTime: '2025-06-27T07:22:37.000Z',
        analysisAvailable: false,
        buildLang: 'Python',
        buildVars: 'ENV=staging',
        artifactName: 'api-service-v1.0.1.tar.gz',
        artifactMd5: 'ghi789jkl012',
        artifactUrl: '/artifacts/api-service-v1.0.1.tar.gz',
        buildLogUrl: 'buildlogs/2025-06-27/api_staging_1729494987_789.log'
      },
      { 
        id: 1003, 
        repository: 'https://github.com/example/repo4.git', 
        branch: 'main',
        commit: 'abc123def456',
        status: 2,
        email: '<EMAIL>',
        startTime: '2025-06-27T06:29:02.000Z',
        endTime: '2025-06-27T06:34:02.000Z',
        analysisAvailable: false,
        buildLang: 'Go',
        buildVars: 'CGO_ENABLED=0',
        artifactName: 'microservice-v3.2.1.tar.gz',
        artifactMd5: 'jkl012mno345',
        artifactUrl: '/artifacts/microservice-v3.2.1.tar.gz',
        buildLogUrl: 'buildlogs/2025-06-27/microservice_prod_1729494542_012.log'
      },
      { 
        id: 1002, 
        repository: 'https://github.com/example/repo5.git', 
        branch: 'main',
        commit: 'abc123def456',
        status: 2,
        email: '<EMAIL>',
        startTime: '2025-06-27T06:26:53.000Z',
        endTime: '2025-06-27T06:31:53.000Z',
        analysisAvailable: false,
        buildLang: 'Java',
        buildVars: 'MAVEN_OPTS=-Xmx2g',
        artifactName: 'backend-api-v1.5.0.jar',
        artifactMd5: 'mno345pqr678',
        artifactUrl: '/artifacts/backend-api-v1.5.0.jar',
        buildLogUrl: 'buildlogs/2025-06-27/backend_prod_1729494413_345.log'
      },
      { 
        id: 1001, 
        repository: 'https://github.com/example/repo6.git', 
        branch: 'main',
        commit: 'abc123def456',
        status: 2,
        email: '<EMAIL>',
        startTime: '2025-06-27T04:21:22.000Z',
        endTime: '2025-06-27T04:26:22.000Z',
        analysisAvailable: false,
        buildLang: 'React',
        buildVars: 'REACT_APP_ENV=production',
        artifactName: 'frontend-build-v2.3.1.zip',
        artifactMd5: 'pqr678stu901',
        artifactUrl: '/artifacts/frontend-build-v2.3.1.zip',
        buildLogUrl: 'buildlogs/2025-06-27/frontend_prod_1729491682_678.log'
      },
      { 
        id: 94687, 
        repository: 'https://github.com/example/repo7.git', 
        branch: 'develop',
        commit: 'def456ghi789',
        status: 2,
        email: '<EMAIL>',
        startTime: '1970-01-21T06:28:44.000Z',
        endTime: '1970-01-21T06:28:45.000Z',
        analysisAvailable: false,
        buildLang: 'Vue.js',
        buildVars: 'VUE_APP_MODE=development',
        artifactName: 'vue-app-v1.8.2.tar.gz',
        artifactMd5: 'stu901vwx234',
        artifactUrl: '/artifacts/vue-app-v1.8.2.tar.gz',
        buildLogUrl: 'buildlogs/1970-01-21/vue_dev_0006284_901.log'
      }
    ];
  }
};

const BuildsPage = () => {
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['builds'],
    queryFn: fetchBuilds,
    refetchInterval: 30000, // 30秒自动刷新
    retry: 2,
  });

  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  const handleRefresh = () => {
    refetch();
  };

  if (isError) {
    console.warn('构建记录加载失败:', error);
  }

  return (
    <TooltipProvider>
      <div className="w-full max-w-none px-6 py-6">
        <Card className="shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between pb-4">
            <CardTitle className="text-xl font-semibold text-gray-800">构建记录</CardTitle>
            <div className="flex items-center space-x-2">
              {isError && (
                <span className="text-sm text-amber-600">⚠️ API连接失败，显示模拟数据</span>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center space-x-1"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span>刷新</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <DataTable 
              table={table}
              isLoading={isLoading}
              isError={false} // 即使API失败也显示数据
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
};

export default BuildsPage;