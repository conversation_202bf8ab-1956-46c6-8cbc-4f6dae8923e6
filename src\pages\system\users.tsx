import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/DataTable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  createColumnHelper,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Edit, Trash2 } from 'lucide-react';
import { usersApi, type UserData } from '@/lib/api';

const columnHelper = createColumnHelper<UserData>();

const columns = [
  columnHelper.accessor('id', {
    header: 'ID',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('name', {
    header: '姓名',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('email', {
    header: '邮箱',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('role', {
    header: '角色',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('department', {
    header: '部门',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('active', {
    header: '状态',
    cell: (info) => (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
        info.getValue() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`}>
        {info.getValue() ? '活跃' : '停用'}
      </span>
    ),
  }),
  columnHelper.accessor('lastLogin', {
    header: '最后登录',
    cell: (info) => new Date(info.getValue()).toLocaleString('zh-CN'),
  }),
  columnHelper.accessor((row) => row, {
    id: 'actions',
    header: '操作',
    cell: (_info) => (
      <div className="flex space-x-2">
        <Button variant="ghost" size="sm">
          <Edit className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-red-600">
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  }),
];

// 使用真实API调用
const fetchUsers = async (): Promise<UserData[]> => {
  try {
    return await usersApi.getUsers();
  } catch (error) {
    console.error('获取用户列表失败:', error);
    // 如果API调用失败，返回模拟数据作为备选
    return [
      { 
        id: 1, 
        name: '张三', 
        email: '<EMAIL>',
        role: '管理员',
        department: '研发部',
        active: true,
        lastLogin: '2025-06-27T08:30:00.000Z',
      },
      { 
        id: 2, 
        name: '李四', 
        email: '<EMAIL>',
        role: '开发者',
        department: '研发部',
        active: true,
        lastLogin: '2025-06-26T14:20:00.000Z',
      },
      { 
        id: 3, 
        name: '王五', 
        email: '<EMAIL>',
        role: '运维',
        department: '运维部',
        active: false,
        lastLogin: '2025-05-15T09:45:00.000Z',
      },
      { 
        id: 4, 
        name: '赵六', 
        email: '<EMAIL>',
        role: '开发者',
        department: '测试部',
        active: true,
        lastLogin: '2025-06-24T16:10:00.000Z',
      },
    ];
  }
};

const UsersPage = () => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['users'],
    queryFn: fetchUsers,
  });

  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isError) {
    console.warn('用户列表加载失败:', error);
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <Card>
        <CardHeader className="flex flex-row justify-between items-center">
          <CardTitle>用户管理</CardTitle>
          <div className="flex items-center gap-4">
            {isError && (
              <span className="text-amber-600 text-sm">⚠️ API连接失败，显示模拟数据</span>
            )}
            <Button>添加用户</Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable 
            table={table}
            isLoading={isLoading}
            isError={false} // 即使API失败也显示数据
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default UsersPage;