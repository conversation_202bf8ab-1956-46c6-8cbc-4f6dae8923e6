from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime

# 构建记录相关模式
class BuildRecordBase(BaseModel):
    ItemId: int
    BuildRecordID: int
    BuildTrigger: int
    BuildType: int
    BuildLang: str
    BuildVars: Optional[str] = None
    AppID: int
    ServiceID: int
    Status: int
    Repository: str
    Branch: str
    Commit: str
    StartTime: int
    EndTime: Optional[int] = None
    Email: str
    ArtifactName: Optional[str] = None
    ArtifactMd5: Optional[str] = None
    ArtifactUrl: Optional[str] = None
    PackageID: Optional[int] = None
    BuildLogUrl: Optional[str] = None

class BuildRecordCreate(BuildRecordBase):
    pass

class BuildRecordUpdate(BaseModel):
    Status: Optional[int] = None
    EndTime: Optional[int] = None
    ArtifactName: Optional[str] = None
    ArtifactMd5: Optional[str] = None
    ArtifactUrl: Optional[str] = None

class BuildRecordResponse(BuildRecordBase):
    
    class Config:
        from_attributes = True

# 分析结果相关模式
class BuildAnalysisBase(BaseModel):
    buildRecordId: int
    provider: str
    model: str
    context: str
    analysisResult: str
    analysisStatus: int = 1

class BuildAnalysisCreate(BuildAnalysisBase):
    pass

class BuildAnalysisUpdate(BaseModel):
    analysisStatus: Optional[int] = None
    analysisResult: Optional[str] = None

class BuildAnalysisResponse(BuildAnalysisBase):
    id: int
    createdAt: datetime
    updatedAt: datetime
    buildResult: Optional[BuildRecordResponse] = None
    
    class Config:
        from_attributes = True

# API 响应模式
class BuildsListResponse(BaseModel):
    data: List[BuildRecordResponse]
    total: int
    page: int = 1
    limit: int = 10

class AnalysisListResponse(BaseModel):
    success: bool = True
    data: List[BuildAnalysisResponse]
    total: int

class StandardResponse(BaseModel):
    Ret: int = 0
    Msg: str = "Success"

class AnalysisCreateResponse(BaseModel):
    success: bool = True
    message: str = "分析结果保存成功"
    data: BuildAnalysisResponse

# Webhook 构建结果模式 (兼容原格式)
class WebhookBuildResult(BuildRecordBase):
    pass