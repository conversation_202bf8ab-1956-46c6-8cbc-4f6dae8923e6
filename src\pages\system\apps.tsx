import { useQuery } from '@tanstack/react-query';
import { DataTable } from '@/components/DataTable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  createColumnHelper,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { appsApi, type AppData } from '@/lib/api';

const columnHelper = createColumnHelper<AppData>();

const columns = [
  columnHelper.accessor('id', {
    header: 'ID',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('name', {
    header: '应用名称',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('description', {
    header: '应用描述',
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('status', {
    header: '状态',
    cell: (info) => (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
        info.getValue() === 'active' ? 'bg-green-100 text-green-800' : 
        info.getValue() === 'inactive' ? 'bg-gray-100 text-gray-800' : 
        'bg-yellow-100 text-yellow-800'
      }`}>
        {info.getValue() === 'active' ? '活跃' : 
         info.getValue() === 'inactive' ? '停用' : '测试中'}
      </span>
    ),
  }),
  columnHelper.accessor('createdAt', {
    header: '创建时间',
    cell: (info) => new Date(info.getValue()).toLocaleString('zh-CN'),
  }),
];

// 使用真实API调用
const fetchApps = async (): Promise<AppData[]> => {
  try {
    return await appsApi.getApps();
  } catch (error) {
    console.error('获取应用列表失败:', error);
    // 如果API调用失败，返回模拟数据作为备选
    return [
      { 
        id: 1, 
        name: '运维管理系统', 
        description: '用于管理服务器和应用部署的系统',
        status: 'active',
        createdAt: '2025-06-15T08:30:00.000Z'
      },
      { 
        id: 2, 
        name: '监控平台', 
        description: '服务器和应用性能监控',
        status: 'active',
        createdAt: '2025-06-10T14:20:00.000Z'
      },
      { 
        id: 3, 
        name: '日志分析系统', 
        description: '集中式日志收集和分析',
        status: 'testing',
        createdAt: '2025-06-20T09:45:00.000Z'
      },
    ];
  }
};

const AppsPage = () => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['apps'],
    queryFn: fetchApps,
  });

  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isError) {
    console.warn('应用列表加载失败:', error);
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>应用管理</CardTitle>
          {isError && (
            <span className="text-amber-600 text-sm">⚠️ API连接失败，显示模拟数据</span>
          )}
        </CardHeader>
        <CardContent>
          <DataTable 
            table={table}
            isLoading={isLoading}
            isError={false} // 即使API失败也显示数据
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default AppsPage;