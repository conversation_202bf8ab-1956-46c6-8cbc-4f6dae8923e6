async function testNavigation() {
  console.log('🧪 测试页面导航...\n');
  
  try {
    // 测试首页
    console.log('📄 测试首页...');
    const homeResponse = await fetch('http://localhost:3000/');
    if (homeResponse.ok) {
      console.log('✅ 首页加载成功');
    } else {
      console.log('❌ 首页加载失败');
    }
    
    // 测试构建记录页面
    console.log('📄 测试构建记录页面...');
    const buildsResponse = await fetch('http://localhost:3000/system/builds');
    if (buildsResponse.ok) {
      console.log('✅ 构建记录页面加载成功');
    } else {
      console.log('❌ 构建记录页面加载失败');
    }
    
    // 测试API
    console.log('📄 测试构建记录API...');
    const apiResponse = await fetch('http://localhost:3000/api/builds?page=1&limit=5');
    if (apiResponse.ok) {
      const data = await apiResponse.json();
      console.log('✅ 构建记录API正常');
      console.log(`📊 数据记录: ${data.total} 条`);
    } else {
      console.log('❌ 构建记录API失败');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
  }
}

// 等待服务器启动
setTimeout(() => {
  testNavigation();
}, 5000); 