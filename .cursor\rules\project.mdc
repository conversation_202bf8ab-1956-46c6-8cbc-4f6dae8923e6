---
description: 
globs: 
alwaysApply: false
---
## 项目介绍
 
这是一个使用 Next.js + React + Tailwind CSS 构建的后台管理系统。
负责一些项目内部业务管理，比如构建记录，崩溃记录，agent设置

## 技术栈

- **框架**: Next.js 14 (App Router)
- **前端**: React 18 + TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Radix UI + Shadcn/ui
- **状态管理**: React Query (TanStack Query)
- **图标**: Lucide React


## 项目结构

```
├── app/                  # Next.js App Router 页面
│   ├── globals.css      # 全局样式
│   ├── layout.tsx       # 根布局
│   ├── page.tsx         # 首页
│   └── system/          # 系统管理页面
├── components/          # 可复用组件
│   ├── AdminLayout.tsx  # 管理后台布局
│   ├── Sidebar.tsx      # 侧边栏组件
│   ├── Header.tsx       # 头部组件
│   ├── DataTable.tsx    # 数据表格组件
│   └── ui/              # UI基础组件
├── lib/                 # 工具函数
├── hooks/               # 自定义Hooks
└── providers/           # 全局状态提供者

python-server/          # 后台API服务
├── main.py              # FastAPI应用入口
├── schemas.py           # Pydantic数据验证模型
├── requirements.txt     # Python依赖
├── config.example       # 配置示例
├── test_api.py          # API测试脚本
├── start.bat            # Windows启动脚本
├── alembic.ini          # 数据库迁移配置
├── db/                  # 数据库模块
│   ├── __init__.py      # 数据库包初始化
│   ├── database.py      # 数据库配置和连接
│   └── models.py        # SQLAlchemy数据库模型
├── route/               # 路由模块
│   ├── __init__.py      # 路由包初始化
│   ├── build_routes.py  # 构建记录路由
│   ├── analysis_routes.py # AI分析路由
│   └── webhook_routes.py  # Webhook路由
└── README.md            # 项目说明


```