---
description: Azure管理后台 - 全栈Web应用项目，用于管理构建记录、AI分析和内部业务
globs:
alwaysApply: false
---
## 项目介绍

这是一个名为 **Azure 管理后台** 的全栈Web应用项目，主要用于管理内部业务，包括构建记录、崩溃记录、AI分析结果和agent设置等。

## 技术架构

### 前端技术栈
- **框架**: React 18 + TypeScript + Vite（使用React Router DOM v7进行路由管理）
- **样式**: Tailwind CSS + Tailwind CSS Animate
- **UI组件库**: Radix UI + Shadcn/ui 组件系统
- **状态管理**: TanStack Query (React Query)
- **表单处理**: React Hook Form + Zod验证
- **图标**: Lucide React
- **图表**: Recharts
- **其他组件**:
  - 日期处理：date-fns
  - 轮播组件：Embla Carousel
  - 通知：Sonner
  - 拖拽面板：React Resizable Panels

### 后端技术栈
- **框架**: Python + FastAPI
- **数据库**: MySQL + SQLAlchemy ORM
- **数据验证**: Pydantic
- **数据库迁移**: Alembic

## 核心功能

### 1. 构建记录管理
- 构建记录列表展示和查询（支持分页和筛选）
- 构建状态跟踪（进行中、成功、失败、构建错误）
- 支持多种构建语言（Node.js等）
- Webhook接收构建结果

### 2. AI分析集成
- AI分析结果的上报和查询
- 支持多种AI提供商（如Claude）
- 分析状态跟踪和管理

### 3. 系统管理
- 用户管理和权限控制
- 应用管理
- 服务管理

### 4. UI特性
- 🎨 现代化的深蓝色侧边栏设计
- 📱 响应式布局，支持移动端
- 🔧 可配置的二级菜单系统
- 📊 数据表格展示和管理
- 🔍 搜索和筛选功能

## 项目结构

### 前端结构 (src/)
```
src/
├── components/          # 可复用组件
│   ├── AdminLayout.tsx  # 管理后台布局
│   ├── Sidebar.tsx      # 侧边栏组件
│   ├── Header.tsx       # 头部组件
│   ├── DataTable.tsx    # 数据表格组件
│   └── ui/              # UI基础组件（Shadcn/ui）
├── pages/               # 页面组件
│   ├── home.tsx         # 首页/仪表盘
│   └── system/          # 系统管理页面
│       ├── apps.tsx     # 应用管理
│       ├── builds.tsx   # 构建记录
│       └── users.tsx    # 用户管理
├── lib/                 # 工具函数和API客户端
│   ├── api.ts           # API客户端和接口定义
│   ├── types.ts         # TypeScript类型定义
│   └── utils.ts         # 工具函数
├── hooks/               # 自定义Hooks
├── providers/           # 全局状态提供者
│   ├── react-query-provider.tsx
│   └── theme-provider.tsx
├── styles/              # 样式文件
│   └── globals.css      # 全局样式
└── main.tsx             # 应用入口点
```

### 后端结构 (python-server/)
```
python-server/
├── main.py              # FastAPI应用入口
├── schemas.py           # Pydantic数据验证模型
├── requirements.txt     # Python依赖
├── config.example       # 配置示例
├── test_api.py          # API测试脚本
├── start.bat            # Windows启动脚本
├── alembic.ini          # 数据库迁移配置
├── db/                  # 数据库模块
│   ├── __init__.py      # 数据库包初始化
│   ├── database.py      # 数据库配置和连接
│   └── models.py        # SQLAlchemy数据库模型
├── route/               # 路由模块
│   ├── __init__.py      # 路由包初始化
│   ├── build_routes.py  # 构建记录路由
│   ├── analysis_routes.py # AI分析路由
│   └── webhook_routes.py  # Webhook路由
└── README.md            # 项目说明
```

## API接口

### 主要端点
- `GET /api/builds` - 获取构建记录列表
- `POST /api/webhook/build_result` - 接收构建结果Webhook
- `POST /api/builds/analysis-result` - 上报AI分析结果
- `GET /api/builds/analysis-result` - 查询AI分析记录
- `GET /health` - 健康检查
- `GET /docs` - API文档 (Swagger UI)

## 数据库设计

### 主要数据表
1. **build_records** - 构建记录表（包含构建状态、仓库信息、分支、提交等）
2. **build_analysis** - AI分析结果表（包含分析状态、AI提供商、模型、分析结果等）

## 开发环境

### 前端启动
```bash
npm install
npm run dev  # 运行在 http://localhost:3000
```

### 后端启动
```bash
cd python-server
pip install -r requirements.txt
python main.py  # 运行在 http://localhost:3001
```

## 页面路由

- `/` - 首页/仪表盘
- `/system/users` - 用户管理
- `/system/apps` - 应用管理
- `/system/builds` - 构建记录管理