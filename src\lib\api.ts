// API配置
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3001";

// API客户端
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("API request failed:", error);
      throw error;
    }
  }

  // GET请求
  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "GET" });
  }

  // POST请求
  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // PUT请求
  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }
}

// API客户端实例
export const apiClient = new ApiClient(API_BASE_URL);

// 构建记录相关API
export interface BuildData {
  BuildRecordID: number;
  ItemId: number;
  BuildTrigger: number;
  BuildType: number;
  BuildLang: string;
  BuildVars: string;
  AppID: number;
  ServiceID: number;
  Status: number;
  Repository: string;
  Branch: string;
  Commit: string;
  StartTime: number;
  EndTime: number;
  Email: string;
  ArtifactName: string;
  ArtifactMd5: string;
  ArtifactUrl: string;
  BuildLogUrl?: string;
  PackageID: number;
  analysisAvailable?: boolean;
}

export interface BuildsResponse {
  data: BuildData[];
  total: number;
  page: number;
  limit: number;
}

export interface AnalysisResult {
  buildRecordId: number;
  context: string;
  analysisResult: string;
  analysisStatus: number;
}

export interface AIAnalysisResponse {
  status?: string;
  message?: string;
  analysis?: string;
  content?: string;
  analysisId?: string;
  [key: string]: any; // 允许其他字段
}

// 构建记录API
export const buildsApi = {
  // 获取构建记录列表
  getBuilds: (params?: {
    page?: number;
    limit?: number;
  }): Promise<BuildsResponse> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());

    const endpoint = `/api/builds${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;
    return apiClient.get<BuildsResponse>(endpoint);
  },

  // 上报分析结果
  submitAnalysisResult: (
    data: AnalysisResult
  ): Promise<{ Ret: number; Msg: string }> => {
    return apiClient.post<{ Ret: number; Msg: string }>(
      "/api/builds/analysis-result",
      data
    );
  },

  // Webhook接收构建结果
  receiveBuildResult: (
    data: BuildData
  ): Promise<{ Ret: number; Msg: string }> => {
    return apiClient.post<{ Ret: number; Msg: string }>(
      "/api/webhook/build_result",
      data
    );
  },

  // AI分析请求
  requestAnalysis: (
    buildRecordId: number,
    provider: string
  ): Promise<AIAnalysisResponse> => {
    return apiClient.post<AIAnalysisResponse>("/api/builds/analysis", {
      buildRecordId,
      provider: provider,
    });
  },
};

// 用户相关API
export interface UserData {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  active: boolean;
  lastLogin: string;
}

export const usersApi = {
  getUsers: (): Promise<UserData[]> => {
    return apiClient.get<UserData[]>("/api/users");
  },

  createUser: (data: Omit<UserData, "id">): Promise<UserData> => {
    return apiClient.post<UserData>("/api/users", data);
  },

  updateUser: (id: number, data: Partial<UserData>): Promise<UserData> => {
    return apiClient.put<UserData>(`/api/users/${id}`, data);
  },

  deleteUser: (id: number): Promise<void> => {
    return apiClient.delete<void>(`/api/users/${id}`);
  },
};

// 应用相关API
export interface AppData {
  id: number;
  name: string;
  description: string;
  status: string;
  createdAt: string;
}

export const appsApi = {
  getApps: (): Promise<AppData[]> => {
    return apiClient.get<AppData[]>("/api/apps");
  },

  createApp: (data: Omit<AppData, "id">): Promise<AppData> => {
    return apiClient.post<AppData>("/api/apps", data);
  },

  updateApp: (id: number, data: Partial<AppData>): Promise<AppData> => {
    return apiClient.put<AppData>(`/api/apps/${id}`, data);
  },

  deleteApp: (id: number): Promise<void> => {
    return apiClient.delete<void>(`/api/apps/${id}`);
  },
};
