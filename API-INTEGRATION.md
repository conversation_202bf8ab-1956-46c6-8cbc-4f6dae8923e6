# API 集成说明

## 📍 HTTP请求接口位置

原来的HTTP请求接口已经重构到以下位置：

```
src/lib/api.ts - 统一的API服务层
```

## 🏗️ 架构设计

### API服务层结构
```
src/lib/api.ts
├── ApiClient 类        # 通用HTTP客户端
├── buildsApi           # 构建记录相关API
├── usersApi           # 用户管理相关API
└── appsApi            # 应用管理相关API
```

### 环境配置
```typescript
// vite-env.d.ts - 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
}
```

## 🔧 使用方式

### 1. 基础配置

创建 `.env` 文件配置API地址：
```env
VITE_API_BASE_URL=http://localhost:3001
```

### 2. API调用示例

#### 构建记录API
```typescript
import { buildsApi } from '@/lib/api';

// 获取构建记录
const builds = await buildsApi.getBuilds({ page: 1, limit: 50 });

// 提交分析结果
await buildsApi.submitAnalysisResult({
  buildRecordId: 12345,
  context: '构建失败原因...',
  analysisResult: 'AI分析结果...',
  analysisStatus: 2
});
```

#### 用户管理API
```typescript
import { usersApi } from '@/lib/api';

// 获取用户列表
const users = await usersApi.getUsers();

// 创建用户
const newUser = await usersApi.createUser({
  name: 'John Doe',
  email: '<EMAIL>',
  role: 'developer',
  department: '研发部',
  active: true,
  lastLogin: new Date().toISOString()
});
```

### 3. 页面组件集成

所有页面组件已更新为使用真实API：

- `src/pages/system/builds.tsx` - 构建记录页面
- `src/pages/system/users.tsx` - 用户管理页面  
- `src/pages/system/apps.tsx` - 应用管理页面

## 🔄 容错机制

### API失败处理
当API调用失败时，系统会：

1. **显示错误提示**：页面顶部显示"⚠️ API连接失败，显示模拟数据"
2. **降级到模拟数据**：确保用户界面仍然可用
3. **记录错误日志**：在控制台输出详细错误信息

### 自动重试
```typescript
useQuery({
  queryKey: ['builds'],
  queryFn: fetchBuilds,
  refetchInterval: 30000, // 30秒自动刷新
  retry: 2,              // 失败时重试2次
});
```

## 🌐 API接口规范

### 原有API端点
基于测试文件分析，原项目包含以下API：

```
GET  /api/builds                    # 获取构建记录列表
POST /api/builds/analysis-result    # 提交分析结果
POST /api/webhook/build_result      # 接收构建结果webhook
GET  /api/users                     # 获取用户列表（推断）
GET  /api/apps                      # 获取应用列表（推断）
```

### 数据格式

#### 构建记录数据结构
```typescript
interface BuildData {
  BuildRecordID: number;
  Repository: string;
  Branch: string;
  Status: number;          // 1:进行中 2:成功 3:失败 4:构建错误
  Email: string;
  StartTime: number;
  EndTime: number;
  // ... 更多字段
}
```

## 🚀 启动开发

1. **启动前端开发服务器**：
```bash
npm run dev
# 访问 http://localhost:3000
```

2. **确保后端API服务运行在**：
```
http://localhost:3001
```

3. **测试API连接**：
页面会自动尝试连接API，失败时显示模拟数据。

## 📝 迁移说明

### 从Next.js API Routes迁移
- ✅ 移除了 `pages/api/` 目录结构
- ✅ 统一到 `src/lib/api.ts` 客户端服务层
- ✅ 支持环境变量配置
- ✅ 完整的TypeScript类型支持
- ✅ 错误处理和重试机制

### 兼容性
- 保持原有API端点不变
- 数据格式向后兼容
- 支持渐进式迁移 