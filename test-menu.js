async function testMenuStructure() {
  console.log('🧪 测试菜单结构...\n');
  
  try {
    const response = await fetch('http://localhost:3001/');
    const html = await response.text();
    
    console.log('📄 页面加载状态:', response.ok ? '✅ 成功' : '❌ 失败');
    
    // 检查关键菜单项
    const menuItems = [
      '项目管理',
      '构建记录', 
      '崩溃记录',
      'Agent管理',
      'LLM管理',
      'Agents'
    ];
    
    console.log('\n🔍 检查菜单项:');
    menuItems.forEach(item => {
      const found = html.includes(item);
      console.log(`${found ? '✅' : '❌'} ${item}`);
    });
    
    // 检查是否有React hydration相关的脚本
    const hasReactScripts = html.includes('_next/static') || html.includes('__NEXT_DATA__');
    console.log(`\n📦 React应用: ${hasReactScripts ? '✅ 已加载' : '❌ 未加载'}`);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testMenuStructure(); 