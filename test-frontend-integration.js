// 测试前端AI分析结果读取功能
async function testFrontendIntegration() {
  const { default: fetch } = await import('node-fetch');
  const baseUrl = 'http://localhost:3000';
  
  console.log('=== 测试前端AI分析结果读取功能 ===\n');

  try {
    // 1. 首先获取一个构建记录
    console.log('1. 获取构建记录列表...');
    const buildsResponse = await fetch(`${baseUrl}/api/builds?page=1&limit=10`);
    const buildsData = await buildsResponse.json();
    
    if (!buildsData.success || buildsData.data.length === 0) {
      console.log('❌ 没有找到构建记录');
      return;
    }
    
    const buildRecord = buildsData.data[0];
    console.log(`✅ 获取到构建记录: ID=${buildRecord.BuildRecordID}, 仓库=${buildRecord.Repository}`);

    // 2. 创建测试用的分析结果
    const testProviders = ['claude', 'gemini', 'openai'];
    
    for (const provider of testProviders) {
      console.log(`\n2. 为构建记录 ${buildRecord.BuildRecordID} 创建 ${provider} 分析结果...`);
      
      const analysisData = {
        buildRecordId: buildRecord.BuildRecordID,
        status: 2, // 分析完成
        context: `测试上下文 for ${provider}`,
        analysisResult: `## ${provider.toUpperCase()} AI 分析结果

### 构建失败原因
这是一个由 ${provider} AI 生成的测试分析结果。

### 问题分析
- 依赖项安装失败
- 编译错误
- 环境配置问题

### 解决建议
1. 检查 package.json 依赖版本
2. 更新 Node.js 版本
3. 清理缓存重新安装

---
分析时间: ${new Date().toLocaleString()}
分析模型: ${provider}-3.5`,
        provider: provider,
        model: `${provider}-3.5`
      };

      const saveResponse = await fetch(`${baseUrl}/api/builds/analysis-result`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analysisData),
      });

      const saveResult = await saveResponse.json();
      
      if (saveResult.success) {
        console.log(`✅ ${provider} 分析结果保存成功，ID: ${saveResult.data.id}`);
      } else {
        console.log(`❌ ${provider} 分析结果保存失败:`, saveResult.error);
      }
    }

    // 3. 测试获取分析结果API（前端会调用这个）
    console.log(`\n3. 测试获取构建记录 ${buildRecord.BuildRecordID} 的分析结果...`);
    
    const queryResponse = await fetch(`${baseUrl}/api/builds/analysis-result?buildRecordId=${buildRecord.BuildRecordID}`);
    const queryResult = await queryResponse.json();
    
    if (queryResult.success) {
      console.log(`✅ 成功获取分析结果，共 ${queryResult.data.length} 条记录:`);
      
      queryResult.data.forEach((record, index) => {
        console.log(`\n记录 ${index + 1}:`);
        console.log(`  - ID: ${record.id}`);
        console.log(`  - Provider: ${record.provider}`);
        console.log(`  - Model: ${record.model}`);
        console.log(`  - Status: ${record.status === 1 ? '分析中' : '分析完成'}`);
        console.log(`  - 创建时间: ${new Date(record.createdAt).toLocaleString()}`);
        console.log(`  - 分析结果长度: ${record.analysisResult.length} 字符`);
      });
    } else {
      console.log('❌ 获取分析结果失败:', queryResult.error);
    }

    // 4. 模拟前端按provider筛选的逻辑
    console.log('\n4. 模拟前端筛选逻辑...');
    
    if (queryResult.success && queryResult.data.length > 0) {
      testProviders.forEach(provider => {
        const record = queryResult.data.find(r => r.provider === provider);
        if (record) {
          console.log(`✅ 找到 ${provider} 的分析结果: ${record.analysisResult.substring(0, 50)}...`);
        } else {
          console.log(`❌ 未找到 ${provider} 的分析结果`);
        }
      });
    }

    console.log('\n=== 前端集成测试完成 ===');
    console.log('\n💡 前端页面使用说明:');
    console.log('1. 打开 http://localhost:3000/system/builds');
    console.log('2. 点击任意构建记录的"AI分析"按钮');
    console.log('3. 在弹出窗口中切换到 Claude/Gemini/OpenAI 标签页');
    console.log('4. 页面会自动加载已有的分析结果');
    console.log('5. 如果没有结果，可以点击"开始分析"按钮');
    console.log('6. 可以点击"刷新结果"按钮重新获取最新数据');

  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
  }
}

// 执行测试
testFrontendIntegration(); 