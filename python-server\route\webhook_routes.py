from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from db.database import get_db
from db.models import BuildRecord
from schemas import WebhookBuildResult, StandardResponse

router = APIRouter(prefix="/api/webhook", tags=["Webhook"])

@router.post("/build_result", response_model=StandardResponse)
async def receive_build_result(
    build_data: WebhookBuildResult,
    db: Session = Depends(get_db)
):
    """接收构建结果 Webhook"""
    try:
        # 检查构建记录是否已存在
        existing_build = db.query(BuildRecord)\
                          .filter(BuildRecord.BuildRecordID == build_data.BuildRecordID)\
                          .first()
        
        if existing_build:
            # 更新现有记录
            for field, value in build_data.dict().items():
                if hasattr(existing_build, field):
                    setattr(existing_build, field, value)
        else:
            # 创建新记录
            new_build = BuildRecord(**build_data.dict())
            db.add(new_build)
        
        db.commit()
        
        return StandardResponse(Ret=0, Msg="构建结果接收成功")
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"保存构建结果失败: {str(e)}") 