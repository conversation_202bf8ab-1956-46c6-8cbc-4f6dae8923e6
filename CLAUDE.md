# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint

# Type check TypeScript
npm run type-check
```

## Project Architecture

### Technology Stack
- **Frontend Framework**: React 18 + TypeScript + Vite
- **Routing**: React Router Dom v7
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI + Shadcn/UI components
- **State Management**: TanStack Query (React Query)
- **Data Visualization**: Recharts
- **Form Handling**: React Hook Form + Zod validation

### Core Structure

The project is a React admin panel UI for Azure services, recently migrated from Next.js to Vite. The application structure follows a component-based architecture:

1. **Components Layer**:
   - `AdminLayout.tsx`: Main layout component with sidebar and header
   - `Sidebar.tsx`: Navigation sidebar with collapsible menu items
   - `Header.tsx`: Top navigation header
   - `DataTable.tsx`: Reusable data table component using TanStack Table
   - `ui/`: Shadcn UI components library (buttons, forms, cards, etc.)

2. **Routing System**:
   - Uses React Router v7
   - Route configuration is defined in `main.tsx`
   - Main routes follow the pattern `/system/*`, `/tools/*`, etc.

3. **State Management**:
   - React Query for server state management with a default stale time of 60 seconds
   - React's Context API for theme management

4. **API Integration**:
   - The project includes API endpoints for build management, analysis, and logs
   - API routes are documented in API-USAGE.md

### UI Organization

The UI follows a standard admin panel pattern:
- Collapsible sidebar for navigation
- Main content area with route-specific components
- Data tables for displaying records like build information
- Modal dialogs for actions and forms

### Build Analysis Feature

The application includes a BuildAnalysis API for AI-powered analysis of build results:
- Supports multiple AI providers (Claude, Gemini, OpenAI)
- Stores analysis results in a database
- Provides endpoints for uploading and querying analysis results


## 回答要求
### 始终用中文回答问题

## 任务
我们目前正在重构整个项目，这个项目原来是使用Next.js + React + Tailwind CSS 构建的后台管理系统。 我现在不想使用   Next.js 作为全栈后台技术了。我正在移除 Next.js ，前端仅保持React + Tailwind CSS 纯前端技术栈，后台使用python fastapi 开发接口。
重构分为两个步骤：
第一步： 剥离Next.js技术栈，转为vite+React + Tailwind CSS 纯前端技术栈
第二步： 提取当前Next.js后台接口，使用python fastapi技术栈开发后台接口
第三步：前端接入后台接口，联调测试
========
目前第一步已经进行到一半了，请你帮我继续完成第一步任务，直到编译通过，能够打开前端界面
