#!/usr/bin/env python3
"""
日志代理接口测试脚本
"""

import requests
import json
import time

BASE_URL = "http://localhost:3001"

def test_build_logs_api():
    """测试构建日志代理接口"""
    print("🧪 测试构建日志代理接口...")
    
    # 测试数据 - 使用一个示例日志路径
    test_params = {
        "buildRecordId": 94233,
        "path": "buildlogs/2025-06-27/socgame_prod_1729495265_473.log"
    }
    
    try:
        print(f"📡 请求参数: {test_params}")
        
        response = requests.get(
            f"{BASE_URL}/api/builds/logs",
            params=test_params,
            timeout=15  # 15秒超时
        )
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 日志代理接口测试成功!")
            
            # 显示结果摘要
            print(f"🔍 构建ID: {result.get('buildRecordId')}")
            print(f"🌐 日志URL: {result.get('logUrl')}")
            print(f"📄 总行数: {result.get('totalLines', 0)}")
            print(f"❌ 错误行数: {result.get('errorLinesCount', 0)}")
            print(f"📝 内容长度: {len(result.get('content', '')) if result.get('content') else 0} 字符")
            
            # 显示前5行处理后的日志
            processed_lines = result.get('processedLines', [])
            if processed_lines:
                print("\n📋 前5行日志预览:")
                for i, line in enumerate(processed_lines[:5]):
                    error_flag = "❌" if line.get('isError') else "  "
                    print(f"  {error_flag} {line.get('lineNumber', 0):3d}: {line.get('content', '')[:80]}...")
            
            # 显示错误行（如果有）
            error_lines = [line for line in processed_lines if line.get('isError')]
            if error_lines:
                print(f"\n🚨 发现 {len(error_lines)} 行错误:")
                for line in error_lines[:3]:  # 最多显示3行
                    print(f"  ❌ {line.get('lineNumber', 0):3d}: {line.get('content', '')[:100]}...")
                if len(error_lines) > 3:
                    print(f"  ... 还有 {len(error_lines) - 3} 行错误")
            
            return True
            
        else:
            error_info = response.json() if response.headers.get('content-type') == 'application/json' else response.text
            print(f"❌ 日志代理接口测试失败: {error_info}")
            return False
            
    except requests.exceptions.Timeout:
        print("⏰ 请求超时 - 日志服务可能响应较慢")
        return False
    except requests.exceptions.ConnectionError:
        print("🌐 连接错误 - 请确保服务器已启动")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_logs_api_with_invalid_params():
    """测试无效参数的情况"""
    print("\n🧪 测试无效参数...")
    
    test_cases = [
        # 缺少buildRecordId
        {"path": "some/log/path.log"},
        # 缺少path
        {"buildRecordId": 12345},
        # 空参数
        {},
    ]
    
    for i, params in enumerate(test_cases):
        print(f"  测试用例 {i+1}: {params}")
        try:
            response = requests.get(f"{BASE_URL}/api/builds/logs", params=params, timeout=5)
            if response.status_code == 422:  # FastAPI validation error
                print("  ✅ 正确返回参数验证错误")
            else:
                print(f"  ⚠️ 状态码: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 异常: {e}")

def test_health_check():
    """测试服务器健康状态"""
    print("🧪 测试服务器健康状态...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务器健康: {result}")
            return True
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始日志代理接口测试...")
    print("=" * 60)
    
    # 检查服务器状态
    if not test_health_check():
        print("❌ 服务器不可用，终止测试")
        return
    
    print()
    
    # 测试正常情况
    success = test_build_logs_api()
    
    # 测试异常情况
    test_logs_api_with_invalid_params()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！日志代理接口工作正常")
    else:
        print("⚠️ 测试完成，但可能存在问题")

if __name__ == "__main__":
    main() 